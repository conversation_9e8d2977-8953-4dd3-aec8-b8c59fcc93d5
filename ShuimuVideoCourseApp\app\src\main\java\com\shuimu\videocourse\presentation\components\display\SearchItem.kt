package com.shuimu.videocourse.presentation.components.display

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.basic.BadgeComponent
import com.shuimu.videocourse.presentation.components.basic.BadgeStyle
import com.shuimu.videocourse.presentation.components.basic.LinearProgressBar
import com.shuimu.videocourse.presentation.components.basic.ProgressBarSize
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 搜索结果项组件
 * 
 * 基于原型设计：UI Prototype/data/component-templates-with-styles.js 第600-650行
 * createPurchasedSearchItem 和 createUnpurchasedSearchItem 函数
 * 
 * 符合度优化：
 * - 增强搜索高亮算法，支持多关键词匹配
 * - 优化悬停动画效果，完全匹配原型
 * - 改进缓存状态显示，支持更多状态类型
 * - 完善移动端触摸反馈体验
 * 
 * 支持功能：
 * - 已购买状态：显示进度条、观看次数徽章、缓存状态
 * - 未购买状态：显示锁定图标
 * - 智能高亮匹配：支持多关键词、忽略大小写、部分匹配
 * - 分类标签显示
 * - 搜索类型标识
 * - 悬停动画效果（完全匹配原型）
 * - 移动端触摸反馈
 */
@Composable
fun SearchItem(
    title: String,
    category: String,
    progress: Float = 0f, // 0-100
    watchCount: Int = 0,
    cacheStatus: String = "", // 空字符串表示未缓存，"已缓存"表示已缓存，"50%"表示下载中
    isPurchased: Boolean = true,
    searchQuery: String = "", // 搜索关键词，用于高亮显示
    dataTitle: String = title, // 用于搜索数据标识
    dataCategory: String = category, // 用于搜索数据标识
    dataIndex: Int = 0, // 搜索结果索引
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    var isHovered by remember { mutableStateOf(false) }
    var isPressed by remember { mutableStateOf(false) }
    
    // 悬停动画 - 完全匹配原型
    val hoverScale by animateFloatAsState(
        targetValue = if (isHovered || isPressed) 1.02f else 1f,
        animationSpec = tween(200, easing = EaseOutCubic), // 原型：0.2s ease
        label = "hoverScale"
    )
    
    val hoverTranslationX by animateFloatAsState(
        targetValue = if (isHovered || isPressed) 2f else 0f, // 原型：translateX(2px)
        animationSpec = tween(200, easing = EaseOutCubic),
        label = "hoverTranslationX"
    )
    
    // 播放图标动画 - 原型：scale(1.1)
    val playIconScale by animateFloatAsState(
        targetValue = if (isHovered || isPressed) 1.1f else 1f,
        animationSpec = tween(200, easing = EaseOutCubic),
        label = "playIconScale"
    )

    // 背景色和边框 - 原型：background-color: #f9fafb; border-left: 3px solid #3b82f6
    val backgroundColor = if (isHovered || isPressed) {
        Color(0xFFF9FAFB) // 原型：#f9fafb
    } else {
        Color.Transparent
    }
    
    val leftBorderWidth = if (isHovered || isPressed) 3.dp else 0.dp
    val leftBorderColor = Color(0xFF3B82F6) // 原型：#3b82f6

    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .drawBehind {
                // 绘制左边框
                if (leftBorderWidth > 0.dp) {
                    drawLine(
                        color = leftBorderColor,
                        start = Offset(0f, 0f),
                        end = Offset(0f, size.height),
                        strokeWidth = leftBorderWidth.toPx()
                    )
                }
            }
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null // 使用自定义反馈效果
            ) { 
                onClick() 
            }
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    }
                )
            }
            .padding(
                start = if (isHovered || isPressed) 8.dp else 6.dp, // 原型：padding-left: 8px
                end = 6.dp,
                top = 10.dp, // 原型：padding: 10px 6px
                bottom = 10.dp
            )
            .offset(x = hoverTranslationX.dp)
            .scale(hoverScale),
        verticalAlignment = Alignment.Top, // 原型：align-items: flex-start
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 播放图标 - 原型样式完全匹配
        Box(
            modifier = Modifier
                .size(20.dp)
                .offset(y = 2.dp) // 原型：margin-top: 2px 与第一行文字对齐
                .clip(CircleShape)
                .background(
                    if (isHovered || isPressed) Color(0xFF1D4ED8) else Color(0xFF3B82F6) // 原型：#1d4ed8
                )
                .scale(playIconScale),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "播放",
                tint = Color.White,
                modifier = Modifier.size(8.dp)
            )
        }

        // 视频内容区域 - 原型：search-suggestion-content
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(2.dp) // 原型：gap: 2px
        ) {
            // 标题行（带智能高亮）
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 智能高亮标题
                Text(
                    text = buildSmartHighlightedText(title, searchQuery),
                    fontSize = 14.sp, // 原型：font-size: 14px
                    lineHeight = 18.sp, // 原型：line-height: 1.3
                    color = if (isHovered || isPressed) Color(0xFF2563EB) else Color(0xFF374151), // 原型：#2563eb, #374151
                    fontWeight = FontWeight.Medium, // 原型：font-weight: 500
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                // 缓存状态指示器
                if (cacheStatus.isNotEmpty()) {
                    EnhancedCacheStatusIndicator(
                        status = cacheStatus,
                        modifier = Modifier
                    )
                }
            }

            // 进度行（仅已购买状态显示）
            if (isPurchased) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(0.dp),
                    modifier = Modifier.offset(y = (-6).dp) // 原型：margin-top: -6px
                ) {
                    // 进度条
                    LinearProgressBar(
                        progress = progress / 100f,
                        size = ProgressBarSize.SMALL,
                        animated = progress > 0,
                        modifier = Modifier.weight(1f)
                    )
                    
                    // 进度信息
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(2.dp),
                        modifier = Modifier
                            .padding(end = 10.dp)
                            .widthIn(min = 80.dp)
                    ) {
                        // 进度百分比
                        Text(
                            text = "${progress.toInt()}%",
                            fontSize = 12.sp,
                            color = Color(0xFF6B7280),
                            fontWeight = FontWeight.Normal
                        )
                        
                        // 观看次数徽章
                        BadgeComponent(
                            count = watchCount,
                            style = BadgeStyle.ORIGINAL
                        )
                    }
                }
            }
            
            // 分类标签 - 原型：search-suggestion-category
            Text(
                text = category,
                fontSize = 12.sp, // 原型：font-size: 12px
                color = Color(0xFF6B7280), // 原型：#6b7280
                fontWeight = FontWeight.Normal,
                lineHeight = 14.sp, // 原型：line-height: 1.2
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.padding(top = 5.dp) // 原型：margin-top: 5px
            )
        }

        // 锁定图标（仅未购买状态显示）
        if (!isPurchased) {
            Icon(
                imageVector = Icons.Default.Lock,
                contentDescription = "未购买",
                tint = Color(0xFFF59E0B), // #F59E0B
                modifier = Modifier
                    .size(12.dp)
                    .padding(end = 10.dp)
                    .offset(y = 2.dp) // 与标题对齐
            )
        }
    }
}

/**
 * 构建智能高亮文本
 * 支持多关键词匹配、忽略大小写、部分匹配
 */
@Composable
private fun buildSmartHighlightedText(
    text: String,
    query: String
) = buildAnnotatedString {
    if (query.isEmpty()) {
        append(text)
        return@buildAnnotatedString
    }
    
    // 分割搜索关键词，支持空格分隔的多关键词
    val keywords = query.trim().split("\\s+".toRegex()).filter { it.isNotEmpty() }
    if (keywords.isEmpty()) {
        append(text)
        return@buildAnnotatedString
    }
    
    // 找到所有匹配位置
    val matches = mutableListOf<Pair<Int, Int>>() // (startIndex, endIndex)
    
    keywords.forEach { keyword ->
        var startIndex = 0
        while (startIndex < text.length) {
            val foundIndex = text.indexOf(keyword, startIndex, ignoreCase = true)
            if (foundIndex >= 0) {
                val endIndex = foundIndex + keyword.length
                // 检查是否与已有匹配重叠
                val overlapping = matches.any { (start, end) ->
                    foundIndex < end && endIndex > start
                }
                if (!overlapping) {
                    matches.add(Pair(foundIndex, endIndex))
                }
                startIndex = foundIndex + 1
            } else {
                break
            }
        }
    }
    
    // 按位置排序
    matches.sortBy { it.first }
    
    if (matches.isEmpty()) {
        append(text)
        return@buildAnnotatedString
    }
    
    // 构建高亮文本
    var currentIndex = 0
    matches.forEach { (start, end) ->
        // 添加高亮前的文本
        if (currentIndex < start) {
            append(text.substring(currentIndex, start))
        }
        
        // 添加高亮的搜索词
        withStyle(
            style = SpanStyle(
                color = Color(0xFF2563EB), // 蓝色高亮
                fontWeight = FontWeight.Bold,
                background = Color(0xFFDEF7FF) // 浅蓝色背景
            )
        ) {
            append(text.substring(start, end))
        }
        
        currentIndex = end
    }
    
    // 添加剩余文本
    if (currentIndex < text.length) {
        append(text.substring(currentIndex))
    }
}

/**
 * 增强的缓存状态指示器
 * 支持更多状态类型和更好的视觉效果
 */
@Composable
private fun EnhancedCacheStatusIndicator(
    status: String,
    modifier: Modifier = Modifier
) {
    val (statusText, statusColor, backgroundColor) = when {
        status.contains("%") -> {
            // 下载中状态
            Triple(status, Color(0xFF3B82F6), Color(0xFF3B82F6).copy(alpha = 0.1f)) // 蓝色
        }
        status == "已缓存" -> {
            // 已缓存状态
            Triple("已缓存", Color(0xFF10B981), Color(0xFF10B981).copy(alpha = 0.1f)) // 绿色
        }
        status.contains("缓存") -> {
            // 其他缓存相关状态
            Triple(status, Color(0xFF10B981), Color(0xFF10B981).copy(alpha = 0.1f)) // 绿色
        }
        status.contains("下载") -> {
            // 下载相关状态
            Triple(status, Color(0xFF3B82F6), Color(0xFF3B82F6).copy(alpha = 0.1f)) // 蓝色
        }
        status.contains("失败") || status.contains("错误") -> {
            // 错误状态
            Triple(status, Color(0xFFEF4444), Color(0xFFEF4444).copy(alpha = 0.1f)) // 红色
        }
        else -> {
            // 默认状态
            Triple(status, Color(0xFF6B7280), Color(0xFF6B7280).copy(alpha = 0.1f)) // 灰色
        }
    }
    
    Text(
        text = statusText,
        fontSize = 10.sp,
        color = statusColor,
        fontWeight = FontWeight.Medium,
        modifier = modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(4.dp)
            )
            .padding(horizontal = 4.dp, vertical = 2.dp)
    )
}

/**
 * 搜索项预览组合
 */
@Composable
fun SearchItemPreview() {
    ShuimuTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 已购买状态 - 有进度 - 高亮搜索
            SearchItem(
                title = "Kotlin基础语法详解",
                category = "Kotlin入门",
                progress = 75f,
                watchCount = 12,
                cacheStatus = "已缓存",
                isPurchased = true,
                searchQuery = "Kotlin",
                dataTitle = "Kotlin基础语法详解",
                dataCategory = "Kotlin入门",
                dataIndex = 0
            )
            
            // 已购买状态 - 下载中
            SearchItem(
                title = "Android Jetpack Compose实战",
                category = "Android开发",
                progress = 45f,
                watchCount = 8,
                cacheStatus = "50%",
                isPurchased = true,
                searchQuery = "Compose",
                dataTitle = "Android Jetpack Compose实战",
                dataCategory = "Android开发",
                dataIndex = 1
            )
            
            // 未购买状态
            SearchItem(
                title = "高级架构设计模式",
                category = "架构设计",
                isPurchased = false,
                searchQuery = "架构",
                dataTitle = "高级架构设计模式",
                dataCategory = "架构设计",
                dataIndex = 2
            )
            
            // 无搜索关键词
            SearchItem(
                title = "Flutter跨平台开发",
                category = "移动开发",
                progress = 30f,
                watchCount = 5,
                isPurchased = true,
                searchQuery = "",
                dataTitle = "Flutter跨平台开发",
                dataCategory = "移动开发",
                dataIndex = 3
            )
        }
    }
} 