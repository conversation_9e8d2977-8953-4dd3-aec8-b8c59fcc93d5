{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1387,1470", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1382,1465,1587"}, "to": {"startLines": "61,62,95,96,98,151,152,153,154,155,156,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4961,5054,8781,8879,9064,13077,13154,13245,13332,13416,13486,13555,13641,13897,14266,14346,14429", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "5049,5133,8874,8979,9154,13149,13240,13327,13411,13481,13550,13636,13724,13967,14341,14424,14546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,399,513,591,681,789,917,1029,1169,1249,1344,1436,1531,1652,1766,1866,2001,2132,2267,2459,2585,2699,2822,2946,3039,3136,3254,3379,3473,3572,3675,3808,3952,4057,4156,4236,4314,4398,4484,4591,4674,4757,4853,4958,5050,5145,5229,5336,5428,5523,5657,5737,5836", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "165,281,394,508,586,676,784,912,1024,1164,1244,1339,1431,1526,1647,1761,1861,1996,2127,2262,2454,2580,2694,2817,2941,3034,3131,3249,3374,3468,3567,3670,3803,3947,4052,4151,4231,4309,4393,4479,4586,4669,4752,4848,4953,5045,5140,5224,5331,5423,5518,5652,5732,5831,5924"}, "to": {"startLines": "50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,97,150,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3752,3867,3983,4096,5138,5216,5306,5414,5542,5654,5794,5874,5969,6061,6156,6277,6391,6491,6626,6757,6892,7084,7210,7324,7447,7571,7664,7761,7879,8004,8098,8197,8300,8433,8577,8682,8984,12999,13729,13972,14159,14551,14634,14717,14813,14918,15010,15105,15189,15296,15388,15483,15617,15697,15796", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "3862,3978,4091,4205,5211,5301,5409,5537,5649,5789,5869,5964,6056,6151,6272,6386,6486,6621,6752,6887,7079,7205,7319,7442,7566,7659,7756,7874,7999,8093,8192,8295,8428,8572,8677,8776,9059,13072,13808,14053,14261,14629,14712,14808,14913,15005,15100,15184,15291,15383,15478,15612,15692,15791,15884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,267,335,416,490,587,682", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "131,195,262,330,411,485,582,677,752"}, "to": {"startLines": "123,124,125,126,127,128,129,130,131", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11134,11215,11279,11346,11414,11495,11569,11666,11761", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "11210,11274,11341,11409,11490,11564,11661,11756,11831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "54,55,56,57,58,59,60,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4210,4308,4418,4517,4620,4731,4841,14058", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "4303,4413,4512,4615,4726,4836,4956,14154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,298,622,933,1017,1100,1178,1275,1372,1446,1510,1606,1702,1773,1838,1901,1974,2082,2192,2300,2372,2448,2521,2595,2684,2772,2841,2908,2961,3019,3074,3135,3201,3270,3335,3403,3467,3525,3598,3665,3739,3798,3861,3938,4015", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,107,109,107,71,75,72,73,88,87,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "293,617,928,1012,1095,1173,1270,1367,1441,1505,1601,1697,1768,1833,1896,1969,2077,2187,2295,2367,2443,2516,2590,2679,2767,2836,2903,2956,3014,3069,3130,3196,3265,3330,3398,3462,3520,3593,3660,3734,3793,3856,3933,4010,4066"}, "to": {"startLines": "2,11,17,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,348,672,9159,9243,9326,9404,9501,9598,9672,9736,9832,9928,9999,10064,10127,10200,10308,10418,10526,10598,10674,10747,10821,10910,10998,11067,11836,11889,11947,12002,12063,12129,12198,12263,12331,12395,12453,12526,12593,12667,12726,12789,12866,12943", "endLines": "10,16,22,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,107,109,107,71,75,72,73,88,87,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "343,667,978,9238,9321,9399,9496,9593,9667,9731,9827,9923,9994,10059,10122,10195,10303,10413,10521,10593,10669,10742,10816,10905,10993,11062,11129,11884,11942,11997,12058,12124,12193,12258,12326,12390,12448,12521,12588,12662,12721,12784,12861,12938,12994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "983,1099,1203,1316,1403,1505,1627,1710,1790,1884,1980,2077,2173,2276,2372,2470,2566,2660,2754,2837,2946,3054,3154,3264,3369,3475,3651,13813", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "1094,1198,1311,1398,1500,1622,1705,1785,1879,1975,2072,2168,2271,2367,2465,2561,2655,2749,2832,2941,3049,3149,3259,3364,3470,3646,3747,13892"}}]}]}