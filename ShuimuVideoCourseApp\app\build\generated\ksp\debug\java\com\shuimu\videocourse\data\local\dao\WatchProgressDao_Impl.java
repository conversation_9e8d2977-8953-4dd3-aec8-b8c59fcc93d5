package com.shuimu.videocourse.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.shuimu.videocourse.data.local.entity.WatchProgressEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class WatchProgressDao_Impl implements WatchProgressDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<WatchProgressEntity> __insertionAdapterOfWatchProgressEntity;

  private final EntityDeletionOrUpdateAdapter<WatchProgressEntity> __deletionAdapterOfWatchProgressEntity;

  private final EntityDeletionOrUpdateAdapter<WatchProgressEntity> __updateAdapterOfWatchProgressEntity;

  private final SharedSQLiteStatement __preparedStmtOfUpdateWatchProgress;

  private final SharedSQLiteStatement __preparedStmtOfMarkVideoCompleted;

  private final SharedSQLiteStatement __preparedStmtOfClearUserProgress;

  private final SharedSQLiteStatement __preparedStmtOfClearCourseProgress;

  private final SharedSQLiteStatement __preparedStmtOfClearAllProgress;

  public WatchProgressDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfWatchProgressEntity = new EntityInsertionAdapter<WatchProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `watch_progress` (`progressId`,`userId`,`videoId`,`courseId`,`currentPosition`,`totalDuration`,`watchedDuration`,`progressPercent`,`isCompleted`,`watchCount`,`lastWatchTime`,`firstWatchTime`,`createTime`,`updateTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WatchProgressEntity entity) {
        statement.bindString(1, entity.getProgressId());
        statement.bindString(2, entity.getUserId());
        statement.bindString(3, entity.getVideoId());
        statement.bindString(4, entity.getCourseId());
        statement.bindLong(5, entity.getCurrentPosition());
        statement.bindLong(6, entity.getTotalDuration());
        statement.bindLong(7, entity.getWatchedDuration());
        statement.bindDouble(8, entity.getProgressPercent());
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(9, _tmp);
        statement.bindLong(10, entity.getWatchCount());
        statement.bindLong(11, entity.getLastWatchTime());
        statement.bindLong(12, entity.getFirstWatchTime());
        statement.bindLong(13, entity.getCreateTime());
        statement.bindLong(14, entity.getUpdateTime());
      }
    };
    this.__deletionAdapterOfWatchProgressEntity = new EntityDeletionOrUpdateAdapter<WatchProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `watch_progress` WHERE `progressId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WatchProgressEntity entity) {
        statement.bindString(1, entity.getProgressId());
      }
    };
    this.__updateAdapterOfWatchProgressEntity = new EntityDeletionOrUpdateAdapter<WatchProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `watch_progress` SET `progressId` = ?,`userId` = ?,`videoId` = ?,`courseId` = ?,`currentPosition` = ?,`totalDuration` = ?,`watchedDuration` = ?,`progressPercent` = ?,`isCompleted` = ?,`watchCount` = ?,`lastWatchTime` = ?,`firstWatchTime` = ?,`createTime` = ?,`updateTime` = ? WHERE `progressId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WatchProgressEntity entity) {
        statement.bindString(1, entity.getProgressId());
        statement.bindString(2, entity.getUserId());
        statement.bindString(3, entity.getVideoId());
        statement.bindString(4, entity.getCourseId());
        statement.bindLong(5, entity.getCurrentPosition());
        statement.bindLong(6, entity.getTotalDuration());
        statement.bindLong(7, entity.getWatchedDuration());
        statement.bindDouble(8, entity.getProgressPercent());
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(9, _tmp);
        statement.bindLong(10, entity.getWatchCount());
        statement.bindLong(11, entity.getLastWatchTime());
        statement.bindLong(12, entity.getFirstWatchTime());
        statement.bindLong(13, entity.getCreateTime());
        statement.bindLong(14, entity.getUpdateTime());
        statement.bindString(15, entity.getProgressId());
      }
    };
    this.__preparedStmtOfUpdateWatchProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE watch_progress SET \n"
                + "        currentPosition = ?,\n"
                + "        watchedDuration = ?,\n"
                + "        progressPercent = ?,\n"
                + "        isCompleted = ?,\n"
                + "        watchCount = watchCount + 1,\n"
                + "        lastWatchTime = ?,\n"
                + "        updateTime = ?\n"
                + "        WHERE userId = ? AND videoId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfMarkVideoCompleted = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE watch_progress SET \n"
                + "        isCompleted = 1,\n"
                + "        progressPercent = 100.0,\n"
                + "        lastWatchTime = ?,\n"
                + "        updateTime = ?\n"
                + "        WHERE userId = ? AND videoId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfClearUserProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM watch_progress WHERE userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearCourseProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM watch_progress WHERE courseId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAllProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM watch_progress";
        return _query;
      }
    };
  }

  @Override
  public Object insertProgress(final WatchProgressEntity progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfWatchProgressEntity.insert(progress);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertProgresses(final List<WatchProgressEntity> progresses,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfWatchProgressEntity.insert(progresses);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteProgress(final WatchProgressEntity progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfWatchProgressEntity.handle(progress);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateProgress(final WatchProgressEntity progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfWatchProgressEntity.handle(progress);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWatchProgress(final String userId, final String videoId,
      final long currentPosition, final long watchedDuration, final float progressPercent,
      final boolean isCompleted, final long lastWatchTime, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateWatchProgress.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, currentPosition);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, watchedDuration);
        _argIndex = 3;
        _stmt.bindDouble(_argIndex, progressPercent);
        _argIndex = 4;
        final int _tmp = isCompleted ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 5;
        _stmt.bindLong(_argIndex, lastWatchTime);
        _argIndex = 6;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 7;
        _stmt.bindString(_argIndex, userId);
        _argIndex = 8;
        _stmt.bindString(_argIndex, videoId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateWatchProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markVideoCompleted(final String userId, final String videoId,
      final long lastWatchTime, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkVideoCompleted.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, lastWatchTime);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 3;
        _stmt.bindString(_argIndex, userId);
        _argIndex = 4;
        _stmt.bindString(_argIndex, videoId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkVideoCompleted.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearUserProgress(final String userId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearUserProgress.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearUserProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearCourseProgress(final String courseId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearCourseProgress.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, courseId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearCourseProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAllProgress(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllProgress.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getProgress(final String userId, final String videoId,
      final Continuation<? super WatchProgressEntity> $completion) {
    final String _sql = "SELECT * FROM watch_progress WHERE userId = ? AND videoId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, videoId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<WatchProgressEntity>() {
      @Override
      @Nullable
      public WatchProgressEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgressId = CursorUtil.getColumnIndexOrThrow(_cursor, "progressId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCurrentPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPosition");
          final int _cursorIndexOfTotalDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDuration");
          final int _cursorIndexOfWatchedDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "watchedDuration");
          final int _cursorIndexOfProgressPercent = CursorUtil.getColumnIndexOrThrow(_cursor, "progressPercent");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watchCount");
          final int _cursorIndexOfLastWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchTime");
          final int _cursorIndexOfFirstWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "firstWatchTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final WatchProgressEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpProgressId;
            _tmpProgressId = _cursor.getString(_cursorIndexOfProgressId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final long _tmpCurrentPosition;
            _tmpCurrentPosition = _cursor.getLong(_cursorIndexOfCurrentPosition);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final long _tmpWatchedDuration;
            _tmpWatchedDuration = _cursor.getLong(_cursorIndexOfWatchedDuration);
            final float _tmpProgressPercent;
            _tmpProgressPercent = _cursor.getFloat(_cursorIndexOfProgressPercent);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastWatchTime;
            _tmpLastWatchTime = _cursor.getLong(_cursorIndexOfLastWatchTime);
            final long _tmpFirstWatchTime;
            _tmpFirstWatchTime = _cursor.getLong(_cursorIndexOfFirstWatchTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new WatchProgressEntity(_tmpProgressId,_tmpUserId,_tmpVideoId,_tmpCourseId,_tmpCurrentPosition,_tmpTotalDuration,_tmpWatchedDuration,_tmpProgressPercent,_tmpIsCompleted,_tmpWatchCount,_tmpLastWatchTime,_tmpFirstWatchTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<WatchProgressEntity> observeProgress(final String userId, final String videoId) {
    final String _sql = "SELECT * FROM watch_progress WHERE userId = ? AND videoId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, videoId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"watch_progress"}, new Callable<WatchProgressEntity>() {
      @Override
      @Nullable
      public WatchProgressEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgressId = CursorUtil.getColumnIndexOrThrow(_cursor, "progressId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCurrentPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPosition");
          final int _cursorIndexOfTotalDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDuration");
          final int _cursorIndexOfWatchedDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "watchedDuration");
          final int _cursorIndexOfProgressPercent = CursorUtil.getColumnIndexOrThrow(_cursor, "progressPercent");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watchCount");
          final int _cursorIndexOfLastWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchTime");
          final int _cursorIndexOfFirstWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "firstWatchTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final WatchProgressEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpProgressId;
            _tmpProgressId = _cursor.getString(_cursorIndexOfProgressId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final long _tmpCurrentPosition;
            _tmpCurrentPosition = _cursor.getLong(_cursorIndexOfCurrentPosition);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final long _tmpWatchedDuration;
            _tmpWatchedDuration = _cursor.getLong(_cursorIndexOfWatchedDuration);
            final float _tmpProgressPercent;
            _tmpProgressPercent = _cursor.getFloat(_cursorIndexOfProgressPercent);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastWatchTime;
            _tmpLastWatchTime = _cursor.getLong(_cursorIndexOfLastWatchTime);
            final long _tmpFirstWatchTime;
            _tmpFirstWatchTime = _cursor.getLong(_cursorIndexOfFirstWatchTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new WatchProgressEntity(_tmpProgressId,_tmpUserId,_tmpVideoId,_tmpCourseId,_tmpCurrentPosition,_tmpTotalDuration,_tmpWatchedDuration,_tmpProgressPercent,_tmpIsCompleted,_tmpWatchCount,_tmpLastWatchTime,_tmpFirstWatchTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUserProgresses(final String userId,
      final Continuation<? super List<WatchProgressEntity>> $completion) {
    final String _sql = "SELECT * FROM watch_progress WHERE userId = ? ORDER BY lastWatchTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WatchProgressEntity>>() {
      @Override
      @NonNull
      public List<WatchProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgressId = CursorUtil.getColumnIndexOrThrow(_cursor, "progressId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCurrentPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPosition");
          final int _cursorIndexOfTotalDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDuration");
          final int _cursorIndexOfWatchedDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "watchedDuration");
          final int _cursorIndexOfProgressPercent = CursorUtil.getColumnIndexOrThrow(_cursor, "progressPercent");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watchCount");
          final int _cursorIndexOfLastWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchTime");
          final int _cursorIndexOfFirstWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "firstWatchTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<WatchProgressEntity> _result = new ArrayList<WatchProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WatchProgressEntity _item;
            final String _tmpProgressId;
            _tmpProgressId = _cursor.getString(_cursorIndexOfProgressId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final long _tmpCurrentPosition;
            _tmpCurrentPosition = _cursor.getLong(_cursorIndexOfCurrentPosition);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final long _tmpWatchedDuration;
            _tmpWatchedDuration = _cursor.getLong(_cursorIndexOfWatchedDuration);
            final float _tmpProgressPercent;
            _tmpProgressPercent = _cursor.getFloat(_cursorIndexOfProgressPercent);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastWatchTime;
            _tmpLastWatchTime = _cursor.getLong(_cursorIndexOfLastWatchTime);
            final long _tmpFirstWatchTime;
            _tmpFirstWatchTime = _cursor.getLong(_cursorIndexOfFirstWatchTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new WatchProgressEntity(_tmpProgressId,_tmpUserId,_tmpVideoId,_tmpCourseId,_tmpCurrentPosition,_tmpTotalDuration,_tmpWatchedDuration,_tmpProgressPercent,_tmpIsCompleted,_tmpWatchCount,_tmpLastWatchTime,_tmpFirstWatchTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<WatchProgressEntity>> observeUserProgresses(final String userId) {
    final String _sql = "SELECT * FROM watch_progress WHERE userId = ? ORDER BY lastWatchTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"watch_progress"}, new Callable<List<WatchProgressEntity>>() {
      @Override
      @NonNull
      public List<WatchProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgressId = CursorUtil.getColumnIndexOrThrow(_cursor, "progressId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCurrentPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPosition");
          final int _cursorIndexOfTotalDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDuration");
          final int _cursorIndexOfWatchedDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "watchedDuration");
          final int _cursorIndexOfProgressPercent = CursorUtil.getColumnIndexOrThrow(_cursor, "progressPercent");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watchCount");
          final int _cursorIndexOfLastWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchTime");
          final int _cursorIndexOfFirstWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "firstWatchTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<WatchProgressEntity> _result = new ArrayList<WatchProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WatchProgressEntity _item;
            final String _tmpProgressId;
            _tmpProgressId = _cursor.getString(_cursorIndexOfProgressId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final long _tmpCurrentPosition;
            _tmpCurrentPosition = _cursor.getLong(_cursorIndexOfCurrentPosition);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final long _tmpWatchedDuration;
            _tmpWatchedDuration = _cursor.getLong(_cursorIndexOfWatchedDuration);
            final float _tmpProgressPercent;
            _tmpProgressPercent = _cursor.getFloat(_cursorIndexOfProgressPercent);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastWatchTime;
            _tmpLastWatchTime = _cursor.getLong(_cursorIndexOfLastWatchTime);
            final long _tmpFirstWatchTime;
            _tmpFirstWatchTime = _cursor.getLong(_cursorIndexOfFirstWatchTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new WatchProgressEntity(_tmpProgressId,_tmpUserId,_tmpVideoId,_tmpCourseId,_tmpCurrentPosition,_tmpTotalDuration,_tmpWatchedDuration,_tmpProgressPercent,_tmpIsCompleted,_tmpWatchCount,_tmpLastWatchTime,_tmpFirstWatchTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getCourseProgresses(final String userId, final String courseId,
      final Continuation<? super List<WatchProgressEntity>> $completion) {
    final String _sql = "SELECT * FROM watch_progress WHERE userId = ? AND courseId = ? ORDER BY lastWatchTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, courseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WatchProgressEntity>>() {
      @Override
      @NonNull
      public List<WatchProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgressId = CursorUtil.getColumnIndexOrThrow(_cursor, "progressId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCurrentPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPosition");
          final int _cursorIndexOfTotalDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDuration");
          final int _cursorIndexOfWatchedDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "watchedDuration");
          final int _cursorIndexOfProgressPercent = CursorUtil.getColumnIndexOrThrow(_cursor, "progressPercent");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watchCount");
          final int _cursorIndexOfLastWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchTime");
          final int _cursorIndexOfFirstWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "firstWatchTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<WatchProgressEntity> _result = new ArrayList<WatchProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WatchProgressEntity _item;
            final String _tmpProgressId;
            _tmpProgressId = _cursor.getString(_cursorIndexOfProgressId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final long _tmpCurrentPosition;
            _tmpCurrentPosition = _cursor.getLong(_cursorIndexOfCurrentPosition);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final long _tmpWatchedDuration;
            _tmpWatchedDuration = _cursor.getLong(_cursorIndexOfWatchedDuration);
            final float _tmpProgressPercent;
            _tmpProgressPercent = _cursor.getFloat(_cursorIndexOfProgressPercent);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastWatchTime;
            _tmpLastWatchTime = _cursor.getLong(_cursorIndexOfLastWatchTime);
            final long _tmpFirstWatchTime;
            _tmpFirstWatchTime = _cursor.getLong(_cursorIndexOfFirstWatchTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new WatchProgressEntity(_tmpProgressId,_tmpUserId,_tmpVideoId,_tmpCourseId,_tmpCurrentPosition,_tmpTotalDuration,_tmpWatchedDuration,_tmpProgressPercent,_tmpIsCompleted,_tmpWatchCount,_tmpLastWatchTime,_tmpFirstWatchTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<WatchProgressEntity>> observeCourseProgresses(final String userId,
      final String courseId) {
    final String _sql = "SELECT * FROM watch_progress WHERE userId = ? AND courseId = ? ORDER BY lastWatchTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, courseId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"watch_progress"}, new Callable<List<WatchProgressEntity>>() {
      @Override
      @NonNull
      public List<WatchProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgressId = CursorUtil.getColumnIndexOrThrow(_cursor, "progressId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCurrentPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPosition");
          final int _cursorIndexOfTotalDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDuration");
          final int _cursorIndexOfWatchedDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "watchedDuration");
          final int _cursorIndexOfProgressPercent = CursorUtil.getColumnIndexOrThrow(_cursor, "progressPercent");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watchCount");
          final int _cursorIndexOfLastWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchTime");
          final int _cursorIndexOfFirstWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "firstWatchTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<WatchProgressEntity> _result = new ArrayList<WatchProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WatchProgressEntity _item;
            final String _tmpProgressId;
            _tmpProgressId = _cursor.getString(_cursorIndexOfProgressId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final long _tmpCurrentPosition;
            _tmpCurrentPosition = _cursor.getLong(_cursorIndexOfCurrentPosition);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final long _tmpWatchedDuration;
            _tmpWatchedDuration = _cursor.getLong(_cursorIndexOfWatchedDuration);
            final float _tmpProgressPercent;
            _tmpProgressPercent = _cursor.getFloat(_cursorIndexOfProgressPercent);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastWatchTime;
            _tmpLastWatchTime = _cursor.getLong(_cursorIndexOfLastWatchTime);
            final long _tmpFirstWatchTime;
            _tmpFirstWatchTime = _cursor.getLong(_cursorIndexOfFirstWatchTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new WatchProgressEntity(_tmpProgressId,_tmpUserId,_tmpVideoId,_tmpCourseId,_tmpCurrentPosition,_tmpTotalDuration,_tmpWatchedDuration,_tmpProgressPercent,_tmpIsCompleted,_tmpWatchCount,_tmpLastWatchTime,_tmpFirstWatchTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getRecentWatchedVideos(final String userId, final int limit,
      final Continuation<? super List<WatchProgressEntity>> $completion) {
    final String _sql = "SELECT * FROM watch_progress WHERE userId = ? ORDER BY lastWatchTime DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WatchProgressEntity>>() {
      @Override
      @NonNull
      public List<WatchProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgressId = CursorUtil.getColumnIndexOrThrow(_cursor, "progressId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCurrentPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPosition");
          final int _cursorIndexOfTotalDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDuration");
          final int _cursorIndexOfWatchedDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "watchedDuration");
          final int _cursorIndexOfProgressPercent = CursorUtil.getColumnIndexOrThrow(_cursor, "progressPercent");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watchCount");
          final int _cursorIndexOfLastWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchTime");
          final int _cursorIndexOfFirstWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "firstWatchTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<WatchProgressEntity> _result = new ArrayList<WatchProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WatchProgressEntity _item;
            final String _tmpProgressId;
            _tmpProgressId = _cursor.getString(_cursorIndexOfProgressId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final long _tmpCurrentPosition;
            _tmpCurrentPosition = _cursor.getLong(_cursorIndexOfCurrentPosition);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final long _tmpWatchedDuration;
            _tmpWatchedDuration = _cursor.getLong(_cursorIndexOfWatchedDuration);
            final float _tmpProgressPercent;
            _tmpProgressPercent = _cursor.getFloat(_cursorIndexOfProgressPercent);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastWatchTime;
            _tmpLastWatchTime = _cursor.getLong(_cursorIndexOfLastWatchTime);
            final long _tmpFirstWatchTime;
            _tmpFirstWatchTime = _cursor.getLong(_cursorIndexOfFirstWatchTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new WatchProgressEntity(_tmpProgressId,_tmpUserId,_tmpVideoId,_tmpCourseId,_tmpCurrentPosition,_tmpTotalDuration,_tmpWatchedDuration,_tmpProgressPercent,_tmpIsCompleted,_tmpWatchCount,_tmpLastWatchTime,_tmpFirstWatchTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedVideos(final String userId,
      final Continuation<? super List<WatchProgressEntity>> $completion) {
    final String _sql = "SELECT * FROM watch_progress WHERE userId = ? AND isCompleted = 1 ORDER BY lastWatchTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WatchProgressEntity>>() {
      @Override
      @NonNull
      public List<WatchProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgressId = CursorUtil.getColumnIndexOrThrow(_cursor, "progressId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCurrentPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPosition");
          final int _cursorIndexOfTotalDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDuration");
          final int _cursorIndexOfWatchedDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "watchedDuration");
          final int _cursorIndexOfProgressPercent = CursorUtil.getColumnIndexOrThrow(_cursor, "progressPercent");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watchCount");
          final int _cursorIndexOfLastWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchTime");
          final int _cursorIndexOfFirstWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "firstWatchTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<WatchProgressEntity> _result = new ArrayList<WatchProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WatchProgressEntity _item;
            final String _tmpProgressId;
            _tmpProgressId = _cursor.getString(_cursorIndexOfProgressId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final long _tmpCurrentPosition;
            _tmpCurrentPosition = _cursor.getLong(_cursorIndexOfCurrentPosition);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final long _tmpWatchedDuration;
            _tmpWatchedDuration = _cursor.getLong(_cursorIndexOfWatchedDuration);
            final float _tmpProgressPercent;
            _tmpProgressPercent = _cursor.getFloat(_cursorIndexOfProgressPercent);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastWatchTime;
            _tmpLastWatchTime = _cursor.getLong(_cursorIndexOfLastWatchTime);
            final long _tmpFirstWatchTime;
            _tmpFirstWatchTime = _cursor.getLong(_cursorIndexOfFirstWatchTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new WatchProgressEntity(_tmpProgressId,_tmpUserId,_tmpVideoId,_tmpCourseId,_tmpCurrentPosition,_tmpTotalDuration,_tmpWatchedDuration,_tmpProgressPercent,_tmpIsCompleted,_tmpWatchCount,_tmpLastWatchTime,_tmpFirstWatchTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getIncompleteVideos(final String userId,
      final Continuation<? super List<WatchProgressEntity>> $completion) {
    final String _sql = "SELECT * FROM watch_progress WHERE userId = ? AND isCompleted = 0 ORDER BY lastWatchTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WatchProgressEntity>>() {
      @Override
      @NonNull
      public List<WatchProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgressId = CursorUtil.getColumnIndexOrThrow(_cursor, "progressId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCurrentPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPosition");
          final int _cursorIndexOfTotalDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDuration");
          final int _cursorIndexOfWatchedDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "watchedDuration");
          final int _cursorIndexOfProgressPercent = CursorUtil.getColumnIndexOrThrow(_cursor, "progressPercent");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watchCount");
          final int _cursorIndexOfLastWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchTime");
          final int _cursorIndexOfFirstWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "firstWatchTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<WatchProgressEntity> _result = new ArrayList<WatchProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WatchProgressEntity _item;
            final String _tmpProgressId;
            _tmpProgressId = _cursor.getString(_cursorIndexOfProgressId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final long _tmpCurrentPosition;
            _tmpCurrentPosition = _cursor.getLong(_cursorIndexOfCurrentPosition);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final long _tmpWatchedDuration;
            _tmpWatchedDuration = _cursor.getLong(_cursorIndexOfWatchedDuration);
            final float _tmpProgressPercent;
            _tmpProgressPercent = _cursor.getFloat(_cursorIndexOfProgressPercent);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastWatchTime;
            _tmpLastWatchTime = _cursor.getLong(_cursorIndexOfLastWatchTime);
            final long _tmpFirstWatchTime;
            _tmpFirstWatchTime = _cursor.getLong(_cursorIndexOfFirstWatchTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new WatchProgressEntity(_tmpProgressId,_tmpUserId,_tmpVideoId,_tmpCourseId,_tmpCurrentPosition,_tmpTotalDuration,_tmpWatchedDuration,_tmpProgressPercent,_tmpIsCompleted,_tmpWatchCount,_tmpLastWatchTime,_tmpFirstWatchTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseProgressStats(final String userId, final String courseId,
      final Continuation<? super CourseProgressStats> $completion) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            COUNT(*) as totalVideos,\n"
            + "            SUM(CASE WHEN isCompleted = 1 THEN 1 ELSE 0 END) as completedVideos,\n"
            + "            AVG(progressPercent) as avgProgress\n"
            + "        FROM watch_progress \n"
            + "        WHERE userId = ? AND courseId = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, courseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CourseProgressStats>() {
      @Override
      @Nullable
      public CourseProgressStats call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTotalVideos = 0;
          final int _cursorIndexOfCompletedVideos = 1;
          final int _cursorIndexOfAvgProgress = 2;
          final CourseProgressStats _result;
          if (_cursor.moveToFirst()) {
            final int _tmpTotalVideos;
            _tmpTotalVideos = _cursor.getInt(_cursorIndexOfTotalVideos);
            final int _tmpCompletedVideos;
            _tmpCompletedVideos = _cursor.getInt(_cursorIndexOfCompletedVideos);
            final float _tmpAvgProgress;
            _tmpAvgProgress = _cursor.getFloat(_cursorIndexOfAvgProgress);
            _result = new CourseProgressStats(_tmpTotalVideos,_tmpCompletedVideos,_tmpAvgProgress);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalWatchTime(final String userId,
      final Continuation<? super Long> $completion) {
    final String _sql = "SELECT SUM(watchedDuration) FROM watch_progress WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @Nullable
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final Long _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseWatchTime(final String userId, final String courseId,
      final Continuation<? super Long> $completion) {
    final String _sql = "SELECT SUM(watchedDuration) FROM watch_progress WHERE userId = ? AND courseId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, courseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @Nullable
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final Long _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
