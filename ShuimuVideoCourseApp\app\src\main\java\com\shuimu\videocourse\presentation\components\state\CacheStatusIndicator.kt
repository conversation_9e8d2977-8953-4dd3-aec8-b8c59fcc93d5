package com.shuimu.videocourse.presentation.components.state

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.domain.cache.CacheStatus
import com.shuimu.videocourse.domain.cache.CacheQuality
import com.shuimu.videocourse.domain.cache.DownloadProgress
import com.shuimu.videocourse.presentation.components.basic.ProgressBar

/**
 * 缓存状态指示器数据类
 */
data class CacheIndicatorData(
    val videoId: String,
    val title: String,
    val status: CacheStatus,
    val quality: CacheQuality? = null,
    val progress: Float = 0f,
    val downloadSpeed: Long = 0L, // bytes per second
    val estimatedTimeRemaining: Long = 0L, // seconds
    val fileSize: Long = 0L,
    val downloadedSize: Long = 0L,
    val errorMessage: String? = null
)

/**
 * 水幕视频课程App - 缓存状态指示器组件
 * 
 * 基于原型设计：UI Prototype/02-视频播放页.html 缓存状态显示
 * 
 * 功能特性：
 * - 显示不同缓存状态的图标和颜色
 * - 下载进度条和百分比显示
 * - 下载速度和剩余时间显示
 * - 支持点击操作（暂停/恢复/取消）
 * - 动画效果和状态转换
 * - 错误状态提示
 * 
 * 状态类型：
 * - NOT_CACHED: 未缓存（灰色云朵图标）
 * - DOWNLOADING: 下载中（蓝色下载图标+进度条）
 * - PAUSED: 暂停下载（橙色暂停图标）
 * - CACHED: 已缓存（绿色完成图标）
 * - ERROR: 下载错误（红色错误图标）
 * - EXPIRED: 缓存过期（黄色警告图标）
 * 
 * @param data 缓存指示器数据
 * @param onStatusClick 状态点击回调
 * @param showDetails 是否显示详细信息
 * @param compact 是否使用紧凑模式
 * @param modifier 修饰符
 */
@Composable
fun CacheStatusIndicator(
    data: CacheIndicatorData,
    onStatusClick: (CacheStatus) -> Unit = {},
    showDetails: Boolean = false,
    compact: Boolean = false,
    modifier: Modifier = Modifier
) {
    val statusConfig = getCacheStatusConfig(data.status)
    
    if (compact) {
        CompactCacheIndicator(
            data = data,
            statusConfig = statusConfig,
            onStatusClick = onStatusClick,
            modifier = modifier
        )
    } else {
        DetailedCacheIndicator(
            data = data,
            statusConfig = statusConfig,
            onStatusClick = onStatusClick,
            showDetails = showDetails,
            modifier = modifier
        )
    }
}

/**
 * 紧凑模式缓存指示器
 */
@Composable
private fun CompactCacheIndicator(
    data: CacheIndicatorData,
    statusConfig: CacheStatusConfig,
    onStatusClick: (CacheStatus) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(32.dp)
            .clip(CircleShape)
            .background(statusConfig.backgroundColor)
            .clickable { onStatusClick(data.status) },
        contentAlignment = Alignment.Center
    ) {
        when (data.status) {
            CacheStatus.DOWNLOADING -> {
                // 下载中显示进度环
                CircularProgressIndicator(
                    progress = data.progress / 100f,
                    modifier = Modifier.size(24.dp),
                    color = statusConfig.iconColor,
                    strokeWidth = 2.dp
                )
                
                // 中心显示百分比
                Text(
                    text = "${data.progress.toInt()}%",
                    color = statusConfig.iconColor,
                    fontSize = 8.sp,
                    fontWeight = FontWeight.Bold
                )
            }
            else -> {
                Icon(
                    imageVector = statusConfig.icon,
                    contentDescription = statusConfig.description,
                    tint = statusConfig.iconColor,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 详细模式缓存指示器
 */
@Composable
private fun DetailedCacheIndicator(
    data: CacheIndicatorData,
    statusConfig: CacheStatusConfig,
    onStatusClick: (CacheStatus) -> Unit,
    showDetails: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onStatusClick(data.status) },
        colors = CardDefaults.cardColors(
            containerColor = statusConfig.backgroundColor.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 状态行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 状态图标和文本
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    StatusIcon(
                        status = data.status,
                        statusConfig = statusConfig,
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Column {
                        Text(
                            text = statusConfig.title,
                            color = statusConfig.iconColor,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                        
                        if (data.quality != null) {
                            Text(
                                text = data.quality.label,
                                color = Color.Gray,
                                fontSize = 12.sp
                            )
                        }
                    }
                }
                
                // 状态值
                StatusValue(data = data, statusConfig = statusConfig)
            }
            
            // 进度条（仅下载中显示）
            if (data.status == CacheStatus.DOWNLOADING && data.progress > 0) {
                Spacer(modifier = Modifier.height(12.dp))
                
                ProgressBar(
                    progress = data.progress,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(6.dp),
                    backgroundColor = Color.Gray.copy(alpha = 0.3f),
                    progressColor = statusConfig.iconColor
                )
            }
            
            // 详细信息
            if (showDetails) {
                Spacer(modifier = Modifier.height(8.dp))
                DetailedInfo(data = data)
            }
            
            // 错误信息
            if (data.status == CacheStatus.ERROR && !data.errorMessage.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = data.errorMessage,
                    color = Color.Red,
                    fontSize = 12.sp
                )
            }
        }
    }
}

/**
 * 状态图标组件
 */
@Composable
private fun StatusIcon(
    status: CacheStatus,
    statusConfig: CacheStatusConfig,
    modifier: Modifier = Modifier
) {
    when (status) {
        CacheStatus.DOWNLOADING -> {
            // 旋转的下载图标
            val rotation by rememberInfiniteTransition().animateFloat(
                initialValue = 0f,
                targetValue = 360f,
                animationSpec = infiniteRepeatable(
                    animation = tween(2000, easing = LinearEasing),
                    repeatMode = RepeatMode.Restart
                )
            )
            
            Icon(
                imageVector = statusConfig.icon,
                contentDescription = statusConfig.description,
                tint = statusConfig.iconColor,
                modifier = modifier.rotate(rotation)
            )
        }
        else -> {
            Icon(
                imageVector = statusConfig.icon,
                contentDescription = statusConfig.description,
                tint = statusConfig.iconColor,
                modifier = modifier
            )
        }
    }
}

/**
 * 状态值组件
 */
@Composable
private fun StatusValue(
    data: CacheIndicatorData,
    statusConfig: CacheStatusConfig
) {
    when (data.status) {
        CacheStatus.DOWNLOADING -> {
            Text(
                text = "${data.progress.toInt()}%",
                color = statusConfig.iconColor,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold
            )
        }
        CacheStatus.CACHED -> {
            Text(
                text = formatFileSize(data.fileSize),
                color = statusConfig.iconColor,
                fontSize = 12.sp
            )
        }
        else -> {
            // 其他状态不显示值
        }
    }
}

/**
 * 详细信息组件
 */
@Composable
private fun DetailedInfo(data: CacheIndicatorData) {
    when (data.status) {
        CacheStatus.DOWNLOADING -> {
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "下载速度: ${formatSpeed(data.downloadSpeed)}",
                        color = Color.Gray,
                        fontSize = 11.sp
                    )
                    
                    Text(
                        text = "剩余时间: ${formatTime(data.estimatedTimeRemaining)}",
                        color = Color.Gray,
                        fontSize = 11.sp
                    )
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "${formatFileSize(data.downloadedSize)} / ${formatFileSize(data.fileSize)}",
                    color = Color.Gray,
                    fontSize = 11.sp
                )
            }
        }
        CacheStatus.CACHED -> {
            Text(
                text = "缓存完成，可离线播放",
                color = Color.Gray,
                fontSize = 11.sp
            )
        }
        CacheStatus.PAUSED -> {
            Text(
                text = "下载已暂停，点击恢复",
                color = Color.Gray,
                fontSize = 11.sp
            )
        }
        else -> {
            // 其他状态不显示详细信息
        }
    }
}

/**
 * 缓存状态配置数据类
 */
private data class CacheStatusConfig(
    val title: String,
    val description: String,
    val icon: ImageVector,
    val iconColor: Color,
    val backgroundColor: Color
)

/**
 * 获取缓存状态配置
 */
private fun getCacheStatusConfig(status: CacheStatus): CacheStatusConfig {
    return when (status) {
        CacheStatus.NOT_CACHED -> CacheStatusConfig(
            title = "未缓存",
            description = "点击开始缓存",
            icon = Icons.Default.CloudQueue,
            iconColor = Color.Gray,
            backgroundColor = Color.Gray
        )
        CacheStatus.DOWNLOADING -> CacheStatusConfig(
            title = "下载中",
            description = "正在下载",
            icon = Icons.Default.CloudDownload,
            iconColor = Color.Blue,
            backgroundColor = Color.Blue
        )
        CacheStatus.PAUSED -> CacheStatusConfig(
            title = "已暂停",
            description = "下载已暂停",
            icon = Icons.Default.Pause,
            iconColor = Color.Orange,
            backgroundColor = Color.Orange
        )
        CacheStatus.CACHED -> CacheStatusConfig(
            title = "已缓存",
            description = "缓存完成",
            icon = Icons.Default.CloudDone,
            iconColor = Color.Green,
            backgroundColor = Color.Green
        )
        CacheStatus.ERROR -> CacheStatusConfig(
            title = "下载失败",
            description = "下载出错",
            icon = Icons.Default.CloudOff,
            iconColor = Color.Red,
            backgroundColor = Color.Red
        )
        CacheStatus.EXPIRED -> CacheStatusConfig(
            title = "缓存过期",
            description = "缓存已过期",
            icon = Icons.Default.Warning,
            iconColor = Color(0xFFFF9800),
            backgroundColor = Color(0xFFFF9800)
        )
    }
}

/**
 * 格式化文件大小
 */
private fun formatFileSize(bytes: Long): String {
    if (bytes <= 0) return "0 B"
    
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    val digitGroups = (Math.log10(bytes.toDouble()) / Math.log10(1024.0)).toInt()
    
    return String.format(
        "%.1f %s",
        bytes / Math.pow(1024.0, digitGroups.toDouble()),
        units[digitGroups]
    )
}

/**
 * 格式化下载速度
 */
private fun formatSpeed(bytesPerSecond: Long): String {
    return "${formatFileSize(bytesPerSecond)}/s"
}

/**
 * 格式化时间
 */
private fun formatTime(seconds: Long): String {
    if (seconds <= 0) return "未知"
    
    val hours = seconds / 3600
    val minutes = (seconds % 3600) / 60
    val secs = seconds % 60
    
    return when {
        hours > 0 -> String.format("%d:%02d:%02d", hours, minutes, secs)
        minutes > 0 -> String.format("%d:%02d", minutes, secs)
        else -> "${secs}秒"
    }
}

/**
 * 批量缓存状态指示器
 * 
 * @param items 缓存项列表
 * @param modifier 修饰符
 */
@Composable
fun BatchCacheStatusIndicator(
    items: List<CacheItem>,
    modifier: Modifier = Modifier
) {
    val totalItems = items.size
    val cachedItems = items.count { it.status == CacheStatus.Cached }
    val downloadingItems = items.count { it.status == CacheStatus.Downloading }
    val errorItems = items.count { it.status == CacheStatus.Error }
    
    val overallProgress = if (totalItems > 0) {
        (cachedItems + items.filter { it.status == CacheStatus.Downloading }
            .sumOf { it.progress.toDouble() }) / totalItems
    } else 0.0
    
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 总体进度
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "缓存进度",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    ),
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "$cachedItems/$totalItems",
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontSize = 12.sp
                    ),
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            // 进度条
            LinearProgressIndicator(
                progress = overallProgress.toFloat(),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp)),
                color = Color(0xFF10b981),
                trackColor = Color(0xFF10b981).copy(alpha = 0.2f)
            )
            
            // 状态统计
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                if (downloadingItems > 0) {
                    StatusCount(
                        count = downloadingItems,
                        label = "下载中",
                        color = Color(0xFF3b82f6)
                    )
                }
                
                if (errorItems > 0) {
                    StatusCount(
                        count = errorItems,
                        label = "失败",
                        color = Color(0xFFef4444)
                    )
                }
            }
        }
    }
}

/**
 * 状态计数组件
 */
@Composable
private fun StatusCount(
    count: Int,
    label: String,
    color: Color
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(color = color, shape = CircleShape)
        )
        
        Text(
            text = "$count $label",
            style = MaterialTheme.typography.bodySmall.copy(
                fontSize = 11.sp
            ),
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

/**
 * 缓存状态枚举
 */
enum class CacheStatus {
    NotCached,      // 未缓存
    Downloading,    // 下载中
    Cached,         // 已缓存
    Error,          // 下载失败
    Paused          // 已暂停
}

/**
 * 缓存指示器尺寸枚举
 */
enum class CacheIndicatorSize {
    Small,      // 小
    Medium,     // 中
    Large       // 大
}

/**
 * 缓存项数据类
 */
data class CacheItem(
    val id: String,
    val name: String,
    val status: CacheStatus,
    val progress: Float = 0f,
    val size: Long = 0L
)

// 预览组件
@Composable
fun CacheStatusIndicatorPreview() {
    ShuimuTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 不同状态的指示器
            CacheStatusIndicator(
                data = CacheIndicatorData("1", "视频1", CacheStatus.Cached),
                showDetails = true
            )
            
            CacheStatusIndicator(
                data = CacheIndicatorData("2", "视频2", CacheStatus.Downloading, 0.3f),
                showDetails = true
            )
            
            CacheStatusIndicator(
                data = CacheIndicatorData("3", "视频3", CacheStatus.NotCached),
                showDetails = true
            )
            
            CacheStatusIndicator(
                data = CacheIndicatorData("4", "视频4", CacheStatus.Error, errorMessage = "下载失败"),
                showDetails = true
            )
            
            // 批量缓存状态
            BatchCacheStatusIndicator(
                items = listOf(
                    CacheItem("1", "视频1", CacheStatus.Cached),
                    CacheItem("2", "视频2", CacheStatus.Downloading, 0.3f),
                    CacheItem("3", "视频3", CacheStatus.NotCached),
                    CacheItem("4", "视频4", CacheStatus.Error)
                )
            )
        }
    }
} 