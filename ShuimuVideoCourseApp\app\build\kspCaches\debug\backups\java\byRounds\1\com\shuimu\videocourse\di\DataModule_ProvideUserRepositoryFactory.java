package com.shuimu.videocourse.di;

import android.content.Context;
import com.shuimu.videocourse.data.local.dao.UserDao;
import com.shuimu.videocourse.data.remote.api.UserApiService;
import com.shuimu.videocourse.data.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideUserRepositoryFactory implements Factory<UserRepository> {
  private final Provider<UserDao> userDaoProvider;

  private final Provider<UserApiService> userApiServiceProvider;

  private final Provider<Context> contextProvider;

  public DataModule_ProvideUserRepositoryFactory(Provider<UserDao> userDaoProvider,
      Provider<UserApiService> userApiServiceProvider, Provider<Context> contextProvider) {
    this.userDaoProvider = userDaoProvider;
    this.userApiServiceProvider = userApiServiceProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public UserRepository get() {
    return provideUserRepository(userDaoProvider.get(), userApiServiceProvider.get(), contextProvider.get());
  }

  public static DataModule_ProvideUserRepositoryFactory create(Provider<UserDao> userDaoProvider,
      Provider<UserApiService> userApiServiceProvider, Provider<Context> contextProvider) {
    return new DataModule_ProvideUserRepositoryFactory(userDaoProvider, userApiServiceProvider, contextProvider);
  }

  public static UserRepository provideUserRepository(UserDao userDao, UserApiService userApiService,
      Context context) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideUserRepository(userDao, userApiService, context));
  }
}
