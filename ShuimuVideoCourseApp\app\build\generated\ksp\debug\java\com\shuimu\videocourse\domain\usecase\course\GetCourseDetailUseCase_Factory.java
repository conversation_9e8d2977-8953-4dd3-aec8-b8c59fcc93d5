package com.shuimu.videocourse.domain.usecase.course;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetCourseDetailUseCase_Factory implements Factory<GetCourseDetailUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public GetCourseDetailUseCase_Factory(Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public GetCourseDetailUseCase get() {
    return newInstance(courseRepositoryProvider.get());
  }

  public static GetCourseDetailUseCase_Factory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new GetCourseDetailUseCase_Factory(courseRepositoryProvider);
  }

  public static GetCourseDetailUseCase newInstance(CourseRepository courseRepository) {
    return new GetCourseDetailUseCase(courseRepository);
  }
}
