package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.UserRepository;
import com.shuimu.videocourse.domain.usecase.user.LogoutUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideLogoutUseCaseFactory implements Factory<LogoutUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public DomainModule_ProvideLogoutUseCaseFactory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public LogoutUseCase get() {
    return provideLogoutUseCase(userRepositoryProvider.get());
  }

  public static DomainModule_ProvideLogoutUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new DomainModule_ProvideLogoutUseCaseFactory(userRepositoryProvider);
  }

  public static LogoutUseCase provideLogoutUseCase(UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideLogoutUseCase(userRepository));
  }
}
