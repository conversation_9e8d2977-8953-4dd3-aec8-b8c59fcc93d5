{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,514,702,783,864,940,1031,1124,1194,1258,1342,1425,1490,1554,1617,1687,1807,1925,2044,2116,2200,2269,2338,2432,2526,2591,2657,2710,2770,2818,2879,2944,3014,3079,3145,3209,3269,3334,3399,3465,3517,3579,3655,3731", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,64,65,51,61,75,75,54", "endOffsets": "318,509,697,778,859,935,1026,1119,1189,1253,1337,1420,1485,1549,1612,1682,1802,1920,2039,2111,2195,2264,2333,2427,2521,2586,2652,2705,2765,2813,2874,2939,3009,3074,3140,3204,3264,3329,3394,3460,3512,3574,3650,3726,3781"}, "to": {"startLines": "2,11,15,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,373,564,8782,8863,8944,9020,9111,9204,9274,9338,9422,9505,9570,9634,9697,9767,9887,10005,10124,10196,10280,10349,10418,10512,10606,10671,11431,11484,11544,11592,11653,11718,11788,11853,11919,11983,12043,12108,12173,12239,12291,12353,12429,12505", "endLines": "10,14,18,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,64,65,51,61,75,75,54", "endOffsets": "368,559,747,8858,8939,9015,9106,9199,9269,9333,9417,9500,9565,9629,9692,9762,9882,10000,10119,10191,10275,10344,10413,10507,10601,10666,10732,11479,11539,11587,11648,11713,11783,11848,11914,11978,12038,12103,12168,12234,12286,12348,12424,12500,12555"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "50,51,52,53,54,55,56,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3933,4028,4130,4228,4331,4437,4542,13602", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "4023,4125,4223,4326,4432,4537,4657,13698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1345,1416", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1340,1411,1533"}, "to": {"startLines": "57,58,91,92,94,147,148,149,150,151,152,153,154,157,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4662,4755,8436,8532,8700,12638,12716,12807,12898,12982,13050,13116,13198,13449,13805,13882,13953", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "4750,4830,8527,8622,8777,12711,12802,12893,12977,13045,13111,13193,13278,13516,13877,13948,14070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,858,957,1068,1154,1256,1373,1454,1531,1623,1717,1813,1915,2024,2118,2219,2313,2405,2498,2581,2692,2796,2895,3005,3107,3206,3372,13366", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "853,952,1063,1149,1251,1368,1449,1526,1618,1712,1808,1910,2019,2113,2214,2308,2400,2493,2576,2687,2791,2890,3000,3102,3201,3367,3469,13444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,289,399,514,592,689,804,933,1049,1193,1276,1373,1463,1558,1670,1793,1893,2024,2153,2280,2451,2573,2688,2806,2925,3016,3109,3225,3355,3456,3555,3656,3782,3913,4017,4115,4188,4266,4349,4430,4532,4608,4690,4787,4887,4977,5077,5162,5267,5364,5466,5579,5655,5755", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "166,284,394,509,587,684,799,928,1044,1188,1271,1368,1458,1553,1665,1788,1888,2019,2148,2275,2446,2568,2683,2801,2920,3011,3104,3220,3350,3451,3550,3651,3777,3908,4012,4110,4183,4261,4344,4425,4527,4603,4685,4782,4882,4972,5072,5157,5262,5359,5461,5574,5650,5750,5845"}, "to": {"startLines": "46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,146,155,158,160,164,165,166,167,168,169,170,171,172,173,174,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3474,3590,3708,3818,4835,4913,5010,5125,5254,5370,5514,5597,5694,5784,5879,5991,6114,6214,6345,6474,6601,6772,6894,7009,7127,7246,7337,7430,7546,7676,7777,7876,7977,8103,8234,8338,8627,12560,13283,13521,13703,14075,14151,14233,14330,14430,14520,14620,14705,14810,14907,15009,15122,15198,15298", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "3585,3703,3813,3928,4908,5005,5120,5249,5365,5509,5592,5689,5779,5874,5986,6109,6209,6340,6469,6596,6767,6889,7004,7122,7241,7332,7425,7541,7671,7772,7871,7972,8098,8229,8333,8431,8695,12633,13361,13597,13800,14146,14228,14325,14425,14515,14615,14700,14805,14902,15004,15117,15193,15293,15388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,263,337,419,490,580,672", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "124,191,258,332,414,485,575,667,744"}, "to": {"startLines": "119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10737,10811,10878,10945,11019,11101,11172,11262,11354", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "10806,10873,10940,11014,11096,11167,11257,11349,11426"}}]}]}