---
description: 
globs: 
alwaysApply: false
---

# 🚨 组件强制使用规则

## ⚠️ 开发前必须检查
创建任何UI组件前，**必须**先执行：
```kotlin
checkComponent("组件名称")  // 检查是否已存在
```

## 📋 23个必须使用的核心组件

### 基础组件（5个）
- **ShuimuButton** - 按钮（4种类型、3种尺寸）
- **ShuimuCard** - 卡片（边框、阴影、悬停）
- **ShuimuTextField** - 输入框（多种类型和状态）
- **BadgeComponent** - 徽章（11级等级系统）
- **ProgressBar** - 进度条（线性、圆形、环形）

### 展示组件（4个）
- **VideoItem** - 视频项（购买状态、播放进度）
- **CategoryCard** - 分类卡片（展开收起、视频列表）
- **SearchItem** - 搜索结果项（统一搜索样式）
- **SeriesHeader** - 系列头部（价格和状态）

### 导航组件（5个）
- **TopAppBar** - 顶部导航栏（返回、标题、分享）
- **BottomNavigationBar** - 底部导航栏（4个导航项）
- **ShareModal** - 分享弹窗（微信、朋友圈、QQ）
- **PaymentModal** - 支付结果弹窗（支付状态）
- **PurchaseModal** - 购买确认弹窗（底部支付框）

### 视频组件（3个）
- **VideoPlayer** - 视频播放器
- **VideoInfoPanel** - 视频信息面板
- **VideoControls** - 视频控制器

### 用户组件（2个）
- **UserProfile** - 用户资料
- **UserStats** - 用户统计

### 状态组件（4个）
- **LoadingIndicator** - 加载指示器
- **ErrorMessage** - 错误消息
- **CacheStatusIndicator** - 缓存状态
- **EmptyState** - 空状态

## 🚫 严格禁止
- ❌ 创建与上述23个组件功能重复的新组件
- ❌ 绕过现有组件直接使用原生Compose组件
- ❌ 复制现有组件代码创建新文件

## ✅ 必须遵循
- ✅ 优先使用现有的23个核心组件
- ✅ 如需修改功能，修改现有组件而非创建新组件
- ✅ 使用checkComponent()检查组件是否存在

## 🔍 快捷检查命令
```kotlin
// 检查组件
checkComponent("Button")        // 检查按钮
checkComponent("Video")         // 检查视频组件
checkComponent("Modal")         // 检查弹窗

// 搜索组件
searchComponents("video")       // 搜索视频相关
searchComponents("user")        // 搜索用户相关

// 获取建议
getComponentSuggestions("button")  // 获取按钮使用建议
```

---

**🎯 记住：23个核心组件已覆盖绝大部分场景，优先使用现有组件！** 