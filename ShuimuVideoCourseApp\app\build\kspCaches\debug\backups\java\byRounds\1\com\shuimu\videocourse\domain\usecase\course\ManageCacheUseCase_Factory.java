package com.shuimu.videocourse.domain.usecase.course;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ManageCacheUseCase_Factory implements Factory<ManageCacheUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public ManageCacheUseCase_Factory(Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public ManageCacheUseCase get() {
    return newInstance(courseRepositoryProvider.get());
  }

  public static ManageCacheUseCase_Factory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new ManageCacheUseCase_Factory(courseRepositoryProvider);
  }

  public static ManageCacheUseCase newInstance(CourseRepository courseRepository) {
    return new ManageCacheUseCase(courseRepository);
  }
}
