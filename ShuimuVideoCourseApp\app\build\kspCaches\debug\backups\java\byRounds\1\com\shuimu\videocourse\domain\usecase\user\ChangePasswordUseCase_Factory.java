package com.shuimu.videocourse.domain.usecase.user;

import com.shuimu.videocourse.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ChangePasswordUseCase_Factory implements Factory<ChangePasswordUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public ChangePasswordUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public ChangePasswordUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static ChangePasswordUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new ChangePasswordUseCase_Factory(userRepositoryProvider);
  }

  public static ChangePasswordUseCase newInstance(UserRepository userRepository) {
    return new ChangePasswordUseCase(userRepository);
  }
}
