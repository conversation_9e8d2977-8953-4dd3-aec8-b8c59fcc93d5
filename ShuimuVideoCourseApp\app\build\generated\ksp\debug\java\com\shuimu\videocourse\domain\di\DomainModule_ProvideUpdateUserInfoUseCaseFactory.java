package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.UserRepository;
import com.shuimu.videocourse.domain.usecase.user.UpdateUserInfoUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideUpdateUserInfoUseCaseFactory implements Factory<UpdateUserInfoUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public DomainModule_ProvideUpdateUserInfoUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UpdateUserInfoUseCase get() {
    return provideUpdateUserInfoUseCase(userRepositoryProvider.get());
  }

  public static DomainModule_ProvideUpdateUserInfoUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new DomainModule_ProvideUpdateUserInfoUseCaseFactory(userRepositoryProvider);
  }

  public static UpdateUserInfoUseCase provideUpdateUserInfoUseCase(UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideUpdateUserInfoUseCase(userRepository));
  }
}
