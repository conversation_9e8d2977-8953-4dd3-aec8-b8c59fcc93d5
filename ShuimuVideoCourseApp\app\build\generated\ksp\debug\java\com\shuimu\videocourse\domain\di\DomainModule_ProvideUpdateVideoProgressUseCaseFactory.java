package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.repository.UserRepository;
import com.shuimu.videocourse.domain.usecase.course.UpdateVideoProgressUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideUpdateVideoProgressUseCaseFactory implements Factory<UpdateVideoProgressUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public DomainModule_ProvideUpdateVideoProgressUseCaseFactory(
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UpdateVideoProgressUseCase get() {
    return provideUpdateVideoProgressUseCase(courseRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static DomainModule_ProvideUpdateVideoProgressUseCaseFactory create(
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new DomainModule_ProvideUpdateVideoProgressUseCaseFactory(courseRepositoryProvider, userRepositoryProvider);
  }

  public static UpdateVideoProgressUseCase provideUpdateVideoProgressUseCase(
      CourseRepository courseRepository, UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideUpdateVideoProgressUseCase(courseRepository, userRepository));
  }
}
