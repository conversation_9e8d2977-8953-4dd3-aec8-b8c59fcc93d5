package com.shuimu.videocourse.presentation.components.navigation

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.MenuBook
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.MenuBook
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 底部导航项数据类
 */
data class BottomNavItem(
    val id: String,
    val label: String,
    val selectedIcon: ImageVector,
    val unselectedIcon: ImageVector,
    val isEmoji: Boolean = false,
    val emojiIcon: String = ""
)

/**
 * 水幕视频课程App - 底部导航栏组件
 * 
 * 基于原型设计：UI Prototype/data/component-templates-with-styles.js 第350-420行底部导航样式定义
 * 
 * 功能特性：
 * - 支持4个导航项：首页、学习、分享、我的
 * - 实现激活状态指示和切换动画
 * - 添加图标和文字布局
 * - 使用原型的#667eea紫蓝色渐变主题
 * 
 * @param currentPage 当前选中的页面ID
 * @param onNavigate 导航点击回调
 * @param backgroundColor 背景颜色，默认为白色
 * @param selectedColor 选中状态颜色
 * @param unselectedColor 未选中状态颜色
 * @param modifier 修饰符
 */
@Composable
fun BottomNavigationBar(
    currentPage: String,
    onNavigate: (String) -> Unit,
    backgroundColor: Color = Color.White,
    selectedColor: Color = Color(0xFF667EEA), // 原型主色调
    unselectedColor: Color = Color(0xFF6B7280),
    modifier: Modifier = Modifier
) {
    // 基于原型的导航项配置
    val navItems = listOf(
        BottomNavItem(
            id = "home",
            label = "首页",
            selectedIcon = Icons.Filled.Home,
            unselectedIcon = Icons.Outlined.Home
        ),
        BottomNavItem(
            id = "learning",
            label = "学习",
            selectedIcon = Icons.Filled.MenuBook,
            unselectedIcon = Icons.Outlined.MenuBook
        ),
        BottomNavItem(
            id = "share",
            label = "分享推广分成",
            selectedIcon = Icons.Filled.Home, // 占位，实际使用emoji
            unselectedIcon = Icons.Outlined.Home, // 占位，实际使用emoji
            isEmoji = true,
            emojiIcon = "💰"
        ),
        BottomNavItem(
            id = "profile",
            label = "我",
            selectedIcon = Icons.Filled.Person,
            unselectedIcon = Icons.Outlined.Person
        )
    )
    
    // 基于原型的底部导航样式
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(80.dp), // 原型中的导航栏高度
        color = backgroundColor,
        shadowElevation = 8.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 8.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            navItems.forEach { item ->
                BottomNavItemView(
                    item = item,
                    isSelected = currentPage == item.id,
                    onClick = { onNavigate(item.id) },
                    selectedColor = selectedColor,
                    unselectedColor = unselectedColor,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 底部导航项视图组件
 */
@Composable
private fun BottomNavItemView(
    item: BottomNavItem,
    isSelected: Boolean,
    onClick: () -> Unit,
    selectedColor: Color,
    unselectedColor: Color,
    modifier: Modifier = Modifier
) {
    val color = if (isSelected) selectedColor else unselectedColor
    val fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal
    
    Column(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .clickable { onClick() }
            .padding(vertical = 8.dp, horizontal = 4.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // 图标区域
        Box(
            modifier = Modifier.size(24.dp),
            contentAlignment = Alignment.Center
        ) {
            if (item.isEmoji) {
                // Emoji图标
                Text(
                    text = item.emojiIcon,
                    fontSize = 20.sp,
                    color = color
                )
            } else {
                // 矢量图标
                Icon(
                    imageVector = if (isSelected) item.selectedIcon else item.unselectedIcon,
                    contentDescription = item.label,
                    tint = color,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 标签文字
        Text(
            text = item.label,
            fontSize = 10.sp,
            fontWeight = fontWeight,
            color = color,
            textAlign = TextAlign.Center,
            maxLines = 1,
            modifier = Modifier.padding(horizontal = 2.dp)
        )
    }
}

/**
 * 预览组件
 */
@Composable
fun BottomNavigationBarPreview() {
    ShuimuTheme {
        var currentPage by remember { mutableStateOf("home") }
        
        Column {
            // 模拟页面内容
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
                    .background(Color(0xFFF9FAFB)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "当前页面: $currentPage",
                    fontSize = 16.sp,
                    color = Color(0xFF1F2937)
                )
            }
            
            // 底部导航栏
            BottomNavigationBar(
                currentPage = currentPage,
                onNavigate = { page ->
                    currentPage = page
                }
            )
        }
    }
} 