package com.shuimu.videocourse.di;

import com.shuimu.videocourse.data.local.dao.CacheDao;
import com.shuimu.videocourse.data.local.database.ShuimuDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideCacheDaoFactory implements Factory<CacheDao> {
  private final Provider<ShuimuDatabase> databaseProvider;

  public DataModule_ProvideCacheDaoFactory(Provider<ShuimuDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public CacheDao get() {
    return provideCacheDao(databaseProvider.get());
  }

  public static DataModule_ProvideCacheDaoFactory create(
      Provider<ShuimuDatabase> databaseProvider) {
    return new DataModule_ProvideCacheDaoFactory(databaseProvider);
  }

  public static CacheDao provideCacheDao(ShuimuDatabase database) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideCacheDao(database));
  }
}
