package com.shuimu.videocourse.presentation.components.user

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 设置项组件
 * 
 * 基于原型设计：UI Prototype/09-设置页面.html 设置项列表区域
 * - 支持设置项标题、描述、开关、选择器
 * - 实现分组显示和统一设置项样式
 * - 添加不同控件类型支持
 * - 支持自定义图标背景色
 * 
 * @param icon 设置图标
 * @param title 设置标题
 * @param description 设置描述
 * @param iconBackgroundColor 图标背景色
 * @param controlType 控件类型
 * @param isChecked 开关状态（仅开关类型使用）
 * @param selectedValue 选择值（仅选择器类型使用）
 * @param onCheckedChange 开关状态变化回调
 * @param onClick 点击回调
 * @param modifier 修饰符
 */
@Composable
fun SettingItem(
    icon: ImageVector,
    title: String,
    description: String,
    iconBackgroundColor: Color = Color(0xFF667eea),
    controlType: ControlType = ControlType.Arrow,
    isChecked: Boolean = false,
    selectedValue: String = "",
    onCheckedChange: (Boolean) -> Unit = {},
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { 
                when (controlType) {
                    ControlType.Switch -> onCheckedChange(!isChecked)
                    else -> onClick()
                }
            }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 设置图标
        Box(
            modifier = Modifier
                .size(32.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(iconBackgroundColor),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(16.dp),
                tint = Color.White
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 标题和描述
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF374151)
            )
            
            if (description.isNotEmpty()) {
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = description,
                    fontSize = 14.sp,
                    color = Color(0xFF6b7280)
                )
            }
        }
        
        // 控件区域
        when (controlType) {
            ControlType.Switch -> {
                Switch(
                    checked = isChecked,
                    onCheckedChange = onCheckedChange,
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color.White,
                        checkedTrackColor = Color(0xFF667eea),
                        uncheckedThumbColor = Color.White,
                        uncheckedTrackColor = Color(0xFFccc)
                    )
                )
            }
            ControlType.Selector -> {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = selectedValue,
                        fontSize = 14.sp,
                        color = Color(0xFF6b7280),
                        modifier = Modifier.padding(end = 4.dp)
                    )
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowRight,
                        contentDescription = "选择",
                        modifier = Modifier.size(16.dp),
                        tint = Color(0xFF9ca3af)
                    )
                }
            }
            ControlType.Arrow -> {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "进入",
                    modifier = Modifier.size(20.dp),
                    tint = Color(0xFF9ca3af)
                )
            }
            ControlType.None -> {
                // 无控件
            }
        }
    }
}

/**
 * 设置项分组组件
 * 
 * @param title 分组标题
 * @param items 设置项列表
 * @param modifier 修饰符
 */
@Composable
fun SettingGroup(
    title: String,
    items: List<SettingItemData>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp
        )
    ) {
        Column {
            // 分组标题
            if (title.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color(0xFFf9fafb))
                        .padding(horizontal = 20.dp, vertical = 12.dp)
                ) {
                    Text(
                        text = title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF374151)
                    )
                }
            }
            
            // 设置项列表
            items.forEachIndexed { index, item ->
                SettingItem(
                    icon = item.icon,
                    title = item.title,
                    description = item.description,
                    iconBackgroundColor = item.iconBackgroundColor,
                    controlType = item.controlType,
                    isChecked = item.isChecked,
                    selectedValue = item.selectedValue,
                    onCheckedChange = item.onCheckedChange,
                    onClick = item.onClick
                )
                
                // 分割线
                if (index < items.size - 1) {
                    Divider(
                        modifier = Modifier.padding(horizontal = 20.dp),
                        color = Color(0xFFf3f4f6),
                        thickness = 1.dp
                    )
                }
            }
        }
    }
}

/**
 * 控件类型枚举
 */
enum class ControlType {
    Switch,    // 开关
    Selector,  // 选择器
    Arrow,     // 箭头
    None       // 无控件
}

/**
 * 设置项数据类
 */
data class SettingItemData(
    val icon: ImageVector,
    val title: String,
    val description: String,
    val iconBackgroundColor: Color = Color(0xFF667eea),
    val controlType: ControlType = ControlType.Arrow,
    val isChecked: Boolean = false,
    val selectedValue: String = "",
    val onCheckedChange: (Boolean) -> Unit = {},
    val onClick: () -> Unit = {}
)

// 预览组件
@Composable
fun SettingItemPreview() {
    ShuimuTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 通用设置组
            SettingGroup(
                title = "通用设置",
                items = listOf(
                    SettingItemData(
                        icon = Icons.Default.KeyboardArrowRight,
                        title = "自动播放",
                        description = "视频播放完成后自动播放下一个",
                        controlType = ControlType.Switch,
                        isChecked = true
                    ),
                    SettingItemData(
                        icon = Icons.Default.KeyboardArrowRight,
                        title = "播放质量",
                        description = "选择默认播放质量",
                        controlType = ControlType.Selector,
                        selectedValue = "高清"
                    ),
                    SettingItemData(
                        icon = Icons.Default.KeyboardArrowRight,
                        title = "缓存位置",
                        description = "当前缓存位置",
                        controlType = ControlType.Arrow
                    )
                )
            )
            
            // 账户设置组
            SettingGroup(
                title = "账户设置",
                items = listOf(
                    SettingItemData(
                        icon = Icons.Default.KeyboardArrowRight,
                        title = "修改密码",
                        controlType = ControlType.Arrow
                    ),
                    SettingItemData(
                        icon = Icons.Default.KeyboardArrowRight,
                        title = "绑定手机",
                        description = "已绑定：138****8888",
                        controlType = ControlType.Arrow
                    ),
                    SettingItemData(
                        icon = Icons.Default.KeyboardArrowRight,
                        title = "注销账户",
                        description = "永久删除账户和所有数据",
                        controlType = ControlType.Arrow
                    )
                )
            )
        }
    }
} 