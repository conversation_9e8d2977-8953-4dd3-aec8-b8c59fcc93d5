package com.shuimu.videocourse.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.shuimu.videocourse.data.local.entity.CourseEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class CourseDao_Impl implements CourseDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CourseEntity> __insertionAdapterOfCourseEntity;

  private final EntityDeletionOrUpdateAdapter<CourseEntity> __deletionAdapterOfCourseEntity;

  private final EntityDeletionOrUpdateAdapter<CourseEntity> __updateAdapterOfCourseEntity;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCourseStats;

  private final SharedSQLiteStatement __preparedStmtOfClearAllCourses;

  public CourseDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCourseEntity = new EntityInsertionAdapter<CourseEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `courses` (`courseId`,`title`,`description`,`coverImage`,`instructor`,`instructorAvatar`,`category`,`tags`,`price`,`originalPrice`,`discount`,`duration`,`videoCount`,`difficulty`,`rating`,`ratingCount`,`studentCount`,`isHot`,`isNew`,`isFree`,`status`,`publishTime`,`createTime`,`updateTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CourseEntity entity) {
        statement.bindString(1, entity.getCourseId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getDescription());
        if (entity.getCoverImage() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCoverImage());
        }
        statement.bindString(5, entity.getInstructor());
        if (entity.getInstructorAvatar() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getInstructorAvatar());
        }
        statement.bindString(7, entity.getCategory());
        statement.bindString(8, entity.getTags());
        statement.bindDouble(9, entity.getPrice());
        statement.bindDouble(10, entity.getOriginalPrice());
        statement.bindDouble(11, entity.getDiscount());
        statement.bindLong(12, entity.getDuration());
        statement.bindLong(13, entity.getVideoCount());
        statement.bindString(14, entity.getDifficulty());
        statement.bindDouble(15, entity.getRating());
        statement.bindLong(16, entity.getRatingCount());
        statement.bindLong(17, entity.getStudentCount());
        final int _tmp = entity.isHot() ? 1 : 0;
        statement.bindLong(18, _tmp);
        final int _tmp_1 = entity.isNew() ? 1 : 0;
        statement.bindLong(19, _tmp_1);
        final int _tmp_2 = entity.isFree() ? 1 : 0;
        statement.bindLong(20, _tmp_2);
        statement.bindString(21, entity.getStatus());
        statement.bindLong(22, entity.getPublishTime());
        statement.bindLong(23, entity.getCreateTime());
        statement.bindLong(24, entity.getUpdateTime());
      }
    };
    this.__deletionAdapterOfCourseEntity = new EntityDeletionOrUpdateAdapter<CourseEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `courses` WHERE `courseId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CourseEntity entity) {
        statement.bindString(1, entity.getCourseId());
      }
    };
    this.__updateAdapterOfCourseEntity = new EntityDeletionOrUpdateAdapter<CourseEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `courses` SET `courseId` = ?,`title` = ?,`description` = ?,`coverImage` = ?,`instructor` = ?,`instructorAvatar` = ?,`category` = ?,`tags` = ?,`price` = ?,`originalPrice` = ?,`discount` = ?,`duration` = ?,`videoCount` = ?,`difficulty` = ?,`rating` = ?,`ratingCount` = ?,`studentCount` = ?,`isHot` = ?,`isNew` = ?,`isFree` = ?,`status` = ?,`publishTime` = ?,`createTime` = ?,`updateTime` = ? WHERE `courseId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CourseEntity entity) {
        statement.bindString(1, entity.getCourseId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getDescription());
        if (entity.getCoverImage() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCoverImage());
        }
        statement.bindString(5, entity.getInstructor());
        if (entity.getInstructorAvatar() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getInstructorAvatar());
        }
        statement.bindString(7, entity.getCategory());
        statement.bindString(8, entity.getTags());
        statement.bindDouble(9, entity.getPrice());
        statement.bindDouble(10, entity.getOriginalPrice());
        statement.bindDouble(11, entity.getDiscount());
        statement.bindLong(12, entity.getDuration());
        statement.bindLong(13, entity.getVideoCount());
        statement.bindString(14, entity.getDifficulty());
        statement.bindDouble(15, entity.getRating());
        statement.bindLong(16, entity.getRatingCount());
        statement.bindLong(17, entity.getStudentCount());
        final int _tmp = entity.isHot() ? 1 : 0;
        statement.bindLong(18, _tmp);
        final int _tmp_1 = entity.isNew() ? 1 : 0;
        statement.bindLong(19, _tmp_1);
        final int _tmp_2 = entity.isFree() ? 1 : 0;
        statement.bindLong(20, _tmp_2);
        statement.bindString(21, entity.getStatus());
        statement.bindLong(22, entity.getPublishTime());
        statement.bindLong(23, entity.getCreateTime());
        statement.bindLong(24, entity.getUpdateTime());
        statement.bindString(25, entity.getCourseId());
      }
    };
    this.__preparedStmtOfUpdateCourseStats = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE courses SET \n"
                + "        rating = ?,\n"
                + "        ratingCount = ?,\n"
                + "        studentCount = ?,\n"
                + "        updateTime = ?\n"
                + "        WHERE courseId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfClearAllCourses = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM courses";
        return _query;
      }
    };
  }

  @Override
  public Object insertCourse(final CourseEntity course,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCourseEntity.insert(course);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertCourses(final List<CourseEntity> courses,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCourseEntity.insert(courses);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCourse(final CourseEntity course,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfCourseEntity.handle(course);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCourse(final CourseEntity course,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCourseEntity.handle(course);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCourseStats(final String courseId, final float rating, final int ratingCount,
      final int studentCount, final long updateTime, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCourseStats.acquire();
        int _argIndex = 1;
        _stmt.bindDouble(_argIndex, rating);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, ratingCount);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, studentCount);
        _argIndex = 4;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 5;
        _stmt.bindString(_argIndex, courseId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateCourseStats.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAllCourses(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllCourses.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllCourses.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseById(final String courseId,
      final Continuation<? super CourseEntity> $completion) {
    final String _sql = "SELECT * FROM courses WHERE courseId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, courseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CourseEntity>() {
      @Override
      @Nullable
      public CourseEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final CourseEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<CourseEntity> observeCourseById(final String courseId) {
    final String _sql = "SELECT * FROM courses WHERE courseId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, courseId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"courses"}, new Callable<CourseEntity>() {
      @Override
      @Nullable
      public CourseEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final CourseEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllCourses(final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "SELECT * FROM courses ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CourseEntity>> observeAllCourses() {
    final String _sql = "SELECT * FROM courses ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"courses"}, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getCoursesByCategory(final String category,
      final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "SELECT * FROM courses WHERE category = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CourseEntity>> observeCoursesByCategory(final String category) {
    final String _sql = "SELECT * FROM courses WHERE category = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"courses"}, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object searchCourses(final String keyword,
      final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "\n"
            + "        SELECT * FROM courses \n"
            + "        WHERE title LIKE '%' || ? || '%' \n"
            + "        OR description LIKE '%' || ? || '%' \n"
            + "        OR instructor LIKE '%' || ? || '%'\n"
            + "        OR tags LIKE '%' || ? || '%'\n"
            + "        ORDER BY createTime DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindString(_argIndex, keyword);
    _argIndex = 2;
    _statement.bindString(_argIndex, keyword);
    _argIndex = 3;
    _statement.bindString(_argIndex, keyword);
    _argIndex = 4;
    _statement.bindString(_argIndex, keyword);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getHotCourses(final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "SELECT * FROM courses WHERE isHot = 1 ORDER BY studentCount DESC, createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getNewCourses(final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "SELECT * FROM courses WHERE isNew = 1 ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getFreeCourses(final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "SELECT * FROM courses WHERE isFree = 1 ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCoursesByPriceRange(final double minPrice, final double maxPrice,
      final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "SELECT * FROM courses WHERE price BETWEEN ? AND ? ORDER BY price ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minPrice);
    _argIndex = 2;
    _statement.bindDouble(_argIndex, maxPrice);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCoursesByRating(final float minRating,
      final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "SELECT * FROM courses WHERE rating >= ? ORDER BY rating DESC, ratingCount DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minRating);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecommendedCourses(final int limit,
      final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "\n"
            + "        SELECT * FROM courses \n"
            + "        WHERE rating >= 4.0 AND studentCount >= 100 \n"
            + "        ORDER BY (rating * 0.7 + (studentCount / 1000.0) * 0.3) DESC\n"
            + "        LIMIT ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCoverImage = CursorUtil.getColumnIndexOrThrow(_cursor, "coverImage");
          final int _cursorIndexOfInstructor = CursorUtil.getColumnIndexOrThrow(_cursor, "instructor");
          final int _cursorIndexOfInstructorAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "instructorAvatar");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfVideoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCount");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfRatingCount = CursorUtil.getColumnIndexOrThrow(_cursor, "ratingCount");
          final int _cursorIndexOfStudentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studentCount");
          final int _cursorIndexOfIsHot = CursorUtil.getColumnIndexOrThrow(_cursor, "isHot");
          final int _cursorIndexOfIsNew = CursorUtil.getColumnIndexOrThrow(_cursor, "isNew");
          final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "isFree");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPublishTime = CursorUtil.getColumnIndexOrThrow(_cursor, "publishTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCoverImage;
            if (_cursor.isNull(_cursorIndexOfCoverImage)) {
              _tmpCoverImage = null;
            } else {
              _tmpCoverImage = _cursor.getString(_cursorIndexOfCoverImage);
            }
            final String _tmpInstructor;
            _tmpInstructor = _cursor.getString(_cursorIndexOfInstructor);
            final String _tmpInstructorAvatar;
            if (_cursor.isNull(_cursorIndexOfInstructorAvatar)) {
              _tmpInstructorAvatar = null;
            } else {
              _tmpInstructorAvatar = _cursor.getString(_cursorIndexOfInstructorAvatar);
            }
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final int _tmpVideoCount;
            _tmpVideoCount = _cursor.getInt(_cursorIndexOfVideoCount);
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final float _tmpRating;
            _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            final int _tmpRatingCount;
            _tmpRatingCount = _cursor.getInt(_cursorIndexOfRatingCount);
            final int _tmpStudentCount;
            _tmpStudentCount = _cursor.getInt(_cursorIndexOfStudentCount);
            final boolean _tmpIsHot;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsHot);
            _tmpIsHot = _tmp != 0;
            final boolean _tmpIsNew;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsNew);
            _tmpIsNew = _tmp_1 != 0;
            final boolean _tmpIsFree;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsFree);
            _tmpIsFree = _tmp_2 != 0;
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final long _tmpPublishTime;
            _tmpPublishTime = _cursor.getLong(_cursorIndexOfPublishTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CourseEntity(_tmpCourseId,_tmpTitle,_tmpDescription,_tmpCoverImage,_tmpInstructor,_tmpInstructorAvatar,_tmpCategory,_tmpTags,_tmpPrice,_tmpOriginalPrice,_tmpDiscount,_tmpDuration,_tmpVideoCount,_tmpDifficulty,_tmpRating,_tmpRatingCount,_tmpStudentCount,_tmpIsHot,_tmpIsNew,_tmpIsFree,_tmpStatus,_tmpPublishTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM courses";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
