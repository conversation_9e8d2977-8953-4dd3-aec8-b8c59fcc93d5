package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.UserRepository;
import com.shuimu.videocourse.domain.usecase.user.GetCurrentUserUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideGetCurrentUserUseCaseFactory implements Factory<GetCurrentUserUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public DomainModule_ProvideGetCurrentUserUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetCurrentUserUseCase get() {
    return provideGetCurrentUserUseCase(userRepositoryProvider.get());
  }

  public static DomainModule_ProvideGetCurrentUserUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new DomainModule_ProvideGetCurrentUserUseCaseFactory(userRepositoryProvider);
  }

  public static GetCurrentUserUseCase provideGetCurrentUserUseCase(UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideGetCurrentUserUseCase(userRepository));
  }
}
