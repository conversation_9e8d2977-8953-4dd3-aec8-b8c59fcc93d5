package com.shuimu.videocourse.presentation.components.video

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Fullscreen
import androidx.compose.material.icons.filled.FullscreenExit
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.shuimu.videocourse.domain.cache.CacheQuality
import com.shuimu.videocourse.domain.cache.CacheStatus
import com.shuimu.videocourse.domain.cache.VideoCacheManager
import com.shuimu.videocourse.presentation.components.basic.ProgressBar
import com.shuimu.videocourse.presentation.components.basic.ShuimuButton
import com.shuimu.videocourse.presentation.components.basic.LoadingIndicator

/**
 * 视频播放状态
 */
enum class VideoPlayState {
    IDLE,       // 空闲状态
    LOADING,    // 加载中
    READY,      // 准备就绪
    PLAYING,    // 播放中
    PAUSED,     // 暂停
    ERROR       // 错误
}

/**
 * 视频播放数据类
 */
data class VideoPlayerData(
    val videoId: String,
    val title: String,
    val videoUrl: String,
    val duration: Long = 0L,
    val currentPosition: Long = 0L,
    val isPlaying: Boolean = false,
    val isBuffering: Boolean = false,
    val cacheStatus: CacheStatus = CacheStatus.NOT_CACHED,
    val selectedQuality: CacheQuality = CacheQuality.HIGH,
    val availableQualities: List<CacheQuality> = CacheQuality.values().toList()
)

/**
 * 水幕视频课程App - 视频播放器组件
 * 
 * 基于原型设计：UI Prototype/02-视频播放页.html 视频播放器区域
 * 
 * 核心功能：
 * - 集成ExoPlayer进行视频播放
 * - 支持在线播放和缓存播放
 * - 提供播放控制器界面
 * - 支持多种清晰度切换
 * - 集成缓存下载功能
 * - 全屏播放支持
 * - 播放进度记录
 * 
 * 技术特性：
 * - 使用ExoPlayer作为播放引擎
 * - 集成VideoCacheManager进行缓存管理
 * - 响应式UI设计
 * - 自动播放状态管理
 * 
 * @param videoData 视频数据
 * @param cacheManager 缓存管理器
 * @param onPlayPause 播放/暂停回调
 * @param onSeek 进度跳转回调
 * @param onQualityChange 清晰度变化回调
 * @param onFullscreenToggle 全屏切换回调
 * @param onCacheToggle 缓存切换回调
 * @param modifier 修饰符
 */
@Composable
fun VideoPlayer(
    videoData: VideoPlayerData,
    cacheManager: VideoCacheManager? = null,
    onPlayPause: () -> Unit = {},
    onSeek: (Long) -> Unit = {},
    onQualityChange: (CacheQuality) -> Unit = {},
    onFullscreenToggle: () -> Unit = {},
    onCacheToggle: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var showControls by remember { mutableStateOf(true) }
    var showQualitySelector by remember { mutableStateOf(false) }
    var exoPlayer by remember { mutableStateOf<ExoPlayer?>(null) }
    
    // 初始化ExoPlayer
    LaunchedEffect(videoData.videoId) {
        exoPlayer?.release()
        exoPlayer = ExoPlayer.Builder(context).build().apply {
            // 检查是否有缓存版本
            val videoUrl = if (videoData.cacheStatus == CacheStatus.CACHED) {
                cacheManager?.getCachedVideoPath(videoData.videoId) ?: videoData.videoUrl
            } else {
                videoData.videoUrl
            }
            
            val mediaItem = MediaItem.fromUri(videoUrl)
            setMediaItem(mediaItem)
            prepare()
            
            // 设置播放监听器
            addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    // 处理播放状态变化
                }
                
                override fun onIsPlayingChanged(isPlaying: Boolean) {
                    // 处理播放/暂停状态变化
                }
            })
        }
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            exoPlayer?.release()
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(16f / 9f)
            .clip(RoundedCornerShape(12.dp))
            .background(Color.Black)
            .clickable { showControls = !showControls }
    ) {
        // ExoPlayer视图
        exoPlayer?.let { player ->
            AndroidView(
                factory = { context ->
                    PlayerView(context).apply {
                        this.player = player
                        useController = false // 使用自定义控制器
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // 加载指示器
        if (videoData.isBuffering) {
            LoadingIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
        
        // 播放控制器
        if (showControls) {
            VideoControls(
                controlsData = VideoControlsData(
                    isPlaying = videoData.isPlaying,
                    currentPosition = videoData.currentPosition,
                    duration = videoData.duration,
                    playbackSpeed = PlaybackSpeed.SPEED_1_0,
                    isFullscreen = false,
                    isMuted = false,
                    volume = 1.0f
                ),
                onPlayPause = {
                    if (videoData.isPlaying) {
                        exoPlayer?.pause()
                    } else {
                        exoPlayer?.play()
                    }
                    onPlayPause()
                },
                onSeek = { position ->
                    exoPlayer?.seekTo(position)
                    onSeek(position)
                },
                onSpeedChange = { speed ->
                    exoPlayer?.setPlaybackSpeed(speed.value)
                },
                onFullscreenToggle = onFullscreenToggle,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
        
        // 顶部工具栏
        VideoTopBar(
            title = videoData.title,
            cacheStatus = videoData.cacheStatus,
            selectedQuality = videoData.selectedQuality,
            onQualityClick = { showQualitySelector = true },
            onCacheClick = onCacheToggle,
            modifier = Modifier.align(Alignment.TopCenter)
        )
        
        // 清晰度选择器
        if (showQualitySelector) {
            QualitySelector(
                availableQualities = videoData.availableQualities,
                selectedQuality = videoData.selectedQuality,
                onQualitySelected = { quality ->
                    onQualityChange(quality)
                    showQualitySelector = false
                },
                onDismiss = { showQualitySelector = false },
                modifier = Modifier.align(Alignment.TopEnd)
            )
        }
    }
}

/**
 * 视频顶部工具栏
 */
@Composable
private fun VideoTopBar(
    title: String,
    cacheStatus: CacheStatus,
    selectedQuality: CacheQuality,
    onQualityClick: () -> Unit,
    onCacheClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.6f),
                RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
            )
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 视频标题
        Text(
            text = title,
            color = Color.White,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(1f)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 清晰度按钮
        ShuimuButton(
            text = selectedQuality.label,
            onClick = onQualityClick,
            type = com.shuimu.videocourse.presentation.components.basic.ButtonType.OUTLINE,
            size = com.shuimu.videocourse.presentation.components.basic.ButtonSize.SMALL,
            modifier = Modifier.height(28.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 缓存按钮
        IconButton(
            onClick = onCacheClick,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = when (cacheStatus) {
                    CacheStatus.CACHED -> Icons.Default.CloudDone
                    CacheStatus.DOWNLOADING -> Icons.Default.CloudDownload
                    CacheStatus.ERROR -> Icons.Default.CloudOff
                    else -> Icons.Default.CloudQueue
                },
                contentDescription = when (cacheStatus) {
                    CacheStatus.CACHED -> "已缓存"
                    CacheStatus.DOWNLOADING -> "下载中"
                    CacheStatus.ERROR -> "缓存失败"
                    else -> "开始缓存"
                },
                tint = when (cacheStatus) {
                    CacheStatus.CACHED -> Color.Green
                    CacheStatus.DOWNLOADING -> Color.Blue
                    CacheStatus.ERROR -> Color.Red
                    else -> Color.White
                },
                modifier = Modifier.size(18.dp)
            )
        }
    }
}

/**
 * 清晰度选择器
 */
@Composable
private fun QualitySelector(
    availableQualities: List<CacheQuality>,
    selectedQuality: CacheQuality,
    onQualitySelected: (CacheQuality) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(120.dp)
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.9f)
        ),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Text(
                text = "清晰度",
                color = Color.White,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(8.dp)
            )
            
            availableQualities.forEach { quality ->
                QualityOptionItem(
                    quality = quality,
                    isSelected = quality == selectedQuality,
                    onClick = { onQualitySelected(quality) },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * 清晰度选项项
 */
@Composable
private fun QualityOptionItem(
    quality: CacheQuality,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .clickable { onClick() }
            .padding(12.dp, 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = quality.label,
                color = if (isSelected) Color(0xFF667EEA) else Color.White,
                fontSize = 14.sp,
                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal
            )
            Text(
                text = quality.resolution,
                color = Color.White.copy(alpha = 0.7f),
                fontSize = 11.sp
            )
        }
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "已选择",
                tint = Color(0xFF667EEA),
                modifier = Modifier.size(16.dp)
            )
        }
    }
} 