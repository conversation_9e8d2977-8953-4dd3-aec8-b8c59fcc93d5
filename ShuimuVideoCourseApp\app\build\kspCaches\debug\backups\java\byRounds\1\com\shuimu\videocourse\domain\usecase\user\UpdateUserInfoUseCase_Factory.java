package com.shuimu.videocourse.domain.usecase.user;

import com.shuimu.videocourse.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UpdateUserInfoUseCase_Factory implements Factory<UpdateUserInfoUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UpdateUserInfoUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UpdateUserInfoUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static UpdateUserInfoUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UpdateUserInfoUseCase_Factory(userRepositoryProvider);
  }

  public static UpdateUserInfoUseCase newInstance(UserRepository userRepository) {
    return new UpdateUserInfoUseCase(userRepository);
  }
}
