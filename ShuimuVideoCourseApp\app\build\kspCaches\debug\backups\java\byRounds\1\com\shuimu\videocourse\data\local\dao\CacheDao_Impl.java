package com.shuimu.videocourse.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.shuimu.videocourse.data.local.entity.CacheEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class CacheDao_Impl implements CacheDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CacheEntity> __insertionAdapterOfCacheEntity;

  private final EntityDeletionOrUpdateAdapter<CacheEntity> __deletionAdapterOfCacheEntity;

  private final EntityDeletionOrUpdateAdapter<CacheEntity> __updateAdapterOfCacheEntity;

  private final SharedSQLiteStatement __preparedStmtOfUpdateDownloadProgress;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCacheStatus;

  private final SharedSQLiteStatement __preparedStmtOfMarkDownloadStarted;

  private final SharedSQLiteStatement __preparedStmtOfMarkDownloadCompleted;

  private final SharedSQLiteStatement __preparedStmtOfMarkDownloadFailed;

  private final SharedSQLiteStatement __preparedStmtOfPauseDownload;

  private final SharedSQLiteStatement __preparedStmtOfResumeDownload;

  private final SharedSQLiteStatement __preparedStmtOfResetRetryCount;

  private final SharedSQLiteStatement __preparedStmtOfClearExpiredCaches;

  private final SharedSQLiteStatement __preparedStmtOfClearFailedCaches;

  private final SharedSQLiteStatement __preparedStmtOfClearCachesByCourse;

  private final SharedSQLiteStatement __preparedStmtOfClearAllCaches;

  public CacheDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCacheEntity = new EntityInsertionAdapter<CacheEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `cache` (`cacheId`,`videoId`,`courseId`,`localPath`,`fileName`,`fileSize`,`downloadedSize`,`downloadProgress`,`status`,`quality`,`resolution`,`downloadSpeed`,`remainingTime`,`retryCount`,`errorMessage`,`downloadUrl`,`expiryTime`,`isWifiOnly`,`startTime`,`completeTime`,`createTime`,`updateTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CacheEntity entity) {
        statement.bindString(1, entity.getCacheId());
        statement.bindString(2, entity.getVideoId());
        statement.bindString(3, entity.getCourseId());
        statement.bindString(4, entity.getLocalPath());
        statement.bindString(5, entity.getFileName());
        statement.bindLong(6, entity.getFileSize());
        statement.bindLong(7, entity.getDownloadedSize());
        statement.bindDouble(8, entity.getDownloadProgress());
        statement.bindString(9, entity.getStatus());
        statement.bindString(10, entity.getQuality());
        statement.bindString(11, entity.getResolution());
        statement.bindLong(12, entity.getDownloadSpeed());
        statement.bindLong(13, entity.getRemainingTime());
        statement.bindLong(14, entity.getRetryCount());
        if (entity.getErrorMessage() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getErrorMessage());
        }
        statement.bindString(16, entity.getDownloadUrl());
        if (entity.getExpiryTime() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getExpiryTime());
        }
        final int _tmp = entity.isWifiOnly() ? 1 : 0;
        statement.bindLong(18, _tmp);
        if (entity.getStartTime() == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, entity.getStartTime());
        }
        if (entity.getCompleteTime() == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, entity.getCompleteTime());
        }
        statement.bindLong(21, entity.getCreateTime());
        statement.bindLong(22, entity.getUpdateTime());
      }
    };
    this.__deletionAdapterOfCacheEntity = new EntityDeletionOrUpdateAdapter<CacheEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `cache` WHERE `cacheId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CacheEntity entity) {
        statement.bindString(1, entity.getCacheId());
      }
    };
    this.__updateAdapterOfCacheEntity = new EntityDeletionOrUpdateAdapter<CacheEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `cache` SET `cacheId` = ?,`videoId` = ?,`courseId` = ?,`localPath` = ?,`fileName` = ?,`fileSize` = ?,`downloadedSize` = ?,`downloadProgress` = ?,`status` = ?,`quality` = ?,`resolution` = ?,`downloadSpeed` = ?,`remainingTime` = ?,`retryCount` = ?,`errorMessage` = ?,`downloadUrl` = ?,`expiryTime` = ?,`isWifiOnly` = ?,`startTime` = ?,`completeTime` = ?,`createTime` = ?,`updateTime` = ? WHERE `cacheId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CacheEntity entity) {
        statement.bindString(1, entity.getCacheId());
        statement.bindString(2, entity.getVideoId());
        statement.bindString(3, entity.getCourseId());
        statement.bindString(4, entity.getLocalPath());
        statement.bindString(5, entity.getFileName());
        statement.bindLong(6, entity.getFileSize());
        statement.bindLong(7, entity.getDownloadedSize());
        statement.bindDouble(8, entity.getDownloadProgress());
        statement.bindString(9, entity.getStatus());
        statement.bindString(10, entity.getQuality());
        statement.bindString(11, entity.getResolution());
        statement.bindLong(12, entity.getDownloadSpeed());
        statement.bindLong(13, entity.getRemainingTime());
        statement.bindLong(14, entity.getRetryCount());
        if (entity.getErrorMessage() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getErrorMessage());
        }
        statement.bindString(16, entity.getDownloadUrl());
        if (entity.getExpiryTime() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getExpiryTime());
        }
        final int _tmp = entity.isWifiOnly() ? 1 : 0;
        statement.bindLong(18, _tmp);
        if (entity.getStartTime() == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, entity.getStartTime());
        }
        if (entity.getCompleteTime() == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, entity.getCompleteTime());
        }
        statement.bindLong(21, entity.getCreateTime());
        statement.bindLong(22, entity.getUpdateTime());
        statement.bindString(23, entity.getCacheId());
      }
    };
    this.__preparedStmtOfUpdateDownloadProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE cache SET \n"
                + "        downloadedSize = ?,\n"
                + "        downloadProgress = ?,\n"
                + "        downloadSpeed = ?,\n"
                + "        remainingTime = ?,\n"
                + "        updateTime = ?\n"
                + "        WHERE cacheId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateCacheStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE cache SET status = ?, updateTime = ? WHERE cacheId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkDownloadStarted = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE cache SET \n"
                + "        status = 'downloading',\n"
                + "        startTime = ?,\n"
                + "        updateTime = ?\n"
                + "        WHERE cacheId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfMarkDownloadCompleted = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE cache SET \n"
                + "        status = 'completed',\n"
                + "        downloadProgress = 100.0,\n"
                + "        completeTime = ?,\n"
                + "        updateTime = ?\n"
                + "        WHERE cacheId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfMarkDownloadFailed = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE cache SET \n"
                + "        status = 'failed',\n"
                + "        errorMessage = ?,\n"
                + "        retryCount = retryCount + 1,\n"
                + "        updateTime = ?\n"
                + "        WHERE cacheId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfPauseDownload = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE cache SET status = 'paused', updateTime = ? WHERE cacheId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfResumeDownload = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE cache SET status = 'downloading', updateTime = ? WHERE cacheId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfResetRetryCount = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE cache SET retryCount = 0, updateTime = ? WHERE cacheId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearExpiredCaches = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cache WHERE expiryTime IS NOT NULL AND expiryTime < ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearFailedCaches = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cache WHERE status = 'failed'";
        return _query;
      }
    };
    this.__preparedStmtOfClearCachesByCourse = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cache WHERE courseId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAllCaches = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cache";
        return _query;
      }
    };
  }

  @Override
  public Object insertCache(final CacheEntity cache, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCacheEntity.insert(cache);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertCaches(final List<CacheEntity> caches,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCacheEntity.insert(caches);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCache(final CacheEntity cache, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfCacheEntity.handle(cache);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCache(final CacheEntity cache, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCacheEntity.handle(cache);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateDownloadProgress(final String cacheId, final long downloadedSize,
      final float downloadProgress, final long downloadSpeed, final long remainingTime,
      final long updateTime, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateDownloadProgress.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, downloadedSize);
        _argIndex = 2;
        _stmt.bindDouble(_argIndex, downloadProgress);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, downloadSpeed);
        _argIndex = 4;
        _stmt.bindLong(_argIndex, remainingTime);
        _argIndex = 5;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 6;
        _stmt.bindString(_argIndex, cacheId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateDownloadProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCacheStatus(final String cacheId, final String status, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCacheStatus.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, status);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 3;
        _stmt.bindString(_argIndex, cacheId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateCacheStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markDownloadStarted(final String cacheId, final long startTime,
      final long updateTime, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkDownloadStarted.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, startTime);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 3;
        _stmt.bindString(_argIndex, cacheId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkDownloadStarted.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markDownloadCompleted(final String cacheId, final long completeTime,
      final long updateTime, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkDownloadCompleted.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, completeTime);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 3;
        _stmt.bindString(_argIndex, cacheId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkDownloadCompleted.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markDownloadFailed(final String cacheId, final String errorMessage,
      final long updateTime, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkDownloadFailed.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, errorMessage);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 3;
        _stmt.bindString(_argIndex, cacheId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkDownloadFailed.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object pauseDownload(final String cacheId, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfPauseDownload.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 2;
        _stmt.bindString(_argIndex, cacheId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfPauseDownload.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object resumeDownload(final String cacheId, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfResumeDownload.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 2;
        _stmt.bindString(_argIndex, cacheId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfResumeDownload.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object resetRetryCount(final String cacheId, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfResetRetryCount.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 2;
        _stmt.bindString(_argIndex, cacheId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfResetRetryCount.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearExpiredCaches(final long currentTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearExpiredCaches.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, currentTime);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearExpiredCaches.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearFailedCaches(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearFailedCaches.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearFailedCaches.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearCachesByCourse(final String courseId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearCachesByCourse.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, courseId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearCachesByCourse.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAllCaches(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllCaches.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllCaches.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getCacheById(final String cacheId,
      final Continuation<? super CacheEntity> $completion) {
    final String _sql = "SELECT * FROM cache WHERE cacheId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, cacheId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CacheEntity>() {
      @Override
      @Nullable
      public CacheEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final CacheEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCacheByVideoId(final String videoId,
      final Continuation<? super CacheEntity> $completion) {
    final String _sql = "SELECT * FROM cache WHERE videoId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, videoId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CacheEntity>() {
      @Override
      @Nullable
      public CacheEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final CacheEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<CacheEntity> observeCacheByVideoId(final String videoId) {
    final String _sql = "SELECT * FROM cache WHERE videoId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, videoId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache"}, new Callable<CacheEntity>() {
      @Override
      @Nullable
      public CacheEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final CacheEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllCaches(final Continuation<? super List<CacheEntity>> $completion) {
    final String _sql = "SELECT * FROM cache ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CacheEntity>> observeAllCaches() {
    final String _sql = "SELECT * FROM cache ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache"}, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getCachesByCourseId(final String courseId,
      final Continuation<? super List<CacheEntity>> $completion) {
    final String _sql = "SELECT * FROM cache WHERE courseId = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, courseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CacheEntity>> observeCachesByCourseId(final String courseId) {
    final String _sql = "SELECT * FROM cache WHERE courseId = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, courseId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache"}, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getCachesByStatus(final String status,
      final Continuation<? super List<CacheEntity>> $completion) {
    final String _sql = "SELECT * FROM cache WHERE status = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, status);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getDownloadingCaches(final Continuation<? super List<CacheEntity>> $completion) {
    final String _sql = "SELECT * FROM cache WHERE status = 'downloading' ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CacheEntity>> observeDownloadingCaches() {
    final String _sql = "SELECT * FROM cache WHERE status = 'downloading' ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache"}, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getCompletedCaches(final Continuation<? super List<CacheEntity>> $completion) {
    final String _sql = "SELECT * FROM cache WHERE status = 'completed' ORDER BY completeTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getFailedCaches(final Continuation<? super List<CacheEntity>> $completion) {
    final String _sql = "SELECT * FROM cache WHERE status = 'failed' ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPausedCaches(final Continuation<? super List<CacheEntity>> $completion) {
    final String _sql = "SELECT * FROM cache WHERE status = 'paused' ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCachesByQuality(final String quality,
      final Continuation<? super List<CacheEntity>> $completion) {
    final String _sql = "SELECT * FROM cache WHERE quality = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, quality);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CacheEntity>>() {
      @Override
      @NonNull
      public List<CacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheId = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheId");
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "videoId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "localPath");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfDownloadedSize = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedSize");
          final int _cursorIndexOfDownloadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadProgress");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfResolution = CursorUtil.getColumnIndexOrThrow(_cursor, "resolution");
          final int _cursorIndexOfDownloadSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadSpeed");
          final int _cursorIndexOfRemainingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingTime");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfDownloadUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadUrl");
          final int _cursorIndexOfExpiryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiryTime");
          final int _cursorIndexOfIsWifiOnly = CursorUtil.getColumnIndexOrThrow(_cursor, "isWifiOnly");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfCompleteTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completeTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<CacheEntity> _result = new ArrayList<CacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheEntity _item;
            final String _tmpCacheId;
            _tmpCacheId = _cursor.getString(_cursorIndexOfCacheId);
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpLocalPath;
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            final String _tmpFileName;
            _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            final float _tmpDownloadProgress;
            _tmpDownloadProgress = _cursor.getFloat(_cursorIndexOfDownloadProgress);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpResolution;
            _tmpResolution = _cursor.getString(_cursorIndexOfResolution);
            final long _tmpDownloadSpeed;
            _tmpDownloadSpeed = _cursor.getLong(_cursorIndexOfDownloadSpeed);
            final long _tmpRemainingTime;
            _tmpRemainingTime = _cursor.getLong(_cursorIndexOfRemainingTime);
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpDownloadUrl;
            _tmpDownloadUrl = _cursor.getString(_cursorIndexOfDownloadUrl);
            final Long _tmpExpiryTime;
            if (_cursor.isNull(_cursorIndexOfExpiryTime)) {
              _tmpExpiryTime = null;
            } else {
              _tmpExpiryTime = _cursor.getLong(_cursorIndexOfExpiryTime);
            }
            final boolean _tmpIsWifiOnly;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsWifiOnly);
            _tmpIsWifiOnly = _tmp != 0;
            final Long _tmpStartTime;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmpStartTime = null;
            } else {
              _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Long _tmpCompleteTime;
            if (_cursor.isNull(_cursorIndexOfCompleteTime)) {
              _tmpCompleteTime = null;
            } else {
              _tmpCompleteTime = _cursor.getLong(_cursorIndexOfCompleteTime);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new CacheEntity(_tmpCacheId,_tmpVideoId,_tmpCourseId,_tmpLocalPath,_tmpFileName,_tmpFileSize,_tmpDownloadedSize,_tmpDownloadProgress,_tmpStatus,_tmpQuality,_tmpResolution,_tmpDownloadSpeed,_tmpRemainingTime,_tmpRetryCount,_tmpErrorMessage,_tmpDownloadUrl,_tmpExpiryTime,_tmpIsWifiOnly,_tmpStartTime,_tmpCompleteTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCacheStats(final Continuation<? super CacheStats> $completion) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            COUNT(*) as totalCaches,\n"
            + "            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedCaches,\n"
            + "            SUM(CASE WHEN status = 'downloading' THEN 1 ELSE 0 END) as downloadingCaches,\n"
            + "            SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failedCaches,\n"
            + "            SUM(fileSize) as totalSize,\n"
            + "            SUM(downloadedSize) as downloadedSize\n"
            + "        FROM cache\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CacheStats>() {
      @Override
      @Nullable
      public CacheStats call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTotalCaches = 0;
          final int _cursorIndexOfCompletedCaches = 1;
          final int _cursorIndexOfDownloadingCaches = 2;
          final int _cursorIndexOfFailedCaches = 3;
          final int _cursorIndexOfTotalSize = 4;
          final int _cursorIndexOfDownloadedSize = 5;
          final CacheStats _result;
          if (_cursor.moveToFirst()) {
            final int _tmpTotalCaches;
            _tmpTotalCaches = _cursor.getInt(_cursorIndexOfTotalCaches);
            final int _tmpCompletedCaches;
            _tmpCompletedCaches = _cursor.getInt(_cursorIndexOfCompletedCaches);
            final int _tmpDownloadingCaches;
            _tmpDownloadingCaches = _cursor.getInt(_cursorIndexOfDownloadingCaches);
            final int _tmpFailedCaches;
            _tmpFailedCaches = _cursor.getInt(_cursorIndexOfFailedCaches);
            final long _tmpTotalSize;
            _tmpTotalSize = _cursor.getLong(_cursorIndexOfTotalSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            _result = new CacheStats(_tmpTotalCaches,_tmpCompletedCaches,_tmpDownloadingCaches,_tmpFailedCaches,_tmpTotalSize,_tmpDownloadedSize);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseCacheStats(final String courseId,
      final Continuation<? super CourseCacheStats> $completion) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            COUNT(*) as totalCaches,\n"
            + "            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedCaches,\n"
            + "            SUM(fileSize) as totalSize,\n"
            + "            SUM(downloadedSize) as downloadedSize\n"
            + "        FROM cache \n"
            + "        WHERE courseId = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, courseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CourseCacheStats>() {
      @Override
      @Nullable
      public CourseCacheStats call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTotalCaches = 0;
          final int _cursorIndexOfCompletedCaches = 1;
          final int _cursorIndexOfTotalSize = 2;
          final int _cursorIndexOfDownloadedSize = 3;
          final CourseCacheStats _result;
          if (_cursor.moveToFirst()) {
            final int _tmpTotalCaches;
            _tmpTotalCaches = _cursor.getInt(_cursorIndexOfTotalCaches);
            final int _tmpCompletedCaches;
            _tmpCompletedCaches = _cursor.getInt(_cursorIndexOfCompletedCaches);
            final long _tmpTotalSize;
            _tmpTotalSize = _cursor.getLong(_cursorIndexOfTotalSize);
            final long _tmpDownloadedSize;
            _tmpDownloadedSize = _cursor.getLong(_cursorIndexOfDownloadedSize);
            _result = new CourseCacheStats(_tmpTotalCaches,_tmpCompletedCaches,_tmpTotalSize,_tmpDownloadedSize);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
