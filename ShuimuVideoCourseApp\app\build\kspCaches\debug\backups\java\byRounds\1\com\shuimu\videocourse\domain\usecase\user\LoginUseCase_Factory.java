package com.shuimu.videocourse.domain.usecase.user;

import com.shuimu.videocourse.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LoginUseCase_Factory implements Factory<LoginUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public LoginUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public LoginUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static LoginUseCase_Factory create(Provider<UserRepository> userRepositoryProvider) {
    return new LoginUseCase_Factory(userRepositoryProvider);
  }

  public static LoginUseCase newInstance(UserRepository userRepository) {
    return new LoginUseCase(userRepository);
  }
}
