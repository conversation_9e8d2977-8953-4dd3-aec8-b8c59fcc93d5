package com.shuimu.videocourse.domain.usecase.course;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WatchVideoUseCase_Factory implements Factory<WatchVideoUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public WatchVideoUseCase_Factory(Provider<CourseRepository> courseRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public WatchVideoUseCase get() {
    return newInstance(courseRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static WatchVideoUseCase_Factory create(
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new WatchVideoUseCase_Factory(courseRepositoryProvider, userRepositoryProvider);
  }

  public static WatchVideoUseCase newInstance(CourseRepository courseRepository,
      UserRepository userRepository) {
    return new WatchVideoUseCase(courseRepository, userRepository);
  }
}
