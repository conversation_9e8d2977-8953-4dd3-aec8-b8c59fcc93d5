package com.shuimu.videocourse.presentation.components.state

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.basic.ShuimuButton
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 错误消息组件
 * 
 * 基于原型的错误消息设计
 * 
 * 功能特性：
 * - 错误信息显示和重试按钮
 * - 统一错误样式和图标文字组合
 * - 使用ShuimuButton作为重试按钮
 * - 支持不同错误类型和严重级别
 * - 响应式布局设计
 * 
 * @param message 错误消息
 * @param type 错误类型
 * @param showRetryButton 是否显示重试按钮
 * @param retryText 重试按钮文本
 * @param modifier 修饰符
 * @param onRetry 重试回调
 */
@Composable
fun ErrorMessage(
    message: String,
    type: ErrorType = ErrorType.General,
    showRetryButton: Boolean = true,
    retryText: String = "重试",
    modifier: Modifier = Modifier,
    onRetry: () -> Unit = {}
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = when (type) {
                ErrorType.Network -> Color(0xFFfef2f2)
                ErrorType.Server -> Color(0xFFfff7ed)
                ErrorType.Auth -> Color(0xFFfefce8)
                ErrorType.General -> Color(0xFFf8fafc)
            }
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 错误图标
            Text(
                text = when (type) {
                    ErrorType.Network -> "📡"
                    ErrorType.Server -> "⚠️"
                    ErrorType.Auth -> "🔐"
                    ErrorType.General -> "❌"
                },
                fontSize = 48.sp
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 错误标题
            Text(
                text = when (type) {
                    ErrorType.Network -> "网络连接失败"
                    ErrorType.Server -> "服务器错误"
                    ErrorType.Auth -> "身份验证失败"
                    ErrorType.General -> "出现错误"
                },
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                ),
                color = when (type) {
                    ErrorType.Network -> Color(0xFFdc2626)
                    ErrorType.Server -> Color(0xFFea580c)
                    ErrorType.Auth -> Color(0xFFca8a04)
                    ErrorType.General -> Color(0xFF64748b)
                },
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 错误消息
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp
                ),
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
            
            if (showRetryButton) {
                Spacer(modifier = Modifier.height(20.dp))
                
                // 重试按钮
                ShuimuButton(
                    text = retryText,
                    onClick = onRetry,
                    type = com.shuimu.videocourse.presentation.components.basic.ShuimuButtonType.PRIMARY,
                    modifier = Modifier.widthIn(min = 120.dp)
                )
            }
        }
    }
}

/**
 * 简化错误消息组件（内联样式）
 * 
 * @param message 错误消息
 * @param type 错误类型
 * @param modifier 修饰符
 */
@Composable
fun InlineErrorMessage(
    message: String,
    type: ErrorType = ErrorType.General,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = when (type) {
                    ErrorType.Network -> Color(0xFFfef2f2)
                    ErrorType.Server -> Color(0xFFfff7ed)
                    ErrorType.Auth -> Color(0xFFfefce8)
                    ErrorType.General -> Color(0xFFf8fafc)
                },
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 错误图标
        Text(
            text = when (type) {
                ErrorType.Network -> "📡"
                ErrorType.Server -> "⚠️"
                ErrorType.Auth -> "🔐"
                ErrorType.General -> "❌"
            },
            fontSize = 16.sp
        )
        
        // 错误消息
        Text(
            text = message,
            style = MaterialTheme.typography.bodySmall.copy(
                fontSize = 12.sp
            ),
            color = when (type) {
                ErrorType.Network -> Color(0xFFdc2626)
                ErrorType.Server -> Color(0xFFea580c)
                ErrorType.Auth -> Color(0xFFca8a04)
                ErrorType.General -> Color(0xFF64748b)
            },
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * 空状态错误组件
 * 
 * @param title 标题
 * @param message 消息
 * @param actionText 操作按钮文本
 * @param modifier 修饰符
 * @param onAction 操作回调
 */
@Composable
fun EmptyStateError(
    title: String = "暂无内容",
    message: String = "这里还没有任何内容",
    actionText: String? = null,
    modifier: Modifier = Modifier,
    onAction: () -> Unit = {}
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(40.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // 空状态图标
        Text(
            text = "📭",
            fontSize = 64.sp
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleLarge.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 20.sp
            ),
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 消息
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium.copy(
                fontSize = 14.sp
            ),
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
            textAlign = TextAlign.Center
        )
        
        if (actionText != null) {
            Spacer(modifier = Modifier.height(24.dp))
            
            // 操作按钮
                            ShuimuButton(
                    text = actionText,
                    onClick = onAction,
                    type = com.shuimu.videocourse.presentation.components.basic.ShuimuButtonType.SECONDARY,
                    modifier = Modifier.widthIn(min = 120.dp)
                )
        }
    }
}

/**
 * 错误类型枚举
 */
enum class ErrorType {
    Network,    // 网络错误
    Server,     // 服务器错误
    Auth,       // 认证错误
    General     // 一般错误
}

// 预览组件
@Composable
fun ErrorMessagePreview() {
    ShuimuTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 网络错误
            ErrorMessage(
                message = "请检查您的网络连接后重试",
                type = ErrorType.Network,
                onRetry = {}
            )
            
            // 服务器错误
            ErrorMessage(
                message = "服务器暂时无法响应，请稍后再试",
                type = ErrorType.Server,
                onRetry = {}
            )
            
            // 内联错误消息
            InlineErrorMessage(
                message = "用户名或密码错误",
                type = ErrorType.Auth
            )
            
            // 空状态
            EmptyStateError(
                title = "暂无课程",
                message = "您还没有购买任何课程",
                actionText = "去购买"
            )
        }
    }
} 