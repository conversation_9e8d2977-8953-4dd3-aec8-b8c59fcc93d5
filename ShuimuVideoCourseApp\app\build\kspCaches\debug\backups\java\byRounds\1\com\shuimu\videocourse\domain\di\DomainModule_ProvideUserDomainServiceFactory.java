package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.repository.UserRepository;
import com.shuimu.videocourse.domain.service.UserDomainService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideUserDomainServiceFactory implements Factory<UserDomainService> {
  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<CourseRepository> courseRepositoryProvider;

  public DomainModule_ProvideUserDomainServiceFactory(
      Provider<UserRepository> userRepositoryProvider,
      Provider<CourseRepository> courseRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public UserDomainService get() {
    return provideUserDomainService(userRepositoryProvider.get(), courseRepositoryProvider.get());
  }

  public static DomainModule_ProvideUserDomainServiceFactory create(
      Provider<UserRepository> userRepositoryProvider,
      Provider<CourseRepository> courseRepositoryProvider) {
    return new DomainModule_ProvideUserDomainServiceFactory(userRepositoryProvider, courseRepositoryProvider);
  }

  public static UserDomainService provideUserDomainService(UserRepository userRepository,
      CourseRepository courseRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideUserDomainService(userRepository, courseRepository));
  }
}
