package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.usecase.course.ManageCacheUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideManageCacheUseCaseFactory implements Factory<ManageCacheUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public DomainModule_ProvideManageCacheUseCaseFactory(
      Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public ManageCacheUseCase get() {
    return provideManageCacheUseCase(courseRepositoryProvider.get());
  }

  public static DomainModule_ProvideManageCacheUseCaseFactory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new DomainModule_ProvideManageCacheUseCaseFactory(courseRepositoryProvider);
  }

  public static ManageCacheUseCase provideManageCacheUseCase(CourseRepository courseRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideManageCacheUseCase(courseRepository));
  }
}
