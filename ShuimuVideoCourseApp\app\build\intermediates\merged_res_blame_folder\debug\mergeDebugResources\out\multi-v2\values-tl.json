{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,692,783,874,954,1039,1130,1208,1274,1375,1478,1545,1610,1672,1743,1861,1981,2101,2170,2257,2331,2411,2502,2593,2658,2722,2775,2833,2881,2942,3007,3069,3134,3202,3266,3324,3390,3454,3520,3572,3634,3713,3792", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "280,492,687,778,869,949,1034,1125,1203,1269,1370,1473,1540,1605,1667,1738,1856,1976,2096,2165,2252,2326,2406,2497,2588,2653,2717,2770,2828,2876,2937,3002,3064,3129,3197,3261,3319,3385,3449,3515,3567,3629,3708,3787,3844"}, "to": {"startLines": "2,11,15,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,547,8989,9080,9171,9251,9336,9427,9505,9571,9672,9775,9842,9907,9969,10040,10158,10278,10398,10467,10554,10628,10708,10799,10890,10955,11728,11781,11839,11887,11948,12013,12075,12140,12208,12272,12330,12396,12460,12526,12578,12640,12719,12798", "endLines": "10,14,18,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "330,542,737,9075,9166,9246,9331,9422,9500,9566,9667,9770,9837,9902,9964,10035,10153,10273,10393,10462,10549,10623,10703,10794,10885,10950,11014,11776,11834,11882,11943,12008,12070,12135,12203,12267,12325,12391,12455,12521,12573,12635,12714,12793,12850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,407,524,606,702,816,959,1082,1228,1309,1404,1495,1589,1706,1840,1940,2083,2227,2370,2536,2670,2789,2911,3033,3125,3220,3341,3472,3575,3675,3784,3924,4069,4181,4282,4354,4433,4518,4603,4706,4782,4862,4958,5058,5154,5256,5340,5448,5549,5654,5769,5845,5948", "endColumns": "119,117,113,116,81,95,113,142,122,145,80,94,90,93,116,133,99,142,143,142,165,133,118,121,121,91,94,120,130,102,99,108,139,144,111,100,71,78,84,84,102,75,79,95,99,95,101,83,107,100,104,114,75,102,90", "endOffsets": "170,288,402,519,601,697,811,954,1077,1223,1304,1399,1490,1584,1701,1835,1935,2078,2222,2365,2531,2665,2784,2906,3028,3120,3215,3336,3467,3570,3670,3779,3919,4064,4176,4277,4349,4428,4513,4598,4701,4777,4857,4953,5053,5149,5251,5335,5443,5544,5649,5764,5840,5943,6034"}, "to": {"startLines": "46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,146,155,158,160,164,165,166,167,168,169,170,171,172,173,174,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3482,3602,3720,3834,4870,4952,5048,5162,5305,5428,5574,5655,5750,5841,5935,6052,6186,6286,6429,6573,6716,6882,7016,7135,7257,7379,7471,7566,7687,7818,7921,8021,8130,8270,8415,8527,8827,12855,13596,13837,14023,14397,14473,14553,14649,14749,14845,14947,15031,15139,15240,15345,15460,15536,15639", "endColumns": "119,117,113,116,81,95,113,142,122,145,80,94,90,93,116,133,99,142,143,142,165,133,118,121,121,91,94,120,130,102,99,108,139,144,111,100,71,78,84,84,102,75,79,95,99,95,101,83,107,100,104,114,75,102,90", "endOffsets": "3597,3715,3829,3946,4947,5043,5157,5300,5423,5569,5650,5745,5836,5930,6047,6181,6281,6424,6568,6711,6877,7011,7130,7252,7374,7466,7561,7682,7813,7916,8016,8125,8265,8410,8522,8623,8894,12929,13676,13917,14121,14468,14548,14644,14744,14840,14942,15026,15134,15235,15340,15455,15531,15634,15725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "50,51,52,53,54,55,56,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3951,4048,4150,4251,4348,4455,4563,13922", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "4043,4145,4246,4343,4450,4558,4680,14018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,999,1068,1155,1241,1312,1390,1456", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,994,1063,1150,1236,1307,1385,1451,1578"}, "to": {"startLines": "57,58,91,92,94,147,148,149,150,151,152,153,154,157,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4685,4784,8628,8725,8899,12934,13016,13108,13200,13284,13354,13423,13510,13766,14126,14204,14270", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "4779,4865,8720,8822,8984,13011,13103,13195,13279,13349,13418,13505,13591,13832,14199,14265,14392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,853,961,1074,1162,1268,1383,1463,1540,1631,1724,1819,1913,2013,2106,2201,2295,2386,2477,2561,2670,2780,2881,2991,3109,3217,3380,13681", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "848,956,1069,1157,1263,1378,1458,1535,1626,1719,1814,1908,2008,2101,2196,2290,2381,2472,2556,2665,2775,2876,2986,3104,3212,3375,3477,13761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,196,264,330,410,488,588,686", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "127,191,259,325,405,483,583,681,759"}, "to": {"startLines": "119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11019,11096,11160,11228,11294,11374,11452,11552,11650", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "11091,11155,11223,11289,11369,11447,11547,11645,11723"}}]}]}