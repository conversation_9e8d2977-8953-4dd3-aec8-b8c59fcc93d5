package com.shuimu.videocourse.di;

import com.shuimu.videocourse.data.remote.api.CourseApiService;
import com.shuimu.videocourse.data.remote.network.NetworkConfig;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideCourseApiServiceFactory implements Factory<CourseApiService> {
  private final Provider<NetworkConfig> networkConfigProvider;

  public DataModule_ProvideCourseApiServiceFactory(Provider<NetworkConfig> networkConfigProvider) {
    this.networkConfigProvider = networkConfigProvider;
  }

  @Override
  public CourseApiService get() {
    return provideCourseApiService(networkConfigProvider.get());
  }

  public static DataModule_ProvideCourseApiServiceFactory create(
      Provider<NetworkConfig> networkConfigProvider) {
    return new DataModule_ProvideCourseApiServiceFactory(networkConfigProvider);
  }

  public static CourseApiService provideCourseApiService(NetworkConfig networkConfig) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideCourseApiService(networkConfig));
  }
}
