package com.shuimu.videocourse.presentation.components.basic

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsHoveredAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.dp
import com.shuimu.videocourse.presentation.theme.*

/**
 * 水幕卡片组件
 * 
 * 基于UI原型设计：UI Prototype/01-首页.html 第60-85行 .category-card 样式定义
 * 原型样式：
 * - 背景：white
 * - 圆角：border-radius: 12px
 * - 内边距：padding: 12px 0 16px 8px
 * - 外边距：margin-bottom: 8px
 * - 阴影：box-shadow: 0 2px 8px rgba(0,0,0,0.1)
 * - 左边框：border-left: 2px solid #667eea
 * - 悬停效果：background: #f8fafc, transform: translateX(4px)
 * - 过渡动画：transition: all 0.3s ease
 */

/**
 * 卡片类型枚举
 */
enum class ShuimuCardType {
    DEFAULT,    // 默认卡片 - 白色背景
    CATEGORY,   // 分类卡片 - 带左边框
    SERIES,     // 系列卡片 - 带悬停效果
    FREE        // 免费卡片 - 绿色背景
}

/**
 * 卡片尺寸枚举
 */
enum class ShuimuCardSize {
    SMALL,      // 小型卡片 - 8dp内边距
    MEDIUM,     // 中型卡片 - 12dp内边距
    LARGE       // 大型卡片 - 16dp内边距
}

/**
 * 水幕卡片组件
 * 
 * @param modifier 修饰符
 * @param type 卡片类型
 * @param size 卡片尺寸
 * @param onClick 点击事件，为null时不可点击
 * @param enabled 是否启用
 * @param content 卡片内容
 */
@Composable
fun ShuimuCard(
    modifier: Modifier = Modifier,
    type: ShuimuCardType = ShuimuCardType.DEFAULT,
    size: ShuimuCardSize = ShuimuCardSize.MEDIUM,
    onClick: (() -> Unit)? = null,
    enabled: Boolean = true,
    content: @Composable ColumnScope.() -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isHovered by interactionSource.collectIsHoveredAsState()
    
    // 根据原型定义卡片样式
    val (backgroundColor, borderColor, shadowElevation) = when (type) {
        ShuimuCardType.DEFAULT -> {
            val bgColor = if (isHovered) Color(0xFFF8FAFC) else Color.White
            Triple(bgColor, Color.Transparent, 2.dp)
        }
        ShuimuCardType.CATEGORY -> {
            // 原型：background: white, border-left: 2px solid #667eea
            val bgColor = if (isHovered) Color(0xFFF8FAFC) else Color.White
            Triple(bgColor, Primary, 2.dp) // #667eea
        }
        ShuimuCardType.SERIES -> {
            // 原型：series-card样式
            val bgColor = if (isHovered) Color(0xFFF8FAFC) else Color.White
            Triple(bgColor, Color.Transparent, if (isHovered) 4.dp else 2.dp)
        }
        ShuimuCardType.FREE -> {
            // 原型：background: #f0fdf4
            val bgColor = Color(0xFFF0FDF4)
            Triple(bgColor, Color.Transparent, 2.dp)
        }
    }
    
    // 悬停动画
    val translateX by animateFloatAsState(
        targetValue = if (isHovered && type == ShuimuCardType.CATEGORY) 4f else 0f,
        animationSpec = tween(300),
        label = "translateX"
    )
    
    val translateY by animateFloatAsState(
        targetValue = if (isHovered && type == ShuimuCardType.SERIES) -2f else 0f,
        animationSpec = tween(300),
        label = "translateY"
    )
    
    // 卡片内边距
    val padding = when (size) {
        ShuimuCardSize.SMALL -> PaddingValues(8.dp)
        ShuimuCardSize.MEDIUM -> PaddingValues(
            start = 8.dp,
            top = 12.dp,
            end = 8.dp,
            bottom = 16.dp
        ) // 原型：padding: 12px 0 16px 8px
        ShuimuCardSize.LARGE -> PaddingValues(16.dp)
    }
    
    // 卡片修饰符 - 优化边框实现和移动端适配
    val cardModifier = modifier
        .fillMaxWidth()
        .clip(RoundedCornerShape(12.dp)) // 原型：border-radius: 12px
        .shadow(
            elevation = shadowElevation,
            shape = RoundedCornerShape(12.dp),
            ambientColor = Color.Black.copy(alpha = 0.1f),
            spotColor = Color.Black.copy(alpha = 0.1f)
        )
        .background(backgroundColor)
        .graphicsLayer {
            translationX = translateX
            translationY = translateY
        }
        .then(
            if (onClick != null && enabled) {
                Modifier.clickable(
                    interactionSource = interactionSource,
                    indication = null,
                    onClick = onClick
                )
            } else {
                Modifier
            }
        )

    // 使用Box叠加实现精确的左边框效果
    if (type == ShuimuCardType.CATEGORY) {
        Box(modifier = cardModifier) {
            // 左边框
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .width(2.dp)
                    .background(borderColor)
                    .align(Alignment.CenterStart)
            )
            // 内容区域
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(padding)
                    .padding(start = 2.dp), // 为左边框留出空间
                content = content
            )
        }
    } else {
        Column(
            modifier = cardModifier.padding(padding),
            content = content
        )
    }
}

/**
 * 预定义的卡片样式组合
 */
object ShuimuCardDefaults {
    
    /**
     * 分类卡片 - 基于原型category-card样式
     * 原型：background: white, border-left: 2px solid #667eea, padding: 12px 0 16px 8px
     */
    @Composable
    fun CategoryCard(
        modifier: Modifier = Modifier,
        onClick: (() -> Unit)? = null,
        enabled: Boolean = true,
        content: @Composable ColumnScope.() -> Unit
    ) {
        ShuimuCard(
            modifier = modifier,
            type = ShuimuCardType.CATEGORY,
            size = ShuimuCardSize.MEDIUM,
            onClick = onClick,
            enabled = enabled,
            content = content
        )
    }
    
    /**
     * 系列卡片 - 基于原型series-card样式
     * 原型：带悬停效果的系列卡片
     */
    @Composable
    fun SeriesCard(
        modifier: Modifier = Modifier,
        onClick: (() -> Unit)? = null,
        enabled: Boolean = true,
        isFree: Boolean = false,
        content: @Composable ColumnScope.() -> Unit
    ) {
        ShuimuCard(
            modifier = modifier,
            type = if (isFree) ShuimuCardType.FREE else ShuimuCardType.SERIES,
            size = ShuimuCardSize.LARGE,
            onClick = onClick,
            enabled = enabled,
            content = content
        )
    }
    
    /**
     * 内容卡片 - 通用内容容器
     */
    @Composable
    fun ContentCard(
        modifier: Modifier = Modifier,
        onClick: (() -> Unit)? = null,
        enabled: Boolean = true,
        content: @Composable ColumnScope.() -> Unit
    ) {
        ShuimuCard(
            modifier = modifier,
            type = ShuimuCardType.DEFAULT,
            size = ShuimuCardSize.MEDIUM,
            onClick = onClick,
            enabled = enabled,
            content = content
        )
    }
    
    /**
     * 小型卡片 - 紧凑布局
     */
    @Composable
    fun CompactCard(
        modifier: Modifier = Modifier,
        onClick: (() -> Unit)? = null,
        enabled: Boolean = true,
        content: @Composable ColumnScope.() -> Unit
    ) {
        ShuimuCard(
            modifier = modifier,
            type = ShuimuCardType.DEFAULT,
            size = ShuimuCardSize.SMALL,
            onClick = onClick,
            enabled = enabled,
            content = content
        )
    }
} 