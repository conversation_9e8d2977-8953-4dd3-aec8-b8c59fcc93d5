package com.shuimu.videocourse.presentation.components

/**
 * 组件使用检查工具
 * 
 * 🚨 强制组件使用规范检查器 🚨
 * 
 * 使用方法：
 * 1. 在创建任何组件前调用 beforeCreateComponent()
 * 2. 检查是否有现成的组件可以使用
 * 3. 如果有，必须使用现有组件
 * 4. 如果没有，才可以创建新组件
 */
object ComponentUsageChecker {
    
    /**
     * 🚨 创建组件前的强制检查
     * 
     * @param componentName 要创建的组件名称
     * @throws IllegalStateException 如果组件已存在
     */
    fun beforeCreateComponent(componentName: String) {
        val existingComponent = ComponentRegistry.checkComponentExists(componentName)
        
        if (existingComponent != null) {
            println("\n" + "=".repeat(60))
            println("🚨 组件重复创建警告 🚨")
            println("=".repeat(60))
            println(existingComponent)
            println("=".repeat(60))
            println("\n📋 查看所有可用组件：")
            println(ComponentRegistry.showAllComponents())
            
            error("""
                ❌ 禁止创建重复组件！
                
                组件 '$componentName' 已存在或有相似功能的组件。
                请使用现有组件而不是创建新组件。
                
                🔍 检查命令：ComponentRegistry.checkComponentExists("$componentName")
                📖 使用规范：.cursor/rules/组件使用保障规范.mdc
            """.trimIndent())
        }
        
        println("✅ 组件检查通过：'$componentName' 可以创建")
    }
    
    /**
     * 搜索相关组件
     */
    fun searchRelatedComponents(keyword: String): List<ComponentInfo> {
        return ComponentRegistry.searchComponents(keyword)
    }
    
    /**
     * 显示组件使用建议
     */
    fun showUsageSuggestions(componentType: String): String {
        val suggestions = when (componentType.lowercase()) {
            "button", "按钮" -> """
                💡 按钮组件使用建议：
                ✅ 使用：ShuimuButton
                📍 位置：presentation/components/basic/ShuimuButton.kt
                🎯 功能：支持4种类型、3种尺寸、多种状态
                
                使用示例：
                ShuimuButton(
                    text = "点击我",
                    type = ButtonType.PRIMARY,
                    size = ButtonSize.MEDIUM,
                    onClick = { /* 处理点击 */ }
                )
            """.trimIndent()
            
            "card", "卡片" -> """
                💡 卡片组件使用建议：
                ✅ 使用：ShuimuCard
                📍 位置：presentation/components/basic/ShuimuCard.kt
                🎯 功能：支持边框、阴影、悬停效果
                
                使用示例：
                ShuimuCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 卡片内容
                }
            """.trimIndent()
            
            "textfield", "input", "输入框" -> """
                💡 输入框组件使用建议：
                ✅ 使用：ShuimuTextField
                📍 位置：presentation/components/basic/ShuimuTextField.kt
                🎯 功能：支持多种输入类型和状态
                
                使用示例：
                ShuimuTextField(
                    value = text,
                    onValueChange = { text = it },
                    label = "请输入内容",
                    inputType = InputType.TEXT
                )
            """.trimIndent()
            
            "progress", "进度条" -> """
                💡 进度条组件使用建议：
                ✅ 使用：ProgressBar
                📍 位置：presentation/components/basic/ProgressBar.kt
                🎯 功能：支持线性、圆形、环形三种类型
                
                使用示例：
                LinearProgressBar(
                    progress = 0.75f,
                    size = ProgressBarSize.MEDIUM,
                    animated = true
                )
            """.trimIndent()
            
            "video", "视频" -> """
                💡 视频组件使用建议：
                ✅ 使用：VideoItem / VideoInfoPanel
                📍 位置：presentation/components/display/VideoItem.kt
                🎯 功能：视频展示、播放进度、购买状态
                
                使用示例：
                VideoItem(
                    title = "视频标题",
                    progress = 75f,
                    isPurchased = true,
                    onClick = { /* 处理点击 */ }
                )
            """.trimIndent()
            
            else -> """
                💡 通用组件使用建议：
                
                🔍 搜索相关组件：
                ${searchRelatedComponents(componentType).joinToString("\n") { 
                    "  • ${it.name} - ${it.description}" 
                }}
                
                📋 查看所有组件：ComponentRegistry.showAllComponents()
                🔍 检查组件存在：ComponentRegistry.checkComponentExists("$componentType")
            """.trimIndent()
        }
        
        return suggestions
    }
    
    /**
     * 验证组件使用规范
     */
    fun validateUsage(): String {
        return ComponentRegistry.validateProjectComponents()
    }
}

/**
 * 快捷检查函数 - 全局使用
 */
fun checkComponent(name: String) = ComponentUsageChecker.beforeCreateComponent(name)

/**
 * 快捷搜索函数 - 全局使用  
 */
fun searchComponents(keyword: String) = ComponentUsageChecker.searchRelatedComponents(keyword)

/**
 * 快捷建议函数 - 全局使用
 */
fun getComponentSuggestions(type: String) = ComponentUsageChecker.showUsageSuggestions(type) 