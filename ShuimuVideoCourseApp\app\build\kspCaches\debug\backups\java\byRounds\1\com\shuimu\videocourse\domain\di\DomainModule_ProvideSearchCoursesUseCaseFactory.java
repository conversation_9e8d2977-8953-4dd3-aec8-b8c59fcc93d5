package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.usecase.course.SearchCoursesUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideSearchCoursesUseCaseFactory implements Factory<SearchCoursesUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public DomainModule_ProvideSearchCoursesUseCaseFactory(
      Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public SearchCoursesUseCase get() {
    return provideSearchCoursesUseCase(courseRepositoryProvider.get());
  }

  public static DomainModule_ProvideSearchCoursesUseCaseFactory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new DomainModule_ProvideSearchCoursesUseCaseFactory(courseRepositoryProvider);
  }

  public static SearchCoursesUseCase provideSearchCoursesUseCase(
      CourseRepository courseRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideSearchCoursesUseCase(courseRepository));
  }
}
