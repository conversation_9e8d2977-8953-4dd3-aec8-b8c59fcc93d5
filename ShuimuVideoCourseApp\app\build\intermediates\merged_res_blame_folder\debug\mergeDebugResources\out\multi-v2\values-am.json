{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "714,812,910,1016,1102,1205,1322,1400,1476,1567,1660,1752,1846,1946,2039,2134,2227,2318,2409,2489,2589,2689,2785,2887,2987,3086,3236,12556", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "807,905,1011,1097,1200,1317,1395,1471,1562,1655,1747,1841,1941,2034,2129,2222,2313,2404,2484,2584,2684,2780,2882,2982,3081,3231,3327,12631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,268,372,475,550,636,740,864,970,1095,1173,1267,1350,1439,1545,1660,1759,1878,1999,2118,2262,2373,2481,2591,2702,2786,2877,2983,3098,3189,3287,3385,3500,3622,3723,3814,3884,3958,4038,4119,4217,4294,4373,4469,4562,4653,4744,4825,4922,5017,5111,5226,5304,5401", "endColumns": "106,105,103,102,74,85,103,123,105,124,77,93,82,88,105,114,98,118,120,118,143,110,107,109,110,83,90,105,114,90,97,97,114,121,100,90,69,73,79,80,97,76,78,95,92,90,90,80,96,94,93,114,77,96,92", "endOffsets": "157,263,367,470,545,631,735,859,965,1090,1168,1262,1345,1434,1540,1655,1754,1873,1994,2113,2257,2368,2476,2586,2697,2781,2872,2978,3093,3184,3282,3380,3495,3617,3718,3809,3879,3953,4033,4114,4212,4289,4368,4464,4557,4648,4739,4820,4917,5012,5106,5221,5299,5396,5489"}, "to": {"startLines": "46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,146,155,158,160,164,165,166,167,168,169,170,171,172,173,174,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3332,3439,3545,3649,4599,4674,4760,4864,4988,5094,5219,5297,5391,5474,5563,5669,5784,5883,6002,6123,6242,6386,6497,6605,6715,6826,6910,7001,7107,7222,7313,7411,7509,7624,7746,7847,8126,11790,12476,12706,12888,13247,13324,13403,13499,13592,13683,13774,13855,13952,14047,14141,14256,14334,14431", "endColumns": "106,105,103,102,74,85,103,123,105,124,77,93,82,88,105,114,98,118,120,118,143,110,107,109,110,83,90,105,114,90,97,97,114,121,100,90,69,73,79,80,97,76,78,95,92,90,90,80,96,94,93,114,77,96,92", "endOffsets": "3434,3540,3644,3747,4669,4755,4859,4983,5089,5214,5292,5386,5469,5558,5664,5779,5878,5997,6118,6237,6381,6492,6600,6710,6821,6905,6996,7102,7217,7308,7406,7504,7619,7741,7842,7933,8191,11859,12551,12782,12981,13319,13398,13494,13587,13678,13769,13850,13947,14042,14136,14251,14329,14426,14519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1297,1362", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1292,1357,1473"}, "to": {"startLines": "57,58,91,92,94,147,148,149,150,151,152,153,154,157,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4439,4522,7938,8030,8196,11864,11942,12025,12107,12185,12251,12317,12395,12636,12986,13066,13131", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "4517,4594,8025,8121,8273,11937,12020,12102,12180,12246,12312,12390,12471,12701,13061,13126,13242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,175,237,297,369,432,521,602", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "112,170,232,292,364,427,516,597,663"}, "to": {"startLines": "119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10101,10163,10221,10283,10343,10415,10478,10567,10648", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "10158,10216,10278,10338,10410,10473,10562,10643,10709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "50,51,52,53,54,55,56,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3752,3845,3945,4042,4141,4237,4339,12787", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3840,3940,4037,4136,4232,4334,4434,12883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,664,744,825,901,986,1067,1133,1195,1279,1362,1429,1492,1553,1619,1719,1821,1922,1991,2067,2135,2201,2280,2360,2422,2487,2540,2597,2643,2704,2762,2837,2896,2958,3017,3074,3138,3201,3265,3315,3371,3441,3511", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "278,477,659,739,820,896,981,1062,1128,1190,1274,1357,1424,1487,1548,1614,1714,1816,1917,1986,2062,2130,2196,2275,2355,2417,2482,2535,2592,2638,2699,2757,2832,2891,2953,3012,3069,3133,3196,3260,3310,3366,3436,3506,3558"}, "to": {"startLines": "2,11,15,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,333,532,8278,8358,8439,8515,8600,8681,8747,8809,8893,8976,9043,9106,9167,9233,9333,9435,9536,9605,9681,9749,9815,9894,9974,10036,10714,10767,10824,10870,10931,10989,11064,11123,11185,11244,11301,11365,11428,11492,11542,11598,11668,11738", "endLines": "10,14,18,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "328,527,709,8353,8434,8510,8595,8676,8742,8804,8888,8971,9038,9101,9162,9228,9328,9430,9531,9600,9676,9744,9810,9889,9969,10031,10096,10762,10819,10865,10926,10984,11059,11118,11180,11239,11296,11360,11423,11487,11537,11593,11663,11733,11785"}}]}]}