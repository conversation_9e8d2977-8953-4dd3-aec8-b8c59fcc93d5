package com.shuimu.videocourse.domain.service;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserDomainService_Factory implements Factory<UserDomainService> {
  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<CourseRepository> courseRepositoryProvider;

  public UserDomainService_Factory(Provider<UserRepository> userRepositoryProvider,
      Provider<CourseRepository> courseRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public UserDomainService get() {
    return newInstance(userRepositoryProvider.get(), courseRepositoryProvider.get());
  }

  public static UserDomainService_Factory create(Provider<UserRepository> userRepositoryProvider,
      Provider<CourseRepository> courseRepositoryProvider) {
    return new UserDomainService_Factory(userRepositoryProvider, courseRepositoryProvider);
  }

  public static UserDomainService newInstance(UserRepository userRepository,
      CourseRepository courseRepository) {
    return new UserDomainService(userRepository, courseRepository);
  }
}
