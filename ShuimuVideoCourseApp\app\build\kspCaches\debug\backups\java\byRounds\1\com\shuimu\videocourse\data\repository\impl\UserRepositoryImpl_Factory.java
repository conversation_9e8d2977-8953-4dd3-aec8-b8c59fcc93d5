package com.shuimu.videocourse.data.repository.impl;

import android.content.Context;
import com.shuimu.videocourse.data.local.dao.UserDao;
import com.shuimu.videocourse.data.remote.api.UserApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserRepositoryImpl_Factory implements Factory<UserRepositoryImpl> {
  private final Provider<UserDao> userDaoProvider;

  private final Provider<UserApiService> userApiServiceProvider;

  private final Provider<Context> contextProvider;

  public UserRepositoryImpl_Factory(Provider<UserDao> userDaoProvider,
      Provider<UserApiService> userApiServiceProvider, Provider<Context> contextProvider) {
    this.userDaoProvider = userDaoProvider;
    this.userApiServiceProvider = userApiServiceProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public UserRepositoryImpl get() {
    return newInstance(userDaoProvider.get(), userApiServiceProvider.get(), contextProvider.get());
  }

  public static UserRepositoryImpl_Factory create(Provider<UserDao> userDaoProvider,
      Provider<UserApiService> userApiServiceProvider, Provider<Context> contextProvider) {
    return new UserRepositoryImpl_Factory(userDaoProvider, userApiServiceProvider, contextProvider);
  }

  public static UserRepositoryImpl newInstance(UserDao userDao, UserApiService userApiService,
      Context context) {
    return new UserRepositoryImpl(userDao, userApiService, context);
  }
}
