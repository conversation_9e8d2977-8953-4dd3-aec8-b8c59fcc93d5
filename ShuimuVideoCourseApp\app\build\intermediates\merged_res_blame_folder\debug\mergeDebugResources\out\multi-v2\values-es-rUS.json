{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,662,748,835,908,1004,1100,1180,1248,1347,1446,1512,1581,1647,1718,1813,1908,2003,2074,2158,2234,2314,2412,2511,2577,2641,2694,2752,2800,2861,2926,2988,3054,3126,3190,3251,3317,3382,3448,3501,3566,3645,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,470,657,743,830,903,999,1095,1175,1243,1342,1441,1507,1576,1642,1713,1808,1903,1998,2069,2153,2229,2309,2407,2506,2572,2636,2689,2747,2795,2856,2921,2983,3049,3121,3185,3246,3312,3377,3443,3496,3561,3640,3719,3777"}, "to": {"startLines": "2,11,15,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,525,8832,8918,9005,9078,9174,9270,9350,9418,9517,9616,9682,9751,9817,9888,9983,10078,10173,10244,10328,10404,10484,10582,10681,10747,11480,11533,11591,11639,11700,11765,11827,11893,11965,12029,12090,12156,12221,12287,12340,12405,12484,12563", "endLines": "10,14,18,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "330,520,707,8913,9000,9073,9169,9265,9345,9413,9512,9611,9677,9746,9812,9883,9978,10073,10168,10239,10323,10399,10479,10577,10676,10742,10806,11528,11586,11634,11695,11760,11822,11888,11960,12024,12085,12151,12216,12282,12335,12400,12479,12558,12616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,994,1058,1145,1235,1312,1390,1460", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,989,1053,1140,1230,1307,1385,1455,1578"}, "to": {"startLines": "57,58,91,92,94,147,148,149,150,151,152,153,154,157,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4631,4730,8469,8567,8743,12698,12777,12873,12965,13052,13116,13180,13267,13523,13893,13971,14041", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "4725,4807,8562,8665,8827,12772,12868,12960,13047,13111,13175,13262,13352,13595,13966,14036,14159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "712,832,941,1049,1134,1236,1352,1437,1517,1608,1701,1796,1890,1989,2082,2181,2277,2368,2459,2541,2648,2747,2846,2954,3062,3169,3328,13440", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "827,936,1044,1129,1231,1347,1432,1512,1603,1696,1791,1885,1984,2077,2176,2272,2363,2454,2536,2643,2742,2841,2949,3057,3164,3323,3423,13518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "50,51,52,53,54,55,56,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3899,3998,4100,4200,4298,4405,4511,13685", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3993,4095,4195,4293,4400,4506,4626,13781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,410,526,604,698,810,949,1063,1211,1292,1390,1483,1581,1695,1814,1914,2046,2175,2310,2482,2609,2725,2844,2968,3062,3154,3271,3400,3497,3598,3709,3839,3976,4083,4183,4256,4333,4416,4501,4608,4686,4766,4863,4965,5061,5156,5240,5351,5448,5547,5666,5744,5847", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "168,286,405,521,599,693,805,944,1058,1206,1287,1385,1478,1576,1690,1809,1909,2041,2170,2305,2477,2604,2720,2839,2963,3057,3149,3266,3395,3492,3593,3704,3834,3971,4078,4178,4251,4328,4411,4496,4603,4681,4761,4858,4960,5056,5151,5235,5346,5443,5542,5661,5739,5842,5937"}, "to": {"startLines": "46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,146,155,158,160,164,165,166,167,168,169,170,171,172,173,174,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3428,3546,3664,3783,4812,4890,4984,5096,5235,5349,5497,5578,5676,5769,5867,5981,6100,6200,6332,6461,6596,6768,6895,7011,7130,7254,7348,7440,7557,7686,7783,7884,7995,8125,8262,8369,8670,12621,13357,13600,13786,14164,14242,14322,14419,14521,14617,14712,14796,14907,15004,15103,15222,15300,15403", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "3541,3659,3778,3894,4885,4979,5091,5230,5344,5492,5573,5671,5764,5862,5976,6095,6195,6327,6456,6591,6763,6890,7006,7125,7249,7343,7435,7552,7681,7778,7879,7990,8120,8257,8364,8464,8738,12693,13435,13680,13888,14237,14317,14414,14516,14612,14707,14791,14902,14999,15098,15217,15295,15398,15493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10811,10886,10949,11014,11083,11160,11234,11323,11411", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "10881,10944,11009,11078,11155,11229,11318,11406,11475"}}]}]}