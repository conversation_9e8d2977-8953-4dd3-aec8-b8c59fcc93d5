package com.shuimu.videocourse.presentation.components.status

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 缓存状态指示器组件
 * 
 * 基于原型设计：UI Prototype/data/component-templates-with-styles.js 第85-95行缓存状态样式定义
 * - 支持缓存状态指示和下载进度显示
 * - 实现状态颜色区分（#3b82f6、#10b981）
 * - 设置文字大小12sp
 * - 支持多种缓存状态显示
 * 
 * @param cacheStatus 缓存状态
 * @param progress 下载进度（0.0-1.0）
 * @param showProgress 是否显示进度
 * @param modifier 修饰符
 */
@Composable
fun CacheStatusIndicator(
    cacheStatus: CacheStatus,
    progress: Float = 0f,
    showProgress: Boolean = false,
    modifier: Modifier = Modifier
) {
    val statusConfig = when (cacheStatus) {
        CacheStatus.NotCached -> StatusConfig(
            text = "未缓存",
            icon = Icons.Default.CloudDownload,
            color = Color(0xFF6b7280),
            backgroundColor = Color(0xFF6b7280).copy(alpha = 0.1f)
        )
        CacheStatus.Downloading -> StatusConfig(
            text = if (showProgress) "${(progress * 100).toInt()}%" else "下载中",
            icon = Icons.Default.Download,
            color = Color(0xFF3b82f6),
            backgroundColor = Color(0xFF3b82f6).copy(alpha = 0.1f)
        )
        CacheStatus.Cached -> StatusConfig(
            text = "已缓存",
            icon = Icons.Default.CheckCircle,
            color = Color(0xFF10b981),
            backgroundColor = Color(0xFF10b981).copy(alpha = 0.1f)
        )
        CacheStatus.Failed -> StatusConfig(
            text = "缓存失败",
            icon = Icons.Default.Error,
            color = Color(0xFFef4444),
            backgroundColor = Color(0xFFef4444).copy(alpha = 0.1f)
        )
    }
    
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .background(statusConfig.backgroundColor)
            .padding(horizontal = 8.dp, vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = statusConfig.icon,
            contentDescription = statusConfig.text,
            modifier = Modifier.size(12.dp),
            tint = statusConfig.color
        )
        
        Text(
            text = statusConfig.text,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = statusConfig.color
        )
    }
}

/**
 * 状态配置数据类
 */
private data class StatusConfig(
    val text: String,
    val icon: ImageVector,
    val color: Color,
    val backgroundColor: Color
)

/**
 * 缓存状态枚举
 */
enum class CacheStatus {
    NotCached,   // 未缓存
    Downloading, // 下载中
    Cached,      // 已缓存
    Failed       // 缓存失败
} 