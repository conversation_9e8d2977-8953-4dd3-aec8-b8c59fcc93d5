{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,190,254,322,403,480,554,631", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "122,185,249,317,398,475,549,626,704"}, "to": {"startLines": "119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10841,10913,10976,11040,11108,11189,11266,11340,11417", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "10908,10971,11035,11103,11184,11261,11335,11412,11490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,477,659,742,824,894,985,1081,1157,1220,1321,1424,1494,1562,1630,1696,1818,1934,2054,2118,2199,2276,2354,2450,2545,2614,2679,2732,2792,2840,2901,2969,3037,3110,3177,3238,3299,3366,3431,3501,3553,3615,3691,3767", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "283,472,654,737,819,889,980,1076,1152,1215,1316,1419,1489,1557,1625,1691,1813,1929,2049,2113,2194,2271,2349,2445,2540,2609,2674,2727,2787,2835,2896,2964,3032,3105,3172,3233,3294,3361,3426,3496,3548,3610,3686,3762,3815"}, "to": {"startLines": "2,11,15,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,527,8821,8904,8986,9056,9147,9243,9319,9382,9483,9586,9656,9724,9792,9858,9980,10096,10216,10280,10361,10438,10516,10612,10707,10776,11495,11548,11608,11656,11717,11785,11853,11926,11993,12054,12115,12182,12247,12317,12369,12431,12507,12583", "endLines": "10,14,18,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "333,522,704,8899,8981,9051,9142,9238,9314,9377,9478,9581,9651,9719,9787,9853,9975,10091,10211,10275,10356,10433,10511,10607,10702,10771,10836,11543,11603,11651,11712,11780,11848,11921,11988,12049,12110,12177,12242,12312,12364,12426,12502,12578,12631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,278,389,501,578,676,792,934,1053,1205,1288,1398,1489,1584,1702,1820,1923,2059,2193,2322,2495,2620,2732,2848,2968,3060,3154,3273,3410,3512,3613,3717,3851,3991,4096,4193,4280,4358,4442,4523,4633,4709,4788,4883,4980,5067,5159,5241,5341,5435,5530,5643,5719,5820", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "162,273,384,496,573,671,787,929,1048,1200,1283,1393,1484,1579,1697,1815,1918,2054,2188,2317,2490,2615,2727,2843,2963,3055,3149,3268,3405,3507,3608,3712,3846,3986,4091,4188,4275,4353,4437,4518,4628,4704,4783,4878,4975,5062,5154,5236,5336,5430,5525,5638,5714,5815,5905"}, "to": {"startLines": "46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,146,155,158,160,164,165,166,167,168,169,170,171,172,173,174,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3418,3530,3641,3752,4765,4842,4940,5056,5198,5317,5469,5552,5662,5753,5848,5966,6084,6187,6323,6457,6586,6759,6884,6996,7112,7232,7324,7418,7537,7674,7776,7877,7981,8115,8255,8360,8651,12636,13375,13616,13798,14176,14252,14331,14426,14523,14610,14702,14784,14884,14978,15073,15186,15262,15363", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "3525,3636,3747,3859,4837,4935,5051,5193,5312,5464,5547,5657,5748,5843,5961,6079,6182,6318,6452,6581,6754,6879,6991,7107,7227,7319,7413,7532,7669,7771,7872,7976,8110,8250,8355,8452,8733,12709,13454,13692,13903,14247,14326,14421,14518,14605,14697,14779,14879,14973,15068,15181,15257,15358,15448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "709,817,917,1027,1116,1222,1339,1421,1501,1592,1685,1780,1874,1974,2067,2162,2256,2347,2438,2521,2627,2733,2832,2942,3050,3151,3321,13459", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "812,912,1022,1111,1217,1334,1416,1496,1587,1680,1775,1869,1969,2062,2157,2251,2342,2433,2516,2622,2728,2827,2937,3045,3146,3316,3413,13537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "50,51,52,53,54,55,56,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3864,3964,4069,4167,4266,4371,4473,13697", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3959,4064,4162,4261,4366,4468,4579,13793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,380,480,563,645,731,826,908,980,1051,1136,1224,1298,1379,1448", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "199,281,375,475,558,640,726,821,903,975,1046,1131,1219,1293,1374,1443,1561"}, "to": {"startLines": "57,58,91,92,94,147,148,149,150,151,152,153,154,157,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4584,4683,8457,8551,8738,12714,12796,12882,12977,13059,13131,13202,13287,13542,13908,13989,14058", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "4678,4760,8546,8646,8816,12791,12877,12972,13054,13126,13197,13282,13370,13611,13984,14053,14171"}}]}]}