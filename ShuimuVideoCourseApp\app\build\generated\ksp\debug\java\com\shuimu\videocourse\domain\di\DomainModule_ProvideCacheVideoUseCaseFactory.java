package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.usecase.course.CacheVideoUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideCacheVideoUseCaseFactory implements Factory<CacheVideoUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public DomainModule_ProvideCacheVideoUseCaseFactory(
      Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public CacheVideoUseCase get() {
    return provideCacheVideoUseCase(courseRepositoryProvider.get());
  }

  public static DomainModule_ProvideCacheVideoUseCaseFactory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new DomainModule_ProvideCacheVideoUseCaseFactory(courseRepositoryProvider);
  }

  public static CacheVideoUseCase provideCacheVideoUseCase(CourseRepository courseRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideCacheVideoUseCase(courseRepository));
  }
}
