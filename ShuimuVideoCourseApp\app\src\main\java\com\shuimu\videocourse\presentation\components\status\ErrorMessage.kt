package com.shuimu.videocourse.presentation.components.status

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.basic.ShuimuButton

/**
 * 错误消息组件
 * 
 * 基于原型的错误消息设计
 * - 支持错误信息显示和重试按钮
 * - 实现统一错误样式和图标文字组合
 * - 使用ShuimuButton作为重试按钮
 * - 支持自定义错误类型和样式
 * 
 * @param errorMessage 错误消息文本
 * @param errorType 错误类型
 * @param showRetryButton 是否显示重试按钮
 * @param retryButtonText 重试按钮文本
 * @param onRetry 重试回调
 * @param modifier 修饰符
 */
@Composable
fun ErrorMessage(
    errorMessage: String,
    errorType: ErrorType = ErrorType.General,
    showRetryButton: Boolean = true,
    retryButtonText: String = "重试",
    onRetry: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val errorColor = when (errorType) {
        ErrorType.Network -> Color(0xFFf59e0b)
        ErrorType.Server -> Color(0xFFef4444)
        ErrorType.General -> Color(0xFF6b7280)
    }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 错误图标
        Icon(
            imageVector = Icons.Default.Warning,
            contentDescription = "错误",
            modifier = Modifier.size(48.dp),
            tint = errorColor
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 错误消息
        Text(
            text = errorMessage,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF374151),
            textAlign = TextAlign.Center,
            lineHeight = 24.sp
        )
        
        if (showRetryButton) {
            Spacer(modifier = Modifier.height(24.dp))
            
            // 重试按钮
            ShuimuButton(
                text = retryButtonText,
                onClick = onRetry,
                modifier = Modifier.fillMaxWidth(0.6f)
            )
        }
    }
}

/**
 * 错误类型枚举
 */
enum class ErrorType {
    Network,  // 网络错误
    Server,   // 服务器错误
    General   // 一般错误
} 