{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,689,774,858,935,1024,1121,1190,1254,1345,1436,1499,1563,1625,1693,1817,1943,2067,2142,2223,2296,2365,2448,2530,2595,2675,2728,2789,2839,2900,2959,3029,3092,3154,3218,3278,3344,3409,3479,3531,3591,3665,3739", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "283,483,684,769,853,930,1019,1116,1185,1249,1340,1431,1494,1558,1620,1688,1812,1938,2062,2137,2218,2291,2360,2443,2525,2590,2670,2723,2784,2834,2895,2954,3024,3087,3149,3213,3273,3339,3404,3474,3526,3586,3660,3734,3787"}, "to": {"startLines": "2,11,15,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,538,8658,8743,8827,8904,8993,9090,9159,9223,9314,9405,9468,9532,9594,9662,9786,9912,10036,10111,10192,10265,10334,10417,10499,10564,11274,11327,11388,11438,11499,11558,11628,11691,11753,11817,11877,11943,12008,12078,12130,12190,12264,12338", "endLines": "10,14,18,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "333,533,734,8738,8822,8899,8988,9085,9154,9218,9309,9400,9463,9527,9589,9657,9781,9907,10031,10106,10187,10260,10329,10412,10494,10559,10639,11322,11383,11433,11494,11553,11623,11686,11748,11812,11872,11938,12003,12073,12125,12185,12259,12333,12386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10644,10711,10770,10829,10895,10971,11034,11123,11205", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "10706,10765,10824,10890,10966,11029,11118,11200,11269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "50,51,52,53,54,55,56,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3842,3937,4044,4141,4241,4344,4448,13422", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3932,4039,4136,4236,4339,4443,4554,13518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "739,839,936,1048,1133,1234,1348,1429,1508,1599,1692,1785,1879,1985,2078,2173,2268,2359,2453,2534,2644,2751,2848,2957,3057,3160,3315,13190", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "834,931,1043,1128,1229,1343,1424,1503,1594,1687,1780,1874,1980,2073,2168,2263,2354,2448,2529,2639,2746,2843,2952,3052,3155,3310,3408,13266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,974,1042,1120,1203,1273,1350,1418", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,969,1037,1115,1198,1268,1345,1413,1533"}, "to": {"startLines": "57,58,91,92,94,147,148,149,150,151,152,153,154,157,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4559,4650,8303,8402,8573,12466,12546,12641,12730,12812,12880,12948,13026,13271,13628,13705,13773", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "4645,4726,8397,8496,8653,12541,12636,12725,12807,12875,12943,13021,13104,13336,13700,13768,13888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,376,484,560,655,768,905,1028,1159,1245,1342,1435,1531,1642,1764,1866,1987,2107,2245,2412,2531,2643,2759,2878,2972,3066,3171,3289,3393,3497,3596,3721,3852,3956,4056,4128,4203,4284,4365,4470,4546,4633,4730,4827,4918,5022,5106,5207,5304,5405,5521,5597,5695", "endColumns": "109,107,102,107,75,94,112,136,122,130,85,96,92,95,110,121,101,120,119,137,166,118,111,115,118,93,93,104,117,103,103,98,124,130,103,99,71,74,80,80,104,75,86,96,96,90,103,83,100,96,100,115,75,97,91", "endOffsets": "160,268,371,479,555,650,763,900,1023,1154,1240,1337,1430,1526,1637,1759,1861,1982,2102,2240,2407,2526,2638,2754,2873,2967,3061,3166,3284,3388,3492,3591,3716,3847,3951,4051,4123,4198,4279,4360,4465,4541,4628,4725,4822,4913,5017,5101,5202,5299,5400,5516,5592,5690,5782"}, "to": {"startLines": "46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,146,155,158,160,164,165,166,167,168,169,170,171,172,173,174,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3413,3523,3631,3734,4731,4807,4902,5015,5152,5275,5406,5492,5589,5682,5778,5889,6011,6113,6234,6354,6492,6659,6778,6890,7006,7125,7219,7313,7418,7536,7640,7744,7843,7968,8099,8203,8501,12391,13109,13341,13523,13893,13969,14056,14153,14250,14341,14445,14529,14630,14727,14828,14944,15020,15118", "endColumns": "109,107,102,107,75,94,112,136,122,130,85,96,92,95,110,121,101,120,119,137,166,118,111,115,118,93,93,104,117,103,103,98,124,130,103,99,71,74,80,80,104,75,86,96,96,90,103,83,100,96,100,115,75,97,91", "endOffsets": "3518,3626,3729,3837,4802,4897,5010,5147,5270,5401,5487,5584,5677,5773,5884,6006,6108,6229,6349,6487,6654,6773,6885,7001,7120,7214,7308,7413,7531,7635,7739,7838,7963,8094,8198,8298,8568,12461,13185,13417,13623,13964,14051,14148,14245,14336,14440,14524,14625,14722,14823,14939,15015,15113,15205"}}]}]}