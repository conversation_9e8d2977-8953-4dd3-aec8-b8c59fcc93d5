package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.repository.UserRepository;
import com.shuimu.videocourse.domain.usecase.course.WatchVideoUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideWatchVideoUseCaseFactory implements Factory<WatchVideoUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public DomainModule_ProvideWatchVideoUseCaseFactory(
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public WatchVideoUseCase get() {
    return provideWatchVideoUseCase(courseRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static DomainModule_ProvideWatchVideoUseCaseFactory create(
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new DomainModule_ProvideWatchVideoUseCaseFactory(courseRepositoryProvider, userRepositoryProvider);
  }

  public static WatchVideoUseCase provideWatchVideoUseCase(CourseRepository courseRepository,
      UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideWatchVideoUseCase(courseRepository, userRepository));
  }
}
