{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,301,412,533,610,706,820,954,1068,1211,1293,1387,1477,1574,1689,1809,1908,2042,2169,2303,2482,2610,2725,2849,2966,3057,3152,3269,3400,3498,3608,3710,3840,3981,4086,4184,4263,4339,4425,4509,4616,4692,4775,4866,4966,5053,5149,5234,5337,5435,5532,5681,5757,5858", "endColumns": "122,122,110,120,76,95,113,133,113,142,81,93,89,96,114,119,98,133,126,133,178,127,114,123,116,90,94,116,130,97,109,101,129,140,104,97,78,75,85,83,106,75,82,90,99,86,95,84,102,97,96,148,75,100,94", "endOffsets": "173,296,407,528,605,701,815,949,1063,1206,1288,1382,1472,1569,1684,1804,1903,2037,2164,2298,2477,2605,2720,2844,2961,3052,3147,3264,3395,3493,3603,3705,3835,3976,4081,4179,4258,4334,4420,4504,4611,4687,4770,4861,4961,5048,5144,5229,5332,5430,5527,5676,5752,5853,5948"}, "to": {"startLines": "48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,95,148,157,160,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3765,3888,4011,4122,5161,5238,5334,5448,5582,5696,5839,5921,6015,6105,6202,6317,6437,6536,6670,6797,6931,7110,7238,7353,7477,7594,7685,7780,7897,8028,8126,8236,8338,8468,8609,8714,9011,13026,13765,14007,14192,14567,14643,14726,14817,14917,15004,15100,15185,15288,15386,15483,15632,15708,15809", "endColumns": "122,122,110,120,76,95,113,133,113,142,81,93,89,96,114,119,98,133,126,133,178,127,114,123,116,90,94,116,130,97,109,101,129,140,104,97,78,75,85,83,106,75,82,90,99,86,95,84,102,97,96,148,75,100,94", "endOffsets": "3883,4006,4117,4238,5233,5329,5443,5577,5691,5834,5916,6010,6100,6197,6312,6432,6531,6665,6792,6926,7105,7233,7348,7472,7589,7680,7775,7892,8023,8121,8231,8333,8463,8604,8709,8807,9085,13097,13846,14086,14294,14638,14721,14812,14912,14999,15095,15180,15283,15381,15478,15627,15703,15804,15899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,196,268,338,418,495,596,694", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "125,191,263,333,413,490,591,689,768"}, "to": {"startLines": "121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11178,11253,11319,11391,11461,11541,11618,11719,11817", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "11248,11314,11386,11456,11536,11613,11714,11812,11891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1002,1072,1157,1244,1317,1395,1463", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,997,1067,1152,1239,1312,1390,1458,1580"}, "to": {"startLines": "59,60,93,94,96,149,150,151,152,153,154,155,156,159,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4974,5072,8812,8908,9090,13102,13188,13276,13369,13453,13523,13593,13678,13934,14299,14377,14445", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "5067,5156,8903,9006,9175,13183,13271,13364,13448,13518,13588,13673,13760,14002,14372,14440,14562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "881,1001,1111,1220,1306,1410,1532,1614,1694,1804,1912,2018,2127,2238,2341,2453,2560,2665,2765,2850,2959,3070,3169,3280,3387,3492,3666,13851", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "996,1106,1215,1301,1405,1527,1609,1689,1799,1907,2013,2122,2233,2336,2448,2555,2660,2760,2845,2954,3065,3164,3275,3382,3487,3661,3760,13929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,831,915,998,1081,1176,1271,1344,1411,1505,1599,1665,1732,1795,1871,1977,2088,2195,2269,2351,2425,2498,2598,2697,2763,2829,2882,2940,2988,3049,3107,3183,3247,3312,3377,3434,3500,3566,3632,3684,3748,3826,3904", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "281,562,826,910,993,1076,1171,1266,1339,1406,1500,1594,1660,1727,1790,1866,1972,2083,2190,2264,2346,2420,2493,2593,2692,2758,2824,2877,2935,2983,3044,3102,3178,3242,3307,3372,3429,3495,3561,3627,3679,3743,3821,3899,3954"}, "to": {"startLines": "2,11,16,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,617,9180,9264,9347,9430,9525,9620,9693,9760,9854,9948,10014,10081,10144,10220,10326,10437,10544,10618,10700,10774,10847,10947,11046,11112,11896,11949,12007,12055,12116,12174,12250,12314,12379,12444,12501,12567,12633,12699,12751,12815,12893,12971", "endLines": "10,15,20,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "331,612,876,9259,9342,9425,9520,9615,9688,9755,9849,9943,10009,10076,10139,10215,10321,10432,10539,10613,10695,10769,10842,10942,11041,11107,11173,11944,12002,12050,12111,12169,12245,12309,12374,12439,12496,12562,12628,12694,12746,12810,12888,12966,13021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "52,53,54,55,56,57,58,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4243,4341,4443,4543,4644,4751,4859,14091", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "4336,4438,4538,4639,4746,4854,4969,14187"}}]}]}