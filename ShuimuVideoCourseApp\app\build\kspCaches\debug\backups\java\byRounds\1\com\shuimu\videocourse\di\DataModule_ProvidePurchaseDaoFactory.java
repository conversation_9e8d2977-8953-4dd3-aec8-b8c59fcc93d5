package com.shuimu.videocourse.di;

import com.shuimu.videocourse.data.local.dao.PurchaseDao;
import com.shuimu.videocourse.data.local.database.ShuimuDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvidePurchaseDaoFactory implements Factory<PurchaseDao> {
  private final Provider<ShuimuDatabase> databaseProvider;

  public DataModule_ProvidePurchaseDaoFactory(Provider<ShuimuDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public PurchaseDao get() {
    return providePurchaseDao(databaseProvider.get());
  }

  public static DataModule_ProvidePurchaseDaoFactory create(
      Provider<ShuimuDatabase> databaseProvider) {
    return new DataModule_ProvidePurchaseDaoFactory(databaseProvider);
  }

  public static PurchaseDao providePurchaseDao(ShuimuDatabase database) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.providePurchaseDao(database));
  }
}
