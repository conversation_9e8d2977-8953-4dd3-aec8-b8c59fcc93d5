package com.shuimu.videocourse.presentation.components.user

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.basic.BadgeComponent
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 用户资料头部组件
 * 
 * 基于原型设计：UI Prototype/03-我的页面.html 用户资料头部区域
 * - 支持用户头像、信息、等级徽章显示
 * - 实现统计数据展示和背景渐变效果
 * - 使用#667eea紫蓝色渐变主题
 * - 包含用户统计数据的卡片展示
 */
@Composable
fun UserProfileHeader(
    userName: String,
    userLevel: Int,
    watchedCount: Int,
    totalCourses: Int,
    studyDays: Int,
    avatarUrl: String? = null,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 用户信息头部卡片
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color(0xFF667eea),
                            Color(0xFF764ba2)
                        )
                    )
                )
                .padding(24.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 用户头像
                UserAvatar(
                    avatarUrl = avatarUrl,
                    size = UserAvatar.Size.Large,
                    modifier = Modifier.padding(end = 16.dp)
                )
                
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    // 用户名
                    Text(
                        text = userName,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 用户等级徽章
                    BadgeComponent(
                        level = userLevel,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 统计数据卡片
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 4.dp
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 已观看视频
                StatItem(
                    number = watchedCount.toString(),
                    label = "已观看",
                    modifier = Modifier.weight(1f)
                )
                
                // 总课程数
                StatItem(
                    number = totalCourses.toString(),
                    label = "总课程",
                    modifier = Modifier.weight(1f)
                )
                
                // 学习天数
                StatItem(
                    number = studyDays.toString(),
                    label = "学习天数",
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatItem(
    number: String,
    label: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = number,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF667eea),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF6b7280),
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 用户头像尺寸枚举
 */
enum class UserAvatarSize(val size: Int) {
    Small(32),
    Medium(40),
    Large(48)
}

/**
 * 用户头像组件
 */
@Composable
fun UserAvatar(
    avatarUrl: String? = null,
    size: UserAvatarSize = UserAvatarSize.Medium,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {}
) {
    Box(
        modifier = modifier
            .size(size.size.dp)
            .clip(CircleShape)
            .background(
                color = Color.White.copy(alpha = 0.2f),
                shape = CircleShape
            ),
        contentAlignment = Alignment.Center
    ) {
        if (avatarUrl != null) {
            // TODO: 实际项目中使用AsyncImage加载网络图片
            // AsyncImage(
            //     model = avatarUrl,
            //     contentDescription = "用户头像",
            //     modifier = Modifier.fillMaxSize(),
            //     contentScale = ContentScale.Crop
            // )
        }
        
        // 默认头像（emoji）
        Text(
            text = "👤",
            fontSize = (size.size * 0.6).sp,
            color = Color.White
        )
    }
}

// 预览组件
@Composable
fun UserProfileHeaderPreview() {
    ShuimuTheme {
        UserProfileHeader(
            userName = "水幕学员",
            userLevel = 5,
            watchedCount = 128,
            totalCourses = 15,
            studyDays = 30
        )
    }
} 