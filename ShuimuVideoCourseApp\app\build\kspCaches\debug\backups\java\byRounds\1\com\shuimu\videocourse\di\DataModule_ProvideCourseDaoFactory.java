package com.shuimu.videocourse.di;

import com.shuimu.videocourse.data.local.dao.CourseDao;
import com.shuimu.videocourse.data.local.database.ShuimuDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideCourseDaoFactory implements Factory<CourseDao> {
  private final Provider<ShuimuDatabase> databaseProvider;

  public DataModule_ProvideCourseDaoFactory(Provider<ShuimuDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public CourseDao get() {
    return provideCourseDao(databaseProvider.get());
  }

  public static DataModule_ProvideCourseDaoFactory create(
      Provider<ShuimuDatabase> databaseProvider) {
    return new DataModule_ProvideCourseDaoFactory(databaseProvider);
  }

  public static CourseDao provideCourseDao(ShuimuDatabase database) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideCourseDao(database));
  }
}
