package com.shuimu.videocourse.presentation.components.modal

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.RadioButtonUnchecked
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.shuimu.videocourse.presentation.components.basic.ShuimuButton
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 支付方式枚举
 */
enum class PaymentMethod(val displayName: String, val emoji: String) {
    ALIPAY("支付宝", "💰"),
    WECHAT("微信支付", "💬")
}

/**
 * 课程购买信息数据类
 */
data class PurchaseInfo(
    val courseTitle: String,
    val originalPrice: String,
    val currentPrice: String,
    val discount: String? = null,
    val videoCount: Int,
    val duration: String,
    val description: String? = null
)

/**
 * 水幕视频课程App - 购买确认弹窗组件
 * 
 * 基于原型设计：UI Prototype/04-支付页面.html 底部支付框区域
 * 
 * 功能特性：
 * - 支持价格信息显示和支付方式选择
 * - 实现确认支付按钮和底部弹出样式
 * - 添加支付宝、微信支付选项
 * - 使用原型的#667eea紫蓝色渐变主题
 * 
 * @param isVisible 弹窗是否可见
 * @param purchaseInfo 购买信息
 * @param onDismiss 关闭弹窗回调
 * @param onConfirmPurchase 确认购买回调
 * @param selectedPaymentMethod 选中的支付方式
 * @param onPaymentMethodChange 支付方式变更回调
 * @param modifier 修饰符
 */
@Composable
fun PurchaseModal(
    isVisible: Boolean,
    purchaseInfo: PurchaseInfo,
    onDismiss: () -> Unit,
    onConfirmPurchase: (PaymentMethod) -> Unit,
    selectedPaymentMethod: PaymentMethod = PaymentMethod.ALIPAY,
    onPaymentMethodChange: (PaymentMethod) -> Unit = {},
    modifier: Modifier = Modifier
) {
    var currentPaymentMethod by remember { mutableStateOf(selectedPaymentMethod) }
    
    if (isVisible) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true
            )
        ) {
            // 底部弹出动画
            AnimatedVisibility(
                visible = isVisible,
                enter = slideInVertically(
                    initialOffsetY = { it },
                    animationSpec = tween(300, easing = EaseOutCubic)
                ) + fadeIn(animationSpec = tween(300)),
                exit = slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(200, easing = EaseInCubic)
                ) + fadeOut(animationSpec = tween(200))
            ) {
                PurchaseModalContent(
                    purchaseInfo = purchaseInfo,
                    onDismiss = onDismiss,
                    onConfirmPurchase = { onConfirmPurchase(currentPaymentMethod) },
                    selectedPaymentMethod = currentPaymentMethod,
                    onPaymentMethodChange = { method ->
                        currentPaymentMethod = method
                        onPaymentMethodChange(method)
                    },
                    modifier = modifier
                )
            }
        }
    }
}

/**
 * 购买弹窗内容组件
 */
@Composable
private fun PurchaseModalContent(
    purchaseInfo: PurchaseInfo,
    onDismiss: () -> Unit,
    onConfirmPurchase: () -> Unit,
    selectedPaymentMethod: PaymentMethod,
    onPaymentMethodChange: (PaymentMethod) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // 弹窗头部
            PurchaseModalHeader(onDismiss = onDismiss)
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 课程信息
            CourseInfoSection(purchaseInfo = purchaseInfo)
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 价格信息
            PriceInfoSection(purchaseInfo = purchaseInfo)
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 支付方式选择
            PaymentMethodSection(
                selectedMethod = selectedPaymentMethod,
                onMethodChange = onPaymentMethodChange
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 确认购买按钮
            ShuimuButton(
                text = "确认支付 ${purchaseInfo.currentPrice}",
                onClick = onConfirmPurchase,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 购买弹窗头部组件
 */
@Composable
private fun PurchaseModalHeader(
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "确认购买",
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF1F2937)
        )
        
        IconButton(
            onClick = onDismiss,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "关闭",
                tint = Color(0xFF6B7280),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

/**
 * 课程信息区域组件
 */
@Composable
private fun CourseInfoSection(
    purchaseInfo: PurchaseInfo,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 课程标题
        Text(
            text = purchaseInfo.courseTitle,
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF1F2937),
            lineHeight = 22.sp
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 课程详情
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "${purchaseInfo.videoCount}个视频",
                fontSize = 14.sp,
                color = Color(0xFF6B7280)
            )
            Text(
                text = "时长${purchaseInfo.duration}",
                fontSize = 14.sp,
                color = Color(0xFF6B7280)
            )
        }
        
        // 课程描述
        if (purchaseInfo.description != null) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = purchaseInfo.description,
                fontSize = 14.sp,
                color = Color(0xFF6B7280),
                lineHeight = 20.sp
            )
        }
    }
}

/**
 * 价格信息区域组件
 */
@Composable
private fun PriceInfoSection(
    purchaseInfo: PurchaseInfo,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF9FAFB)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "课程价格",
                    fontSize = 14.sp,
                    color = Color(0xFF6B7280)
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 当前价格
                    Text(
                        text = purchaseInfo.currentPrice,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFFEF4444) // 红色价格
                    )
                    
                    // 原价（如果有折扣）
                    if (purchaseInfo.originalPrice != purchaseInfo.currentPrice) {
                        Text(
                            text = purchaseInfo.originalPrice,
                            fontSize = 14.sp,
                            color = Color(0xFF9CA3AF),
                            textDecoration = TextDecoration.LineThrough
                        )
                    }
                }
            }
            
            // 折扣标签
            if (purchaseInfo.discount != null) {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFEF4444)),
                    modifier = Modifier.clip(RoundedCornerShape(4.dp))
                ) {
                    Text(
                        text = purchaseInfo.discount,
                        fontSize = 12.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }
        }
    }
}

/**
 * 支付方式选择区域组件
 */
@Composable
private fun PaymentMethodSection(
    selectedMethod: PaymentMethod,
    onMethodChange: (PaymentMethod) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "选择支付方式",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF1F2937)
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 支付方式选项
        PaymentMethod.values().forEach { method ->
            PaymentMethodItem(
                method = method,
                isSelected = selectedMethod == method,
                onSelect = { onMethodChange(method) }
            )
            
            if (method != PaymentMethod.values().last()) {
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

/**
 * 支付方式项组件
 */
@Composable
private fun PaymentMethodItem(
    method: PaymentMethod,
    isSelected: Boolean,
    onSelect: () -> Unit,
    modifier: Modifier = Modifier
) {
    val borderColor = if (isSelected) Color(0xFF667EEA) else Color(0xFFE5E7EB)
    val backgroundColor = if (isSelected) Color(0xFF667EEA).copy(alpha = 0.05f) else Color.Transparent
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .border(1.dp, borderColor, RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .clickable { onSelect() },
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 支付方式图标
                Text(
                    text = method.emoji,
                    fontSize = 20.sp
                )
                
                // 支付方式名称
                Text(
                    text = method.displayName,
                    fontSize = 16.sp,
                    color = Color(0xFF1F2937),
                    fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                )
            }
            
            // 选择状态图标
            Icon(
                imageVector = if (isSelected) Icons.Default.CheckCircle else Icons.Default.RadioButtonUnchecked,
                contentDescription = null,
                tint = if (isSelected) Color(0xFF667EEA) else Color(0xFF9CA3AF),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

/**
 * 预览组件
 */
@Composable
fun PurchaseModalPreview() {
    ShuimuTheme {
        var isVisible by remember { mutableStateOf(true) }
        var selectedMethod by remember { mutableStateOf(PaymentMethod.ALIPAY) }
        
        Column {
            Button(
                onClick = { isVisible = true },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text("显示购买弹窗")
            }
        }
        
        // 购买弹窗
        PurchaseModal(
            isVisible = isVisible,
            purchaseInfo = PurchaseInfo(
                courseTitle = "Android Jetpack Compose 完整开发教程",
                originalPrice = "¥299.00",
                currentPrice = "¥199.00",
                discount = "限时7折",
                videoCount = 45,
                duration = "12小时30分钟",
                description = "从零基础到高级应用，全面掌握现代Android UI开发技术"
            ),
            onDismiss = { isVisible = false },
            onConfirmPurchase = { method ->
                // 处理购买逻辑
                isVisible = false
            },
            selectedPaymentMethod = selectedMethod,
            onPaymentMethodChange = { method ->
                selectedMethod = method
            }
        )
    }
} 