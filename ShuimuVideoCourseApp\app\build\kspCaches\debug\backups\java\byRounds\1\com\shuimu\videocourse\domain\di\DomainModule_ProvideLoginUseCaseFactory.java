package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.UserRepository;
import com.shuimu.videocourse.domain.usecase.user.LoginUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideLoginUseCaseFactory implements Factory<LoginUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public DomainModule_ProvideLoginUseCaseFactory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public LoginUseCase get() {
    return provideLoginUseCase(userRepositoryProvider.get());
  }

  public static DomainModule_ProvideLoginUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new DomainModule_ProvideLoginUseCaseFactory(userRepositoryProvider);
  }

  public static LoginUseCase provideLoginUseCase(UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideLoginUseCase(userRepository));
  }
}
