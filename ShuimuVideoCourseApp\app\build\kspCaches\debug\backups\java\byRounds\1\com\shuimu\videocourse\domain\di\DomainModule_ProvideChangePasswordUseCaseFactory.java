package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.UserRepository;
import com.shuimu.videocourse.domain.usecase.user.ChangePasswordUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideChangePasswordUseCaseFactory implements Factory<ChangePasswordUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public DomainModule_ProvideChangePasswordUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public ChangePasswordUseCase get() {
    return provideChangePasswordUseCase(userRepositoryProvider.get());
  }

  public static DomainModule_ProvideChangePasswordUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new DomainModule_ProvideChangePasswordUseCaseFactory(userRepositoryProvider);
  }

  public static ChangePasswordUseCase provideChangePasswordUseCase(UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideChangePasswordUseCase(userRepository));
  }
}
