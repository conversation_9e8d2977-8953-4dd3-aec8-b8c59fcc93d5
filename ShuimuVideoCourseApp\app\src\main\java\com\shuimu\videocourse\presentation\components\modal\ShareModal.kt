package com.shuimu.videocourse.presentation.components.modal

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 分享选项数据类
 */
data class ShareOption(
    val id: String,
    val label: String,
    val icon: ImageVector? = null,
    val emoji: String? = null,
    val backgroundColor: Color,
    val onClick: () -> Unit
)

/**
 * 分享统计数据类
 */
data class ShareStats(
    val shareCount: Int = 12,
    val conversionCount: Int = 3,
    val totalEarnings: String = "¥180"
)

/**
 * 水幕视频课程App - 分享弹窗组件
 * 
 * 基于原型设计：UI Prototype/data/component-templates-with-styles.js 第680-750行 createShareModal函数
 * 
 * 功能特性：
 * - 支持分享选项网格布局（4列网格）
 * - 实现分享统计数据显示
 * - 添加底部弹出动画和分享功能集成
 * - 使用原型的#667eea紫蓝色渐变主题
 * 
 * @param isVisible 弹窗是否可见
 * @param onDismiss 关闭弹窗回调
 * @param shareStats 分享统计数据
 * @param onShareToWechat 分享到微信好友
 * @param onShareToMoments 分享到朋友圈
 * @param onShareToQQ 分享到QQ好友
 * @param onCopyLink 复制链接
 * @param modifier 修饰符
 */
@Composable
fun ShareModal(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    shareStats: ShareStats = ShareStats(),
    onShareToWechat: () -> Unit = {},
    onShareToMoments: () -> Unit = {},
    onShareToQQ: () -> Unit = {},
    onCopyLink: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val clipboardManager = LocalClipboardManager.current
    
    // 基于原型的分享选项配置
    val shareOptions = listOf(
        ShareOption(
            id = "wechat",
            label = "微信好友",
            emoji = "💬",
            backgroundColor = Color(0xFF07C160),
            onClick = onShareToWechat
        ),
        ShareOption(
            id = "moments",
            label = "朋友圈",
            emoji = "📷",
            backgroundColor = Color(0xFF1AAD19),
            onClick = onShareToMoments
        ),
        ShareOption(
            id = "copy",
            label = "复制链接",
            icon = Icons.Default.ContentCopy,
            backgroundColor = Color(0xFF3B82F6),
            onClick = {
                clipboardManager.setText(AnnotatedString("https://shuimu.app/share/course123"))
                onCopyLink()
            }
        ),
        ShareOption(
            id = "qq",
            label = "QQ好友",
            emoji = "🐧",
            backgroundColor = Color(0xFF12B7F5),
            onClick = onShareToQQ
        )
    )
    
    if (isVisible) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true
            )
        ) {
            // 底部弹出动画
            AnimatedVisibility(
                visible = isVisible,
                enter = slideInVertically(
                    initialOffsetY = { it },
                    animationSpec = tween(300, easing = EaseOutCubic)
                ) + fadeIn(animationSpec = tween(300)),
                exit = slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(200, easing = EaseInCubic)
                ) + fadeOut(animationSpec = tween(200))
            ) {
                ShareModalContent(
                    shareOptions = shareOptions,
                    shareStats = shareStats,
                    onDismiss = onDismiss,
                    modifier = modifier
                )
            }
        }
    }
}

/**
 * 分享弹窗内容组件
 */
@Composable
private fun ShareModalContent(
    shareOptions: List<ShareOption>,
    shareStats: ShareStats,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // 弹窗头部
            ShareModalHeader(onDismiss = onDismiss)
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 描述文字
            Text(
                text = "分享给好友，每成交一单获得30%分成收益",
                fontSize = 14.sp,
                color = Color(0xFF6B7280),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 分享选项网格
            ShareOptionsGrid(shareOptions = shareOptions)
            
            Spacer(modifier = Modifier.height(30.dp))
            
            // 分享统计
            ShareStatsSection(shareStats = shareStats)
        }
    }
}

/**
 * 分享弹窗头部组件
 */
@Composable
private fun ShareModalHeader(
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "分享推广分成",
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF1F2937)
        )
        
        IconButton(
            onClick = onDismiss,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "关闭",
                tint = Color(0xFF6B7280),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

/**
 * 分享选项网格组件
 */
@Composable
private fun ShareOptionsGrid(
    shareOptions: List<ShareOption>,
    modifier: Modifier = Modifier
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        horizontalArrangement = Arrangement.spacedBy(20.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp),
        modifier = modifier.fillMaxWidth()
    ) {
        items(shareOptions) { option ->
            ShareOptionItem(option = option)
        }
    }
}

/**
 * 分享选项项组件
 */
@Composable
private fun ShareOptionItem(
    option: ShareOption,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clickable { option.onClick() }
            .padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 图标背景圆圈
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(option.backgroundColor),
            contentAlignment = Alignment.Center
        ) {
            if (option.emoji != null) {
                Text(
                    text = option.emoji,
                    fontSize = 20.sp,
                    color = Color.White
                )
            } else if (option.icon != null) {
                Icon(
                    imageVector = option.icon,
                    contentDescription = option.label,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 标签文字
        Text(
            text = option.label,
            fontSize = 12.sp,
            color = Color(0xFF1F2937),
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

/**
 * 分享统计区域组件
 */
@Composable
private fun ShareStatsSection(
    shareStats: ShareStats,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        StatItem(
            number = shareStats.shareCount.toString(),
            label = "分享次数"
        )
        StatItem(
            number = shareStats.conversionCount.toString(),
            label = "转化人数"
        )
        StatItem(
            number = shareStats.totalEarnings,
            label = "累计收益"
        )
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatItem(
    number: String,
    label: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = number,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF667EEA) // 原型主色调
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF6B7280)
        )
    }
}

/**
 * 预览组件
 */
@Composable
fun ShareModalPreview() {
    ShuimuTheme {
        var isVisible by remember { mutableStateOf(true) }
        
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.5f)),
            contentAlignment = Alignment.BottomCenter
        ) {
            ShareModal(
                isVisible = isVisible,
                onDismiss = { isVisible = false },
                shareStats = ShareStats(
                    shareCount = 12,
                    conversionCount = 3,
                    totalEarnings = "¥180"
                ),
                onShareToWechat = { /* 分享到微信 */ },
                onShareToMoments = { /* 分享到朋友圈 */ },
                onShareToQQ = { /* 分享到QQ */ },
                onCopyLink = { /* 复制链接 */ }
            )
        }
    }
} 