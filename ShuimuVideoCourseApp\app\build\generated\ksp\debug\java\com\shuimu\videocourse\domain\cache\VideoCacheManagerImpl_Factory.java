package com.shuimu.videocourse.domain.cache;

import android.content.Context;
import com.shuimu.videocourse.data.local.dao.CacheDao;
import com.shuimu.videocourse.data.local.datastore.PreferencesManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VideoCacheManagerImpl_Factory implements Factory<VideoCacheManagerImpl> {
  private final Provider<Context> contextProvider;

  private final Provider<CacheDao> cacheDaoProvider;

  private final Provider<PreferencesManager> preferencesManagerProvider;

  private final Provider<OkHttpClient> okHttpClientProvider;

  public VideoCacheManagerImpl_Factory(Provider<Context> contextProvider,
      Provider<CacheDao> cacheDaoProvider, Provider<PreferencesManager> preferencesManagerProvider,
      Provider<OkHttpClient> okHttpClientProvider) {
    this.contextProvider = contextProvider;
    this.cacheDaoProvider = cacheDaoProvider;
    this.preferencesManagerProvider = preferencesManagerProvider;
    this.okHttpClientProvider = okHttpClientProvider;
  }

  @Override
  public VideoCacheManagerImpl get() {
    return newInstance(contextProvider.get(), cacheDaoProvider.get(), preferencesManagerProvider.get(), okHttpClientProvider.get());
  }

  public static VideoCacheManagerImpl_Factory create(Provider<Context> contextProvider,
      Provider<CacheDao> cacheDaoProvider, Provider<PreferencesManager> preferencesManagerProvider,
      Provider<OkHttpClient> okHttpClientProvider) {
    return new VideoCacheManagerImpl_Factory(contextProvider, cacheDaoProvider, preferencesManagerProvider, okHttpClientProvider);
  }

  public static VideoCacheManagerImpl newInstance(Context context, CacheDao cacheDao,
      PreferencesManager preferencesManager, OkHttpClient okHttpClient) {
    return new VideoCacheManagerImpl(context, cacheDao, preferencesManager, okHttpClient);
  }
}
