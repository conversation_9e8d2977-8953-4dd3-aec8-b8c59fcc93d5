package com.shuimu.videocourse.di;

import com.shuimu.videocourse.data.local.dao.WatchProgressDao;
import com.shuimu.videocourse.data.local.database.ShuimuDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideWatchProgressDaoFactory implements Factory<WatchProgressDao> {
  private final Provider<ShuimuDatabase> databaseProvider;

  public DataModule_ProvideWatchProgressDaoFactory(Provider<ShuimuDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public WatchProgressDao get() {
    return provideWatchProgressDao(databaseProvider.get());
  }

  public static DataModule_ProvideWatchProgressDaoFactory create(
      Provider<ShuimuDatabase> databaseProvider) {
    return new DataModule_ProvideWatchProgressDaoFactory(databaseProvider);
  }

  public static WatchProgressDao provideWatchProgressDao(ShuimuDatabase database) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideWatchProgressDao(database));
  }
}
