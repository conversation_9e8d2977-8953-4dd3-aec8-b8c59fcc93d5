package com.shuimu.videocourse.di;

import android.content.Context;
import com.shuimu.videocourse.data.remote.network.NetworkConfig;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideNetworkConfigFactory implements Factory<NetworkConfig> {
  private final Provider<Context> contextProvider;

  public DataModule_ProvideNetworkConfigFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public NetworkConfig get() {
    return provideNetworkConfig(contextProvider.get());
  }

  public static DataModule_ProvideNetworkConfigFactory create(Provider<Context> contextProvider) {
    return new DataModule_ProvideNetworkConfigFactory(contextProvider);
  }

  public static NetworkConfig provideNetworkConfig(Context context) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideNetworkConfig(context));
  }
}
