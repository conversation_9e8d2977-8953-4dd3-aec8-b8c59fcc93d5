package com.shuimu.videocourse.di;

import com.shuimu.videocourse.data.local.dao.VideoDao;
import com.shuimu.videocourse.data.local.database.ShuimuDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideVideoDaoFactory implements Factory<VideoDao> {
  private final Provider<ShuimuDatabase> databaseProvider;

  public DataModule_ProvideVideoDaoFactory(Provider<ShuimuDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public VideoDao get() {
    return provideVideoDao(databaseProvider.get());
  }

  public static DataModule_ProvideVideoDaoFactory create(
      Provider<ShuimuDatabase> databaseProvider) {
    return new DataModule_ProvideVideoDaoFactory(databaseProvider);
  }

  public static VideoDao provideVideoDao(ShuimuDatabase database) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideVideoDao(database));
  }
}
