package com.shuimu.videocourse.presentation.components.basic

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.platform.LocalDensity
import kotlin.math.cos
import kotlin.math.sin
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 徽章测试Activity - 用于测试所有徽章等级的动画效果
 */
class BadgeTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ShuimuTheme {
                BadgeTestScreen()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BadgeTestScreen() {
    var selectedStyle by remember { mutableStateOf(BadgeStyle.ORIGINAL) }
    var showPinkDiamondOptions by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = "水幕徽章系统测试",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 样式切换按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = { selectedStyle = BadgeStyle.ORIGINAL },
                modifier = Modifier.weight(1f)
            ) {
                Text("原版钻石系统")
            }
            Button(
                onClick = { selectedStyle = BadgeStyle.MIXED },
                modifier = Modifier.weight(1f)
            ) {
                Text("混合图标系统")
            }
        }
        
        // 粉色钻石效果测试按钮
        Button(
            onClick = { showPinkDiamondOptions = !showPinkDiamondOptions },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(if (showPinkDiamondOptions) "隐藏粉色钻石效果" else "显示粉色钻石效果选择")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 粉色钻石效果选择区域
        if (showPinkDiamondOptions) {
            PinkDiamondOptionsSection()
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // 徽章展示
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items((0..15).toList()) { count ->
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "观看次数: $count",
                            style = MaterialTheme.typography.bodyLarge
                        )
                        
                        BadgeComponent(
                            count = count,
                            style = selectedStyle,
                            animated = true
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun BadgeTestItem(
    count: Int,
    style: BadgeStyle
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column {
                Text(
                    text = "观看次数: $count",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "等级: ${if (count >= 10) "传奇" else "LEVEL_$count"}",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "样式: ${if (style == BadgeStyle.ORIGINAL) "原版钻石" else "混合图标"}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 徽章组件
            BadgeComponent(
                count = count,
                style = style,
                animated = true,
                modifier = Modifier.padding(start = 16.dp)
            )
        }
    }
}

@Composable
fun PinkDiamondOptionsSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "粉色钻石效果选择",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Text(
                text = "等级7-8粉色钻石效果对比：",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            // 粉色钻石实现方案（真正的CSS色相旋转算法）
            val pinkDiamondOptions = listOf(
                "方案1：💎 + 精确CSS hue-rotate(70deg)" to PinkDiamondMethod.HUE_ROTATE_70,
                "方案2：💎 + CSS hue-rotate(60deg)" to PinkDiamondMethod.HUE_ROTATE_60,
                "方案3：💎 + CSS hue-rotate(80deg)" to PinkDiamondMethod.HUE_ROTATE_80,
                "方案4：💎 + CSS hue-rotate(90deg)" to PinkDiamondMethod.HUE_ROTATE_90,
                "方案5：💎 + hue-rotate(70deg) + saturate(1.5)" to PinkDiamondMethod.HUE_SATURATE_70,
                "方案6：💎 + hue-rotate(70deg) + brightness(1.2)" to PinkDiamondMethod.HUE_BRIGHT_70,
                "方案7：💎 + HSV颜色空间直接操作" to PinkDiamondMethod.HSV_DIRECT,
                "方案8：💎 + HSV增强饱和度" to PinkDiamondMethod.HSV_ENHANCED,
                "方案9：💎 + CSS滤镜组合效果" to PinkDiamondMethod.CSS_FILTER_COMBO,
                "方案10：💎 + sepia + hue-rotate组合" to PinkDiamondMethod.SEPIA_HUE_ROTATE,
                "方案11：💎 + contrast + hue-rotate组合" to PinkDiamondMethod.CONTRAST_HUE,
                "方案12：💎 + 多重CSS滤镜叠加" to PinkDiamondMethod.MULTI_FILTER,
                "方案13：💎 + CSS原型精确复现（动态滤镜）" to PinkDiamondMethod.CSS_PROTOTYPE_EXACT
            )
            
            // 使用LazyColumn实现虚拟化滚动，只渲染可见的预览组件
            LazyColumn(
                modifier = Modifier.heightIn(max = 800.dp), // 使用heightIn允许自适应高度，最大800dp
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(pinkDiamondOptions) { (name, method) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = name,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = getMethodDescription(method),
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 等级7示例
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                PinkDiamondPreview(method = method, isLevel8 = false)
                                Text(text = "7级", fontSize = 10.sp)
                            }
                            
                            // 等级8示例
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Row(horizontalArrangement = Arrangement.spacedBy(4.dp)) {
                                    PinkDiamondPreview(method = method, isLevel8 = true, isRightDiamond = false) // 左钻石：顺时针1.8s
                                    PinkDiamondPreview(method = method, isLevel8 = true, isRightDiamond = true)  // 右钻石：逆时针1.6s
                                }
                                Text(text = "8级", fontSize = 10.sp)
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "以上展示了13种不同的粉色钻石实现方法，每种都能产生不同的视觉效果。请选择最接近CSS原型的方案。",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

// 粉色钻石方法枚举（真正的CSS色相旋转实现）
enum class PinkDiamondMethod {
    HUE_ROTATE_70,      // 精确CSS hue-rotate(70deg)
    HUE_ROTATE_60,      // hue-rotate(60deg)
    HUE_ROTATE_80,      // hue-rotate(80deg)
    HUE_ROTATE_90,      // hue-rotate(90deg)
    HUE_SATURATE_70,    // hue-rotate(70deg) + saturate(1.5)
    HUE_BRIGHT_70,      // hue-rotate(70deg) + brightness(1.2)
    HSV_DIRECT,         // HSV颜色空间直接操作
    HSV_ENHANCED,       // HSV增强饱和度
    CSS_FILTER_COMBO,   // CSS滤镜组合效果
    SEPIA_HUE_ROTATE,   // sepia + hue-rotate组合
    CONTRAST_HUE,       // contrast + hue-rotate组合
    MULTI_FILTER,       // 多重CSS滤镜叠加
    CSS_PROTOTYPE_EXACT // 精确复现CSS原型动态滤镜
}

// 获取方法描述
fun getMethodDescription(method: PinkDiamondMethod): String {
    return when (method) {
        PinkDiamondMethod.HUE_ROTATE_70 -> "💎 + 精确CSS hue-rotate(70deg)算法"
        PinkDiamondMethod.HUE_ROTATE_60 -> "💎 + CSS hue-rotate(60deg)算法"
        PinkDiamondMethod.HUE_ROTATE_80 -> "💎 + CSS hue-rotate(80deg)算法"
        PinkDiamondMethod.HUE_ROTATE_90 -> "💎 + CSS hue-rotate(90deg)算法"
        PinkDiamondMethod.HUE_SATURATE_70 -> "💎 + hue-rotate(70deg) + saturate(1.5)"
        PinkDiamondMethod.HUE_BRIGHT_70 -> "💎 + hue-rotate(70deg) + brightness(1.2)"
        PinkDiamondMethod.HSV_DIRECT -> "💎 + HSV颜色空间直接色相调整"
        PinkDiamondMethod.HSV_ENHANCED -> "💎 + HSV增强饱和度和色相"
        PinkDiamondMethod.CSS_FILTER_COMBO -> "💎 + CSS多滤镜组合效果"
        PinkDiamondMethod.SEPIA_HUE_ROTATE -> "💎 + sepia(0.5) + hue-rotate(70deg)"
        PinkDiamondMethod.CONTRAST_HUE -> "💎 + contrast(1.2) + hue-rotate(70deg)"
        PinkDiamondMethod.MULTI_FILTER -> "💎 + 多重CSS滤镜精确叠加"
        PinkDiamondMethod.CSS_PROTOTYPE_EXACT -> "💎 + CSS原型动态滤镜精确复现"
    }
}

// 创建精确色相旋转ColorMatrix的辅助函数
private fun createHueRotateMatrix(degrees: Float): ColorMatrix {
    val radians = degrees * Math.PI / 180.0
    val cos = kotlin.math.cos(radians).toFloat()
    val sin = kotlin.math.sin(radians).toFloat()
    val sqrt3 = kotlin.math.sqrt(3.0).toFloat()
    
    // 精确的色相旋转ColorMatrix公式
    val a = cos + (1 - cos) / 3
    val b = (1 - cos) / 3 - sin / sqrt3
    val c = (1 - cos) / 3 + sin / sqrt3
    
    return ColorMatrix(
        floatArrayOf(
            a, b, c, 0f, 0f,
            c, a, b, 0f, 0f,
            b, c, a, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        )
    )
}

// 创建饱和度调整ColorMatrix
private fun createSaturateMatrix(saturation: Float): ColorMatrix {
    val sr = (1 - saturation) * 0.3086f
    val sg = (1 - saturation) * 0.6094f
    val sb = (1 - saturation) * 0.0820f
    
    return ColorMatrix(
        floatArrayOf(
            sr + saturation, sg, sb, 0f, 0f,
            sr, sg + saturation, sb, 0f, 0f,
            sr, sg, sb + saturation, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        )
    )
}

// 创建亮度调整ColorMatrix
private fun createBrightnessMatrix(brightness: Float): ColorMatrix {
    val offset = (brightness - 1) * 255
    return ColorMatrix(
        floatArrayOf(
            brightness, 0f, 0f, 0f, offset,
            0f, brightness, 0f, 0f, offset,
            0f, 0f, brightness, 0f, offset,
            0f, 0f, 0f, 1f, 0f
        )
    )
}

// 创建sepia效果ColorMatrix
private fun createSepiaMatrix(amount: Float): ColorMatrix {
    val r = 1 - amount
    return ColorMatrix(
        floatArrayOf(
            r + amount * 0.393f, amount * 0.769f, amount * 0.189f, 0f, 0f,
            amount * 0.349f, r + amount * 0.686f, amount * 0.168f, 0f, 0f,
            amount * 0.272f, amount * 0.534f, r + amount * 0.131f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        )
    )
}

// 创建对比度调整ColorMatrix
private fun createContrastMatrix(contrast: Float): ColorMatrix {
    val offset = (1 - contrast) * 128
    return ColorMatrix(
        floatArrayOf(
            contrast, 0f, 0f, 0f, offset,
            0f, contrast, 0f, 0f, offset,
            0f, 0f, contrast, 0f, offset,
            0f, 0f, 0f, 1f, 0f
        )
    )
}

// 粉色钻石预览组件（轻量级动画版本）
@Composable
fun PinkDiamondPreview(method: PinkDiamondMethod, isLevel8: Boolean, isRightDiamond: Boolean = false) {
    // 创建轻量级动画：只有旋转动画，避免复杂的缩放和多重动画
    val infiniteTransition = rememberInfiniteTransition(label = "pink_diamond_rotation")
    
    // 简化的旋转动画
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = if (isLevel8 && isRightDiamond) -360f else 360f, // 右钻石逆时针，其他顺时针
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = if (isLevel8 && isRightDiamond) 1600 else 1700, // 右钻石1.6s，其他1.7s
                easing = LinearEasing
            ),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    when (method) {
        PinkDiamondMethod.HUE_ROTATE_70 -> {
            // 方案1：💎 + 精确CSS hue-rotate(70deg)
            val hueMatrix = remember { createHueRotateMatrix(70f) }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(hueMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.HUE_ROTATE_60 -> {
            // 方案2：💎 + CSS hue-rotate(60deg)
            val hueMatrix = remember { createHueRotateMatrix(60f) }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(hueMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.HUE_ROTATE_80 -> {
            // 方案3：💎 + CSS hue-rotate(80deg)
            val hueMatrix = remember { createHueRotateMatrix(80f) }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(hueMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.HUE_ROTATE_90 -> {
            // 方案4：💎 + CSS hue-rotate(90deg)
            val hueMatrix = remember { createHueRotateMatrix(90f) }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(hueMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.HUE_SATURATE_70 -> {
            // 方案5：💎 + hue-rotate(70deg) + saturate(1.5)
            val combinedMatrix = remember {
                val hue = createHueRotateMatrix(70f)
                val saturate = createSaturateMatrix(1.5f)
                // 手动矩阵乘法组合
                val combined = ColorMatrix()
                combined.set(hue)
                combined.timesAssign(saturate)
                combined
            }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(combinedMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.HUE_BRIGHT_70 -> {
            // 方案6：💎 + hue-rotate(70deg) + brightness(1.2)
            val combinedMatrix = remember {
                val hue = createHueRotateMatrix(70f)
                val bright = createBrightnessMatrix(1.2f)
                // 手动矩阵乘法组合
                val combined = ColorMatrix()
                combined.set(hue)
                combined.timesAssign(bright)
                combined
            }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(combinedMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.HSV_DIRECT -> {
            // 方案7：💎 + HSV颜色空间直接操作
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                // 使用HSV颜色空间调整
                val paint = android.graphics.Paint().apply {
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                    // 直接设置粉色
                    color = android.graphics.Color.HSVToColor(floatArrayOf(320f, 0.7f, 1.0f))
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.HSV_ENHANCED -> {
            // 方案8：💎 + HSV增强饱和度
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                    // HSV增强饱和度的粉色
                    color = android.graphics.Color.HSVToColor(floatArrayOf(310f, 0.9f, 1.0f))
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.CSS_FILTER_COMBO -> {
            // 方案9：💎 + CSS滤镜组合效果
            val comboMatrix = remember {
                val hue = createHueRotateMatrix(70f)
                val saturate = createSaturateMatrix(1.3f)
                val bright = createBrightnessMatrix(1.1f)
                // 逐步组合矩阵
                val combined = ColorMatrix()
                combined.set(hue)
                combined.timesAssign(saturate)
                combined.timesAssign(bright)
                combined
            }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(comboMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.SEPIA_HUE_ROTATE -> {
            // 方案10：💎 + sepia + hue-rotate组合
            val sepiaHueMatrix = remember {
                val sepia = createSepiaMatrix(0.5f)
                val hue = createHueRotateMatrix(70f)
                // 组合sepia和hue-rotate
                val combined = ColorMatrix()
                combined.set(sepia)
                combined.timesAssign(hue)
                combined
            }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(sepiaHueMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.CONTRAST_HUE -> {
            // 方案11：💎 + contrast + hue-rotate组合
            val contrastHueMatrix = remember {
                val contrast = createContrastMatrix(1.2f)
                val hue = createHueRotateMatrix(70f)
                // 组合contrast和hue-rotate
                val combined = ColorMatrix()
                combined.set(contrast)
                combined.timesAssign(hue)
                combined
            }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(contrastHueMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.MULTI_FILTER -> {
            // 方案12：💎 + 多重CSS滤镜精确叠加
            val multiFilterMatrix = remember {
                val sepia = createSepiaMatrix(0.3f)
                val hue = createHueRotateMatrix(70f)
                val saturate = createSaturateMatrix(1.4f)
                val bright = createBrightnessMatrix(1.15f)
                val contrast = createContrastMatrix(1.1f)
                
                // 逐步叠加所有滤镜
                val combined = ColorMatrix()
                combined.set(sepia)
                combined.timesAssign(hue)
                combined.timesAssign(saturate)
                combined.timesAssign(bright)
                combined.timesAssign(contrast)
                combined
            }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { rotationZ = rotation }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(multiFilterMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
        
        PinkDiamondMethod.CSS_PROTOTYPE_EXACT -> {
            // 方案13：💎 + CSS原型精确复现（动态滤镜）
            // 复现CSS: filter: brightness(1.3-2.0) contrast(1.2-1.6) hue-rotate(70-120deg)
            val animationProgress = (rotation / 360f) % 1f // 0-1循环
            
            val dynamicMatrix = remember(animationProgress) {
                // 根据动画进度动态计算滤镜参数
                val brightness = when {
                    animationProgress < 0.2f -> 1.3f + (animationProgress / 0.2f) * 0.5f // 1.3->1.8
                    animationProgress < 0.4f -> 1.8f - ((animationProgress - 0.2f) / 0.2f) * 0.4f // 1.8->1.4
                    animationProgress < 0.6f -> 1.4f + ((animationProgress - 0.4f) / 0.2f) * 0.6f // 1.4->2.0
                    animationProgress < 0.8f -> 2.0f - ((animationProgress - 0.6f) / 0.2f) * 0.5f // 2.0->1.5
                    else -> 1.5f - ((animationProgress - 0.8f) / 0.2f) * 0.2f // 1.5->1.3
                }
                
                val contrast = when {
                    animationProgress < 0.2f -> 1.2f + (animationProgress / 0.2f) * 0.3f // 1.2->1.5
                    animationProgress < 0.4f -> 1.5f - ((animationProgress - 0.2f) / 0.2f) * 0.2f // 1.5->1.3
                    animationProgress < 0.6f -> 1.3f + ((animationProgress - 0.4f) / 0.2f) * 0.3f // 1.3->1.6
                    animationProgress < 0.8f -> 1.6f - ((animationProgress - 0.6f) / 0.2f) * 0.2f // 1.6->1.4
                    else -> 1.4f - ((animationProgress - 0.8f) / 0.2f) * 0.2f // 1.4->1.2
                }
                
                val hueRotate = when {
                    animationProgress < 0.2f -> 70f + (animationProgress / 0.2f) * 15f // 70->85
                    animationProgress < 0.4f -> 85f + ((animationProgress - 0.2f) / 0.2f) * 15f // 85->100
                    animationProgress < 0.6f -> 100f + ((animationProgress - 0.4f) / 0.2f) * 20f // 100->120
                    animationProgress < 0.8f -> 120f - ((animationProgress - 0.6f) / 0.2f) * 15f // 120->105
                    else -> 105f - ((animationProgress - 0.8f) / 0.2f) * 35f // 105->70
                }
                
                // 组合动态滤镜
                val hue = createHueRotateMatrix(hueRotate)
                val bright = createBrightnessMatrix(brightness)
                val cont = createContrastMatrix(contrast)
                
                val combined = ColorMatrix()
                combined.set(hue)
                combined.timesAssign(bright)
                combined.timesAssign(cont)
                combined
            }
            
            Canvas(
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer { 
                        rotationZ = rotation
                        // 添加动态缩放效果，复现CSS的scale变化
                        val scaleProgress = (rotation / 360f) % 1f
                        val scale = when {
                            scaleProgress < 0.2f -> 0.8f + (scaleProgress / 0.2f) * 0.3f // 0.8->1.1
                            scaleProgress < 0.4f -> 1.1f - ((scaleProgress - 0.2f) / 0.2f) * 0.2f // 1.1->0.9
                            scaleProgress < 0.6f -> 0.9f + ((scaleProgress - 0.4f) / 0.2f) * 0.3f // 0.9->1.2
                            scaleProgress < 0.8f -> 1.2f - ((scaleProgress - 0.6f) / 0.2f) * 0.2f // 1.2->1.0
                            else -> 1.0f - ((scaleProgress - 0.8f) / 0.2f) * 0.2f // 1.0->0.8
                        }
                        scaleX = scale
                        scaleY = scale
                        
                        // 添加动态透明度效果
                        val alphaProgress = (rotation / 360f) % 1f
                        alpha = when {
                            alphaProgress < 0.2f -> 0.3f + (alphaProgress / 0.2f) * 0.7f // 0.3->1.0
                            alphaProgress < 0.4f -> 1.0f - ((alphaProgress - 0.2f) / 0.2f) * 0.4f // 1.0->0.6
                            alphaProgress < 0.6f -> 0.6f + ((alphaProgress - 0.4f) / 0.2f) * 0.4f // 0.6->1.0
                            alphaProgress < 0.8f -> 1.0f - ((alphaProgress - 0.6f) / 0.2f) * 0.3f // 1.0->0.7
                            else -> 0.7f - ((alphaProgress - 0.8f) / 0.2f) * 0.4f // 0.7->0.3
                        }
                    }
            ) {
                val paint = android.graphics.Paint().apply {
                    colorFilter = ColorFilter.colorMatrix(dynamicMatrix).asAndroidColorFilter()
                    textSize = 16.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
                
                drawContext.canvas.nativeCanvas.drawText(
                    "💎",
                    center.x,
                    center.y + (paint.textSize / 4),
                    paint
                )
            }
        }
    }
}

// Canvas自定义钻石绘制函数
fun DrawScope.drawCustomDiamond(color: Color, size: Float) {
    val centerX = size / 2
    val centerY = size / 2
    val radius = size / 3
    
    // 绘制钻石主体
    val diamondPath = Path().apply {
        moveTo(centerX, centerY - radius)
        lineTo(centerX + radius * 0.6f, centerY)
        lineTo(centerX, centerY + radius)
        lineTo(centerX - radius * 0.6f, centerY)
        close()
    }
    
    // 填充钻石主体
    drawPath(
        path = diamondPath,
        color = color,
        style = Fill
    )
    
    // 添加渐变效果
    drawPath(
        path = diamondPath,
        brush = Brush.linearGradient(
            colors = listOf(
                color.copy(alpha = 0.8f),
                color,
                color.copy(alpha = 0.6f)
            ),
            start = androidx.compose.ui.geometry.Offset(centerX - radius, centerY - radius),
            end = androidx.compose.ui.geometry.Offset(centerX + radius, centerY + radius)
        ),
        style = Fill
    )
    
    // 添加高光边框
    drawPath(
        path = diamondPath,
        color = Color.White.copy(alpha = 0.4f),
        style = Stroke(width = 1.dp.toPx())
    )
} 