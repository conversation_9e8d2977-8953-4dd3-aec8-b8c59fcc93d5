<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="background">#FFFFFBFF</color>
    <color name="badge_level_0">#FFF7FEF8</color>
    <color name="badge_level_1">#FFE6F9EA</color>
    <color name="badge_level_2">#FFD1F2DB</color>
    <color name="badge_level_3">#FFB8EBC5</color>
    <color name="badge_level_4">#FF9EE4AF</color>
    <color name="badge_level_5">#FF84DD99</color>
    <color name="badge_text_dark">#FF166534</color>
    <color name="badge_text_light">#FFFFFFFF</color>
    <color name="black">#FF000000</color>
    <color name="error">#FFEF4444</color>
    <color name="info">#FF3B82F6</color>
    <color name="on_background">#FF1C1B1F</color>
    <color name="on_surface">#FF1C1B1F</color>
    <color name="primary">#FF667EEA</color>
    <color name="primary_variant">#FF764BA2</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="secondary">#FF10B981</color>
    <color name="secondary_variant">#FF3B82F6</color>
    <color name="success">#FF10B981</color>
    <color name="surface">#FFFFFBFF</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="warning">#FFF59E0B</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">水幕视频课程</string>
    <string name="cache_management">缓存管理</string>
    <string name="home">首页</string>
    <string name="login">登录</string>
    <string name="logout">退出登录</string>
    <string name="my_profile">我的</string>
    <string name="settings">设置</string>
    <string name="video_player">视频播放</string>
    <string name="welcome_message">欢迎使用水幕视频课程App</string>
    <style name="Theme.ShuimuVideoCourseApp" parent="Theme.AppCompat.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_variant</item>
        <item name="colorAccent">@color/secondary</item>
        
        
        <item name="android:colorBackground">@color/background</item>
        
        
        <item name="android:statusBarColor" ns1:targetApi="l">@color/primary_variant</item>
    </style>
</resources>