package com.shuimu.videocourse.data.local;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HomeDataSource_Factory implements Factory<HomeDataSource> {
  @Override
  public HomeDataSource get() {
    return newInstance();
  }

  public static HomeDataSource_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static HomeDataSource newInstance() {
    return new HomeDataSource();
  }

  private static final class InstanceHolder {
    private static final HomeDataSource_Factory INSTANCE = new HomeDataSource_Factory();
  }
}
