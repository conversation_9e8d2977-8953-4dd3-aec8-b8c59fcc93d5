package com.shuimu.videocourse.di;

import com.shuimu.videocourse.data.local.dao.CacheDao;
import com.shuimu.videocourse.data.local.dao.CourseDao;
import com.shuimu.videocourse.data.local.dao.PurchaseDao;
import com.shuimu.videocourse.data.local.dao.VideoDao;
import com.shuimu.videocourse.data.local.dao.WatchProgressDao;
import com.shuimu.videocourse.data.remote.api.CourseApiService;
import com.shuimu.videocourse.data.repository.CourseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideCourseRepositoryFactory implements Factory<CourseRepository> {
  private final Provider<CourseDao> courseDaoProvider;

  private final Provider<VideoDao> videoDaoProvider;

  private final Provider<WatchProgressDao> watchProgressDaoProvider;

  private final Provider<PurchaseDao> purchaseDaoProvider;

  private final Provider<CacheDao> cacheDaoProvider;

  private final Provider<CourseApiService> courseApiServiceProvider;

  public DataModule_ProvideCourseRepositoryFactory(Provider<CourseDao> courseDaoProvider,
      Provider<VideoDao> videoDaoProvider, Provider<WatchProgressDao> watchProgressDaoProvider,
      Provider<PurchaseDao> purchaseDaoProvider, Provider<CacheDao> cacheDaoProvider,
      Provider<CourseApiService> courseApiServiceProvider) {
    this.courseDaoProvider = courseDaoProvider;
    this.videoDaoProvider = videoDaoProvider;
    this.watchProgressDaoProvider = watchProgressDaoProvider;
    this.purchaseDaoProvider = purchaseDaoProvider;
    this.cacheDaoProvider = cacheDaoProvider;
    this.courseApiServiceProvider = courseApiServiceProvider;
  }

  @Override
  public CourseRepository get() {
    return provideCourseRepository(courseDaoProvider.get(), videoDaoProvider.get(), watchProgressDaoProvider.get(), purchaseDaoProvider.get(), cacheDaoProvider.get(), courseApiServiceProvider.get());
  }

  public static DataModule_ProvideCourseRepositoryFactory create(
      Provider<CourseDao> courseDaoProvider, Provider<VideoDao> videoDaoProvider,
      Provider<WatchProgressDao> watchProgressDaoProvider,
      Provider<PurchaseDao> purchaseDaoProvider, Provider<CacheDao> cacheDaoProvider,
      Provider<CourseApiService> courseApiServiceProvider) {
    return new DataModule_ProvideCourseRepositoryFactory(courseDaoProvider, videoDaoProvider, watchProgressDaoProvider, purchaseDaoProvider, cacheDaoProvider, courseApiServiceProvider);
  }

  public static CourseRepository provideCourseRepository(CourseDao courseDao, VideoDao videoDao,
      WatchProgressDao watchProgressDao, PurchaseDao purchaseDao, CacheDao cacheDao,
      CourseApiService courseApiService) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideCourseRepository(courseDao, videoDao, watchProgressDao, purchaseDao, cacheDao, courseApiService));
  }
}
