package com.shuimu.videocourse.presentation.components.video

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.basic.BadgeComponent
import com.shuimu.videocourse.presentation.components.basic.ProgressBar
import com.shuimu.videocourse.presentation.components.basic.ShuimuButton
import com.shuimu.videocourse.presentation.components.state.CacheStatusIndicator
import com.shuimu.videocourse.presentation.components.state.CacheStatus
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 视频信息数据类
 */
data class VideoInfo(
    val title: String,
    val description: String? = null,
    val duration: String,
    val watchProgress: Float = 0f, // 0-100
    val watchCount: Int = 0,
    val isDownloaded: Boolean = false,
    val downloadProgress: Float? = null, // 0-100，null表示未下载
    val cacheStatus: String? = null,
    val category: String? = null,
    val videoIndex: Int = 1,
    val totalVideos: Int = 1
)

/**
 * 水幕视频课程App - 视频信息面板组件
 * 
 * 基于原型设计：UI Prototype/02-视频播放页.html 视频信息面板区域
 * 
 * 功能特性：
 * - 支持视频标题、描述、进度信息显示
 * - 实现操作按钮区域和缓存状态显示
 * - 结合ProgressBar和BadgeComponent
 * - 使用原型的#667eea紫蓝色渐变主题
 * 
 * @param videoInfo 视频信息
 * @param onPlayClick 播放按钮点击回调
 * @param onDownloadClick 下载按钮点击回调
 * @param onShareClick 分享按钮点击回调
 * @param showActions 是否显示操作按钮
 * @param modifier 修饰符
 */
@Composable
fun VideoInfoPanel(
    videoInfo: VideoInfo,
    onPlayClick: () -> Unit = {},
    onDownloadClick: () -> Unit = {},
    onShareClick: () -> Unit = {},
    showActions: Boolean = true,
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 视频标题和序号
            VideoTitleSection(videoInfo = videoInfo)
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 视频描述
            if (videoInfo.description != null) {
                VideoDescriptionSection(description = videoInfo.description)
                Spacer(modifier = Modifier.height(12.dp))
            }
            
            // 视频统计信息
            VideoStatsSection(videoInfo = videoInfo)
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 播放进度
            VideoProgressSection(videoInfo = videoInfo)
            
            if (showActions) {
                Spacer(modifier = Modifier.height(20.dp))
                
                // 操作按钮区域
                VideoActionButtons(
                    videoInfo = videoInfo,
                    onPlayClick = onPlayClick,
                    onDownloadClick = onDownloadClick,
                    onShareClick = onShareClick
                )
            }
        }
    }
}

/**
 * 视频标题区域组件
 */
@Composable
private fun VideoTitleSection(
    videoInfo: VideoInfo,
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }
    
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Top
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 视频标题
            Text(
                text = videoInfo.title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1F2937),
                lineHeight = 24.sp,
                maxLines = if (isExpanded) Int.MAX_VALUE else 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.clickable { isExpanded = !isExpanded }
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 视频序号和分类
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "${videoInfo.videoIndex}/${videoInfo.totalVideos}",
                    fontSize = 14.sp,
                    color = Color(0xFF6B7280),
                    fontWeight = FontWeight.Medium
                )
                
                if (videoInfo.category != null) {
                    Text(
                        text = "•",
                        fontSize = 14.sp,
                        color = Color(0xFF6B7280)
                    )
                    Text(
                        text = videoInfo.category,
                        fontSize = 14.sp,
                        color = Color(0xFF6B7280)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 观看次数徽章
        BadgeComponent(
            count = videoInfo.watchCount
        )
    }
}

/**
 * 视频描述区域组件
 */
@Composable
private fun VideoDescriptionSection(
    description: String,
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }
    val maxLines = if (isExpanded) Int.MAX_VALUE else 2
    
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = description,
            fontSize = 14.sp,
            color = Color(0xFF6B7280),
            lineHeight = 20.sp,
            maxLines = maxLines,
            overflow = TextOverflow.Ellipsis
        )
        
        if (description.length > 100) {
            Spacer(modifier = Modifier.height(4.dp))
            TextButton(
                onClick = { isExpanded = !isExpanded },
                contentPadding = PaddingValues(0.dp)
            ) {
                Text(
                    text = if (isExpanded) "收起" else "展开",
                    fontSize = 14.sp,
                    color = Color(0xFF667EEA)
                )
            }
        }
    }
}

/**
 * 视频统计信息区域组件
 */
@Composable
private fun VideoStatsSection(
    videoInfo: VideoInfo,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 视频时长
        VideoStatItem(
            label = "时长",
            value = videoInfo.duration
        )
        
        // 缓存状态
        if (videoInfo.cacheStatus != null) {
            VideoStatItem(
                label = "缓存",
                value = videoInfo.cacheStatus,
                valueColor = when {
                    videoInfo.cacheStatus.contains("已缓存") -> Color(0xFF10B981)
                    videoInfo.cacheStatus.contains("%") -> Color(0xFF3B82F6)
                    else -> Color(0xFF6B7280)
                }
            )
        }
        
        // 下载状态
        if (videoInfo.isDownloaded) {
            VideoStatItem(
                label = "状态",
                value = "已下载",
                valueColor = Color(0xFF10B981)
            )
        } else if (videoInfo.downloadProgress != null) {
            VideoStatItem(
                label = "下载",
                value = "${videoInfo.downloadProgress.toInt()}%",
                valueColor = Color(0xFF3B82F6)
            )
        }
    }
}

/**
 * 视频统计项组件
 */
@Composable
private fun VideoStatItem(
    label: String,
    value: String,
    valueColor: Color = Color(0xFF1F2937),
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "$label:",
            fontSize = 14.sp,
            color = Color(0xFF6B7280)
        )
        Text(
            text = value,
            fontSize = 14.sp,
            color = valueColor,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 视频进度区域组件
 */
@Composable
private fun VideoProgressSection(
    videoInfo: VideoInfo,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "观看进度",
                fontSize = 14.sp,
                color = Color(0xFF6B7280),
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "${videoInfo.watchProgress.toInt()}%",
                fontSize = 14.sp,
                color = Color(0xFF667EEA),
                fontWeight = FontWeight.SemiBold
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        ProgressBar(
            progress = videoInfo.watchProgress / 100f,
            showText = false,
            animated = true,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 视频操作按钮区域组件
 */
@Composable
private fun VideoActionButtons(
    videoInfo: VideoInfo,
    onPlayClick: () -> Unit,
    onDownloadClick: () -> Unit,
    onShareClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 播放按钮（主要操作）
        ShuimuButton(
            text = if (videoInfo.watchProgress > 0) "继续播放" else "开始播放",
            onClick = onPlayClick,
            modifier = Modifier.weight(1f)
        )
        
        // 下载按钮
        ShuimuButton(
            text = when {
                videoInfo.isDownloaded -> "已下载"
                videoInfo.downloadProgress != null -> "${videoInfo.downloadProgress.toInt()}%"
                else -> "下载"
            },
            onClick = onDownloadClick,
            type = com.shuimu.videocourse.presentation.components.basic.ShuimuButtonType.SECONDARY,
            enabled = !videoInfo.isDownloaded && videoInfo.downloadProgress == null,
            modifier = Modifier.weight(0.7f)
        )
        
        // 分享按钮
        IconButton(
            onClick = onShareClick,
            modifier = Modifier
                .size(48.dp)
                .background(
                    Color(0xFF667EEA).copy(alpha = 0.1f),
                    RoundedCornerShape(8.dp)
                )
        ) {
            Icon(
                imageVector = Icons.Default.Share,
                contentDescription = "分享",
                tint = Color(0xFF667EEA),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

/**
 * 预览组件
 */
@Composable
fun VideoInfoPanelPreview() {
    ShuimuTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF9FAFB))
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 基本视频信息
            VideoInfoPanel(
                videoInfo = VideoInfo(
                    title = "Android Jetpack Compose 基础入门",
                    description = "本节课程将带你了解Jetpack Compose的基本概念和核心特性，包括声明式UI、状态管理、组合函数等重要内容。通过实际案例演示，帮助你快速上手现代Android UI开发。",
                    duration = "25:30",
                    watchProgress = 65f,
                    watchCount = 3,
                    category = "基础教程",
                    videoIndex = 1,
                    totalVideos = 12,
                    cacheStatus = "已缓存"
                ),
                onPlayClick = { /* 播放视频 */ },
                onDownloadClick = { /* 下载视频 */ },
                onShareClick = { /* 分享视频 */ }
            )
            
            // 下载中的视频
            VideoInfoPanel(
                videoInfo = VideoInfo(
                    title = "状态管理与数据流",
                    duration = "18:45",
                    watchProgress = 0f,
                    watchCount = 0,
                    category = "进阶教程",
                    videoIndex = 5,
                    totalVideos = 12,
                    downloadProgress = 45f
                ),
                onPlayClick = { /* 播放视频 */ },
                onDownloadClick = { /* 下载视频 */ },
                onShareClick = { /* 分享视频 */ }
            )
            
            // 已下载的视频
            VideoInfoPanel(
                videoInfo = VideoInfo(
                    title = "自定义组件开发实战",
                    description = "深入学习如何创建可复用的自定义组件",
                    duration = "32:15",
                    watchProgress = 100f,
                    watchCount = 5,
                    category = "实战项目",
                    videoIndex = 8,
                    totalVideos = 12,
                    isDownloaded = true,
                    cacheStatus = "已缓存"
                ),
                showActions = false // 不显示操作按钮
            )
        }
    }
} 