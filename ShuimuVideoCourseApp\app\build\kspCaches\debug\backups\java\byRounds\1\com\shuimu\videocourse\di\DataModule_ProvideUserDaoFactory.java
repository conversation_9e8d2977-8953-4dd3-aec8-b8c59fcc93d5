package com.shuimu.videocourse.di;

import com.shuimu.videocourse.data.local.dao.UserDao;
import com.shuimu.videocourse.data.local.database.ShuimuDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideUserDaoFactory implements Factory<UserDao> {
  private final Provider<ShuimuDatabase> databaseProvider;

  public DataModule_ProvideUserDaoFactory(Provider<ShuimuDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public UserDao get() {
    return provideUserDao(databaseProvider.get());
  }

  public static DataModule_ProvideUserDaoFactory create(Provider<ShuimuDatabase> databaseProvider) {
    return new DataModule_ProvideUserDaoFactory(databaseProvider);
  }

  public static UserDao provideUserDao(ShuimuDatabase database) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideUserDao(database));
  }
}
