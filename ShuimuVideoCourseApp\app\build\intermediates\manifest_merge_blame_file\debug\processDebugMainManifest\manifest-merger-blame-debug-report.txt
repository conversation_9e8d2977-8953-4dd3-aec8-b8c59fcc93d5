1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.shuimu.videocourse"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:7:5-81
13-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:8:5-80
14-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:8:22-77
15
16    <permission
16-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.shuimu.videocourse.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.shuimu.videocourse.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:10:5-43:19
23        android:name="com.shuimu.videocourse.ShuimuApplication"
23-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:11:9-42
24        android:allowBackup="true"
24-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:12:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:13:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:14:9-54
30        android:icon="@android:drawable/sym_def_app_icon"
30-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:15:9-58
31        android:label="@string/app_name"
31-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:16:9-41
32        android:roundIcon="@android:drawable/sym_def_app_icon"
32-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:17:9-63
33        android:supportsRtl="true"
33-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:18:9-35
34        android:theme="@style/Theme.ShuimuVideoCourseApp" >
34-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:19:9-58
35        <activity
35-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:21:9-30:20
36            android:name="com.shuimu.videocourse.MainActivity"
36-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:22:13-41
37            android:exported="true"
37-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:23:13-36
38            android:label="@string/app_name"
38-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:24:13-45
39            android:theme="@style/Theme.ShuimuVideoCourseApp" >
39-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:25:13-62
40            <intent-filter>
40-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:26:13-29:29
41                <action android:name="android.intent.action.MAIN" />
41-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:17-69
41-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:17-77
43-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:27-74
44            </intent-filter>
45        </activity>
46
47        <!-- 徽章测试Activity -->
48        <activity
48-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:33:9-42:20
49            android:name="com.shuimu.videocourse.presentation.components.basic.BadgeTestActivity"
49-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:34:13-76
50            android:exported="true"
50-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:35:13-36
51            android:label="徽章测试"
51-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:36:13-33
52            android:theme="@style/Theme.ShuimuVideoCourseApp" >
52-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:37:13-62
53            <intent-filter>
53-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:26:13-29:29
54                <action android:name="android.intent.action.MAIN" />
54-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:17-69
54-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:17-77
56-->D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:27-74
57            </intent-filter>
58        </activity>
59        <activity
59-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
60            android:name="androidx.compose.ui.tooling.PreviewActivity"
60-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
61            android:exported="true" />
61-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
62
63        <provider
63-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
64            android:name="androidx.startup.InitializationProvider"
64-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
65            android:authorities="com.shuimu.videocourse.androidx-startup"
65-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
66            android:exported="false" >
66-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
67            <meta-data
67-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
68                android:name="androidx.emoji2.text.EmojiCompatInitializer"
68-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
69                android:value="androidx.startup" />
69-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
70            <meta-data
70-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
71-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
72                android:value="androidx.startup" />
72-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
73            <meta-data
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
75                android:value="androidx.startup" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
76        </provider>
77
78        <activity
78-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
79            android:name="androidx.activity.ComponentActivity"
79-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
80            android:exported="true" />
80-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
81
82        <service
82-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
83            android:name="androidx.room.MultiInstanceInvalidationService"
83-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
84            android:directBootAware="true"
84-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
85            android:exported="false" />
85-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
86
87        <receiver
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
88            android:name="androidx.profileinstaller.ProfileInstallReceiver"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
89            android:directBootAware="false"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
90            android:enabled="true"
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
91            android:exported="true"
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
92            android:permission="android.permission.DUMP" >
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
94                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
97                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
100                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
103                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
104            </intent-filter>
105        </receiver>
106    </application>
107
108</manifest>
