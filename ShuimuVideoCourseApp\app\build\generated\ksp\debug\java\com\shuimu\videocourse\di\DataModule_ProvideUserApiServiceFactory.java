package com.shuimu.videocourse.di;

import com.shuimu.videocourse.data.remote.api.UserApiService;
import com.shuimu.videocourse.data.remote.network.NetworkConfig;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideUserApiServiceFactory implements Factory<UserApiService> {
  private final Provider<NetworkConfig> networkConfigProvider;

  public DataModule_ProvideUserApiServiceFactory(Provider<NetworkConfig> networkConfigProvider) {
    this.networkConfigProvider = networkConfigProvider;
  }

  @Override
  public UserApiService get() {
    return provideUserApiService(networkConfigProvider.get());
  }

  public static DataModule_ProvideUserApiServiceFactory create(
      Provider<NetworkConfig> networkConfigProvider) {
    return new DataModule_ProvideUserApiServiceFactory(networkConfigProvider);
  }

  public static UserApiService provideUserApiService(NetworkConfig networkConfig) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideUserApiService(networkConfig));
  }
}
