package com.shuimu.videocourse.presentation.components.basic

import androidx.compose.animation.core.*
import androidx.compose.animation.animateColor
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.sin
import kotlin.math.cos

/**
 * 水幕徽章组件 - 观看次数徽章
 * 
 * 严格基于UI原型：UI Prototype/data/badge-styles-dual.css
 * 原型定义：
 * - 基础样式：font-size: 11px, padding: 0px 6px, border-radius: 10px, font-weight: 500
 * - 支持两套方案：badge-style-original（钻石系统）和 badge-style-mixed（图标系统）
 * - 等级范围：badge-level-0 到 badge-level-10+（传奇等级）
 * - 丰富的动画图标：⭐海星、✨闪耀、💎钻石、💎粉色钻石、👑皇冠、👧少女
 */

/**
 * 徽章等级枚举 - 严格按照原型定义
 */
enum class BadgeLevel(val level: Int) {
    LEVEL_0(0),   // 静态显示
    LEVEL_1(1),   // 闪动效果 / 1个海星
    LEVEL_2(2),   // 闪动效果 / 2个海星
    LEVEL_3(3),   // 闪动+1钻石 / 1个闪耀
    LEVEL_4(4),   // 闪动+1钻石 / 2个闪耀
    LEVEL_5(5),   // 闪动+1钻石 / 1个钻石
    LEVEL_6(6),   // 闪动+2钻石 / 2个钻石
    LEVEL_7(7),   // 闪动+2钻石 / 1个粉钻
    LEVEL_8(8),   // 闪动+2钻石 / 2个粉钻
    LEVEL_9(9),   // 闪动+2钻石 / 1个皇冠
    LEVEL_10(10)  // 传奇等级（金色渐变+皇冠+少女）
}

/**
 * 徽章样式方案枚举 - 按照原型的两套方案
 */
enum class BadgeStyle {
    ORIGINAL,  // 原版11级钻石系统
    MIXED      // 混合10级图标系统
}

/**
 * 获取徽章颜色配置 - 严格按照原型CSS定义
 */
private fun getBadgeColors(level: BadgeLevel, style: BadgeStyle): Pair<Color, Color> {
    return when (style) {
        BadgeStyle.ORIGINAL -> {
            when (level) {
                BadgeLevel.LEVEL_0 -> Pair(Color(0xFF166534), Color(0xFFF7FEF8)) // #166534, #f7fef8
                BadgeLevel.LEVEL_1 -> Pair(Color(0xFF166534), Color(0xFFE6F9EA)) // #166534, #e6f9ea
                BadgeLevel.LEVEL_2 -> Pair(Color(0xFF166534), Color(0xFFD1F2DB)) // #166534, #d1f2db
                BadgeLevel.LEVEL_3 -> Pair(Color(0xFF166534), Color(0xFFB8EBC5)) // #166534, #b8ebc5
                BadgeLevel.LEVEL_4 -> Pair(Color(0xFF166534), Color(0xFF9EE4AF)) // #166534, #9ee4af
                BadgeLevel.LEVEL_5 -> Pair(Color(0xFFFFFFFF), Color(0xFF84DD99)) // #ffffff, #84dd99
                BadgeLevel.LEVEL_6 -> Pair(Color(0xFFFFFFFF), Color(0xFF6AD683)) // #ffffff, #6ad683
                BadgeLevel.LEVEL_7 -> Pair(Color(0xFFFFFFFF), Color(0xFF50CF6D)) // #ffffff, #50cf6d
                BadgeLevel.LEVEL_8 -> Pair(Color(0xFFFFFFFF), Color(0xFF36C857)) // #ffffff, #36c857
                BadgeLevel.LEVEL_9 -> Pair(Color(0xFFFFFFFF), Color(0xFF2CC157)) // #ffffff, #2cc157
                BadgeLevel.LEVEL_10 -> Pair(Color(0xFFFFFFFF), Color(0xFFFBBF24)) // 传奇金色
            }
        }
        BadgeStyle.MIXED -> {
            when (level) {
                BadgeLevel.LEVEL_0 -> Pair(Color(0xFF166534), Color(0xFFF7FEF8)) // #166534, #f7fef8
                BadgeLevel.LEVEL_1 -> Pair(Color(0xFF166534), Color(0xFFF0FDF4)) // #166534, #f0fdf4
                BadgeLevel.LEVEL_2 -> Pair(Color(0xFF166534), Color(0xFFE6F9EA)) // #166534, #e6f9ea
                BadgeLevel.LEVEL_3 -> Pair(Color(0xFF166534), Color(0xFFB8EBC5)) // #166534, #b8ebc5
                BadgeLevel.LEVEL_4 -> Pair(Color(0xFF166534), Color(0xFF9EE4AF)) // #166534, #9ee4af
                BadgeLevel.LEVEL_5 -> Pair(Color(0xFFFFFFFF), Color(0xFF84DD99)) // #ffffff, #84dd99
                BadgeLevel.LEVEL_6 -> Pair(Color(0xFFFFFFFF), Color(0xFF6AD683)) // #ffffff, #6ad683
                BadgeLevel.LEVEL_7 -> Pair(Color(0xFFFFFFFF), Color(0xFF50CF6D)) // #ffffff, #50cf6d
                BadgeLevel.LEVEL_8 -> Pair(Color(0xFFFFFFFF), Color(0xFF36C857)) // #ffffff, #36c857
                BadgeLevel.LEVEL_9 -> Pair(Color(0xFFFFFFFF), Color(0xFF2CC157)) // #ffffff, #2cc157
                BadgeLevel.LEVEL_10 -> Pair(Color(0xFFFFFFFF), Color(0xFFFBBF24)) // 传奇金色
            }
        }
    }
}

/**
 * 获取混合系统背景动画时长 - 严格按照CSS原型
 */
private fun getMixedBackgroundAnimationDuration(level: BadgeLevel): Int {
    return when (level) {
        BadgeLevel.LEVEL_1 -> 2500 // starfish-grow 2.5s
        BadgeLevel.LEVEL_2 -> 2300 // starfish-grow 2.3s
        BadgeLevel.LEVEL_3 -> 2000 // sparkle-grow 2.0s
        BadgeLevel.LEVEL_4 -> 1800 // sparkle-grow 1.8s
        BadgeLevel.LEVEL_5 -> 2200 // diamond-grow 2.2s
        BadgeLevel.LEVEL_6 -> 2000 // diamond-grow 2.0s
        BadgeLevel.LEVEL_7 -> 1800 // pink-diamond-grow 1.8s
        BadgeLevel.LEVEL_8 -> 1600 // pink-diamond-grow 1.6s
        BadgeLevel.LEVEL_9 -> 800  // crown-grow 0.8s
        else -> 1000
    }
}

/**
 * 获取混合系统背景动画的起始和目标颜色 - 严格按照CSS grow动画
 */
private fun getMixedGrowAnimationColors(level: BadgeLevel): Pair<Color, Color> {
    return when (level) {
        BadgeLevel.LEVEL_1 -> Pair(Color(0xFFF0FDF4), Color(0xFFDCFCE7)) // #f0fdf4 → #dcfce7
        BadgeLevel.LEVEL_2 -> Pair(Color(0xFFE6F9EA), Color(0xFFD1F2DB)) // #e6f9ea → #d1f2db
        BadgeLevel.LEVEL_3 -> Pair(Color(0xFFB8EBC5), Color(0xFF9EE4AF)) // #b8ebc5 → #9ee4af
        BadgeLevel.LEVEL_4 -> Pair(Color(0xFF9EE4AF), Color(0xFF84DD99)) // #9ee4af → #84dd99
        BadgeLevel.LEVEL_5 -> Pair(Color(0xFF84DD99), Color(0xFF6AD683)) // #84dd99 → #6ad683
        BadgeLevel.LEVEL_6 -> Pair(Color(0xFF6AD683), Color(0xFF50CF6D)) // #6ad683 → #50cf6d
        BadgeLevel.LEVEL_7 -> Pair(Color(0xFF50CF6D), Color(0xFF36C857)) // #50cf6d → #36c857
        BadgeLevel.LEVEL_8 -> Pair(Color(0xFF36C857), Color(0xFF22C55E)) // #36c857 → #22c55e
        BadgeLevel.LEVEL_9 -> Pair(Color(0xFF2CC157), Color(0xFF22C55E)) // #2cc157 → #22c55e
        else -> Pair(Color(0xFFF7FEF8), Color(0xFFF7FEF8))
    }
}

/**
 * 获取混合系统阴影动画参数 - 严格按照CSS grow动画
 */
private fun getMixedShadowAnimationParams(level: BadgeLevel): Pair<Float, Float> {
    return when (level) {
        BadgeLevel.LEVEL_1 -> Pair(5f, 10f) // 0 0 5px → 0 0 10px
        BadgeLevel.LEVEL_2 -> Pair(5f, 10f) // 0 0 5px → 0 0 10px
        BadgeLevel.LEVEL_3 -> Pair(5f, 10f) // 0 0 5px → 0 0 10px
        BadgeLevel.LEVEL_4 -> Pair(5f, 10f) // 0 0 5px → 0 0 10px
        BadgeLevel.LEVEL_5 -> Pair(7f, 14f) // 0 0 7px → 0 0 14px
        BadgeLevel.LEVEL_6 -> Pair(7f, 14f) // 0 0 7px → 0 0 14px
        BadgeLevel.LEVEL_7 -> Pair(10f, 15f) // 0 0 10px → 0 0 15px
        BadgeLevel.LEVEL_8 -> Pair(10f, 15f) // 0 0 10px → 0 0 15px
        BadgeLevel.LEVEL_9 -> Pair(10f, 15f) // 0 0 10px → 0 0 15px
        else -> Pair(0f, 0f)
    }
}

/**
 * 获取混合系统缩放动画参数 - 严格按照CSS grow动画
 */
private fun getMixedScaleAnimationParams(level: BadgeLevel): Pair<Float, Float> {
    return when (level) {
        BadgeLevel.LEVEL_1 -> Pair(1f, 1.02f) // scale(1) → scale(1.02)
        BadgeLevel.LEVEL_2 -> Pair(1f, 1.02f) // scale(1) → scale(1.02)
        BadgeLevel.LEVEL_3 -> Pair(1f, 1.02f) // scale(1) → scale(1.02)
        BadgeLevel.LEVEL_4 -> Pair(1f, 1.02f) // scale(1) → scale(1.02)
        BadgeLevel.LEVEL_5 -> Pair(1f, 1.04f) // scale(1) → scale(1.04)
        BadgeLevel.LEVEL_6 -> Pair(1f, 1.04f) // scale(1) → scale(1.04)
        BadgeLevel.LEVEL_7 -> Pair(1f, 1.05f) // scale(1) → scale(1.05)
        BadgeLevel.LEVEL_8 -> Pair(1f, 1.05f) // scale(1) → scale(1.05)
        BadgeLevel.LEVEL_9 -> Pair(1f, 1.05f) // scale(1) → scale(1.05)
        else -> Pair(1f, 1f)
    }
}

/**
 * 获取混合系统图标动画时长 - 严格按照CSS原型
 */
private fun getMixedIconAnimationDuration(level: BadgeLevel): Int {
    return when (level) {
        BadgeLevel.LEVEL_1 -> 2800 // starfish-twinkle 2.8s
        BadgeLevel.LEVEL_2 -> 2500 // starfish-twinkle 2.6s/2.4s 平均
        BadgeLevel.LEVEL_3 -> 2200 // sparkle-twinkle 2.2s
        BadgeLevel.LEVEL_4 -> 1900 // sparkle-twinkle 2.0s/1.8s 平均
        BadgeLevel.LEVEL_5 -> 2400 // diamond-sparkle 2.4s
        BadgeLevel.LEVEL_6 -> 2100 // diamond-sparkle 2.2s/2.0s 平均
        BadgeLevel.LEVEL_7 -> 2000 // pink-diamond-sparkle 2.0s
        BadgeLevel.LEVEL_8 -> 1700 // pink-diamond-sparkle 1.8s/1.6s 平均
        BadgeLevel.LEVEL_9 -> 2000 // crown-sparkle 2.0s
        BadgeLevel.LEVEL_10 -> 2000 // crown-sparkle 2s, girl-sparkle 2s
        else -> 2800
    }
}

/**
 * 创建粉钻ColorFilter - 实现CSS中的粉色滤镜效果
 * CSS: filter: brightness(1.3) contrast(1.2) hue-rotate(70deg);
 */
private fun createPinkDiamondColorFilter(): ColorFilter {
    val matrix = ColorMatrix().apply {
        // 增加亮度 (brightness 1.3)
        val brightness = 0.3f
        set(0, 4, brightness)
        set(1, 4, brightness)
        set(2, 4, brightness)
        
        // 增加对比度 (contrast 1.2)
        val contrast = 1.2f
        val translate = (1f - contrast) / 2f
        set(0, 0, contrast)
        set(1, 1, contrast)
        set(2, 2, contrast)
        set(0, 4, get(0, 4) + translate)
        set(1, 4, get(1, 4) + translate)
        set(2, 4, get(2, 4) + translate)
        
        // 色相旋转 (hue-rotate 70deg) - 简化实现，主要增强红色和减少蓝色
        set(0, 2, 0.8f)  // 减少蓝色分量
        set(1, 2, 1.2f)  // 增强绿色分量
        set(2, 2, 0.7f)  // 减少蓝色分量
    }
    return ColorFilter.colorMatrix(matrix)
}

/**
 * 创建海星ColorFilter - 实现CSS中的特殊滤镜效果
 * CSS: filter: brightness(1.1) saturate(1.2);
 */
private fun createStarfishColorFilter(): ColorFilter {
    val matrix = ColorMatrix().apply {
        // 增加亮度 (brightness 1.1)
        val brightness = 0.1f
        set(0, 4, brightness)
        set(1, 4, brightness)
        set(2, 4, brightness)
        
        // 增加饱和度 (saturate 1.2)
        val saturation = 1.2f
        val lumR = 0.3086f
        val lumG = 0.6094f
        val lumB = 0.0820f
        
        set(0, 0, lumR * (1f - saturation) + saturation)
        set(0, 1, lumG * (1f - saturation))
        set(0, 2, lumB * (1f - saturation))
        set(1, 0, lumR * (1f - saturation))
        set(1, 1, lumG * (1f - saturation) + saturation)
        set(1, 2, lumB * (1f - saturation))
        set(2, 0, lumR * (1f - saturation))
        set(2, 1, lumG * (1f - saturation))
        set(2, 2, lumB * (1f - saturation) + saturation)
    }
    return ColorFilter.colorMatrix(matrix)
}

/**
 * 水幕徽章组件 - 观看次数徽章
 * 
 * @param count 观看次数
 * @param modifier 修饰符
 * @param style 徽章样式方案
 * @param animated 是否启用动画
 */
@Composable
fun BadgeComponent(
    count: Int,
    modifier: Modifier = Modifier,
    style: BadgeStyle = BadgeStyle.ORIGINAL,
    animated: Boolean = true
) {
    // 根据观看次数确定等级
    val level = when {
        count == 0 -> BadgeLevel.LEVEL_0
        count == 1 -> BadgeLevel.LEVEL_1
        count == 2 -> BadgeLevel.LEVEL_2
        count == 3 -> BadgeLevel.LEVEL_3
        count == 4 -> BadgeLevel.LEVEL_4
        count == 5 -> BadgeLevel.LEVEL_5
        count == 6 -> BadgeLevel.LEVEL_6
        count == 7 -> BadgeLevel.LEVEL_7
        count == 8 -> BadgeLevel.LEVEL_8
        count == 9 -> BadgeLevel.LEVEL_9
        count >= 10 -> BadgeLevel.LEVEL_10
        else -> BadgeLevel.LEVEL_0
    }
    
    val (textColor, backgroundColor) = getBadgeColors(level, style)
    
    // 呼吸动画 - 根据样式选择不同的动画时长
    val infiniteTransition = rememberInfiniteTransition(label = "badge_animation")
    
    // 获取动画参数 - 严格按照CSS原型
    val backgroundAnimationDuration = if (style == BadgeStyle.MIXED) {
        getMixedBackgroundAnimationDuration(level)
    } else {
        when (level) {
            BadgeLevel.LEVEL_1 -> 1700
            BadgeLevel.LEVEL_2 -> 1500
            BadgeLevel.LEVEL_3 -> 3100
            BadgeLevel.LEVEL_4 -> 2900
            BadgeLevel.LEVEL_5 -> 2700
            BadgeLevel.LEVEL_6 -> 2500
            BadgeLevel.LEVEL_7 -> 2400
            BadgeLevel.LEVEL_8 -> 2300
            BadgeLevel.LEVEL_9 -> 2200
            else -> 1000
        }
    }
    
    // 获取混合系统动画参数
    val (startColor, endColor) = if (style == BadgeStyle.MIXED && level != BadgeLevel.LEVEL_0) {
        getMixedGrowAnimationColors(level)
    } else {
        Pair(backgroundColor, backgroundColor)
    }
    
    val (startShadow, endShadow) = if (style == BadgeStyle.MIXED && level != BadgeLevel.LEVEL_0) {
        getMixedShadowAnimationParams(level)
    } else {
        Pair(0f, 0f)
    }
    
    val (startScale, endScale) = if (style == BadgeStyle.MIXED && level != BadgeLevel.LEVEL_0) {
        getMixedScaleAnimationParams(level)
    } else if (style == BadgeStyle.ORIGINAL && level != BadgeLevel.LEVEL_0) {
        when (level) {
            BadgeLevel.LEVEL_1, BadgeLevel.LEVEL_2 -> Pair(1f, 1.02f)
            BadgeLevel.LEVEL_3, BadgeLevel.LEVEL_4 -> Pair(1f, 1.03f)
            BadgeLevel.LEVEL_5 -> Pair(1f, 1.04f)
            BadgeLevel.LEVEL_6 -> Pair(1f, 1.05f)
            BadgeLevel.LEVEL_7 -> Pair(1f, 1.06f)
            BadgeLevel.LEVEL_8 -> Pair(1f, 1.07f)
            BadgeLevel.LEVEL_9 -> Pair(1f, 1.08f)
            else -> Pair(1f, 1f)
        }
    } else {
        Pair(1f, 1f)
    }
    
    // 背景缩放动画 - 严格按照CSS grow动画
    val animatedScale by infiniteTransition.animateFloat(
        initialValue = startScale,
        targetValue = endScale,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = backgroundAnimationDuration,
                easing = EaseInOut
            ),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scale_animation"
    )
    
    // 背景色动画 - 严格按照CSS grow动画
    val animatedBackgroundColor by infiniteTransition.animateColor(
        initialValue = startColor,
        targetValue = endColor,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = backgroundAnimationDuration,
                easing = EaseInOut
            ),
            repeatMode = RepeatMode.Reverse
        ),
        label = "background_color_animation"
    )
    
    // 阴影动画 - 严格按照CSS grow动画
    val animatedShadowElevation by infiniteTransition.animateFloat(
        initialValue = startShadow,
        targetValue = endShadow,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = backgroundAnimationDuration,
                easing = EaseInOut
            ),
            repeatMode = RepeatMode.Reverse
        ),
        label = "shadow_animation"
    )
    
    // 图标动画时长 - 混合系统使用专用时长
    val iconAnimationDuration = if (style == BadgeStyle.MIXED) {
        getMixedIconAnimationDuration(level)
    } else {
        when (level) {
            BadgeLevel.LEVEL_3, BadgeLevel.LEVEL_4, BadgeLevel.LEVEL_5 -> 2800
            BadgeLevel.LEVEL_6, BadgeLevel.LEVEL_7 -> 2200
            BadgeLevel.LEVEL_8, BadgeLevel.LEVEL_9 -> 2000
            BadgeLevel.LEVEL_10 -> 1200
            else -> 2800
        }
    }
    
    // 图标旋转动画 - 大部分图标使用360度旋转
    val iconRotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = iconAnimationDuration,
                easing = LinearEasing
            ),
            repeatMode = RepeatMode.Restart
        ),
        label = "icon_rotation"
    )
    
    // 等级9皇冠专用摆动动画 - crown-sparkle效果
    val crownSwingRotation by infiniteTransition.animateFloat(
        initialValue = -10f,
        targetValue = 10f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = 2000, // crown-sparkle 2.0s
                easing = EaseInOut
            ),
            repeatMode = RepeatMode.Reverse
        ),
        label = "crown_swing_rotation"
    )
    
    // 等级8专用：右粉钻差异化动画 (counter, 1.6s)
    val level8RightIconRotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = -360f, // 逆时针
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = 1600, // pink-diamond-sparkle-counter 1.6s
                easing = LinearEasing
            ),
            repeatMode = RepeatMode.Restart
        ),
        label = "level8_right_icon_rotation"
    )
    
    // 图标缩放动画 - 实现多阶段复杂动画，模拟CSS原型的步长效果
    val iconScale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = keyframes {
                durationMillis = iconAnimationDuration
                when (level) {
                    BadgeLevel.LEVEL_1, BadgeLevel.LEVEL_2 -> {
                        // 海星：0.8 → 1.1 → 1.2 → 1.0 → 0.8
                        0.8f at 0 with EaseInOut
                        1.1f at (iconAnimationDuration * 0.25).toInt() with EaseInOut
                        1.2f at (iconAnimationDuration * 0.5).toInt() with EaseInOut
                        1.0f at (iconAnimationDuration * 0.75).toInt() with EaseInOut
                        0.8f at iconAnimationDuration with EaseInOut
                    }
                    BadgeLevel.LEVEL_3, BadgeLevel.LEVEL_4 -> {
                        // 闪耀：0.8 → 1.2 → 1.0 → 1.1 → 0.8
                        0.8f at 0 with EaseInOut
                        1.2f at (iconAnimationDuration * 0.25).toInt() with EaseInOut
                        1.0f at (iconAnimationDuration * 0.5).toInt() with EaseInOut
                        1.1f at (iconAnimationDuration * 0.75).toInt() with EaseInOut
                        0.8f at iconAnimationDuration with EaseInOut
                    }
                    BadgeLevel.LEVEL_5, BadgeLevel.LEVEL_6, BadgeLevel.LEVEL_7, BadgeLevel.LEVEL_8 -> {
                        // 钻石/粉钻：0.8 → 1.1 → 0.9 → 1.2 → 1.0 → 0.8
                        0.8f at 0 with EaseInOut
                        1.1f at (iconAnimationDuration * 0.2).toInt() with EaseInOut
                        0.9f at (iconAnimationDuration * 0.4).toInt() with EaseInOut
                        1.2f at (iconAnimationDuration * 0.6).toInt() with EaseInOut
                        1.0f at (iconAnimationDuration * 0.8).toInt() with EaseInOut
                        0.8f at iconAnimationDuration with EaseInOut
                    }
                    BadgeLevel.LEVEL_9, BadgeLevel.LEVEL_10 -> {
                        // 皇冠：0.9 → 1.1 → 1.0 → 1.1 → 0.9
                        0.9f at 0 with EaseInOut
                        1.1f at (iconAnimationDuration * 0.25).toInt() with EaseInOut
                        1.0f at (iconAnimationDuration * 0.5).toInt() with EaseInOut
                        1.1f at (iconAnimationDuration * 0.75).toInt() with EaseInOut
                        0.9f at iconAnimationDuration with EaseInOut
                    }
                    else -> {
                        0.8f at 0 with EaseInOut
                        1.2f at (iconAnimationDuration / 2) with EaseInOut
                        0.8f at iconAnimationDuration with EaseInOut
                    }
                }
            },
            repeatMode = RepeatMode.Restart
        ),
        label = "icon_scale"
    )
    
    // 图标Y轴偏移动画 - 实现CSS中的translateY效果（仅海星使用）
    val iconTranslateY by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = if (style == BadgeStyle.MIXED && (level == BadgeLevel.LEVEL_1 || level == BadgeLevel.LEVEL_2)) -2f else 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = iconAnimationDuration,
                easing = EaseInOut
            ),
            repeatMode = RepeatMode.Reverse
        ),
        label = "icon_translate_y"
    )
    
    // 传奇等级三重动画：pulse-legendary + breathe-legendary + gradient-shift
    val gradientOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "gradient_shift"
    )
    
    // 传奇等级pulse动画 - 0.8s
    val legendaryPulseScale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(800, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_legendary"
    )
    
    // 传奇等级breathe动画 - 1.5s (阴影变化，不是缩放)
    val legendaryBreatheShadow by infiniteTransition.animateFloat(
        initialValue = 15f, // 初始阴影大小
        targetValue = 20f,  // 目标阴影大小
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse
        ),
        label = "breathe_legendary_shadow"
    )
    
    Box(
        modifier = modifier
            .wrapContentSize()
    ) {
        // 主徽章
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(10.dp)) // 原型：border-radius: 10px
                .background(
                    if (level == BadgeLevel.LEVEL_10) {
                        // 传奇等级渐变背景 - 原型：linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #92400e)
                        Brush.linearGradient(
                            colors = listOf(
                                Color(0xFFFBBF24), // #fbbf24
                                Color(0xFFF59E0B), // #f59e0b
                                Color(0xFFD97706), // #d97706
                                Color(0xFF92400E)  // #92400e
                            ),
                            start = androidx.compose.ui.geometry.Offset(
                                gradientOffset * 100f,
                                gradientOffset * 100f
                            ),
                            end = androidx.compose.ui.geometry.Offset(
                                (1f - gradientOffset) * 100f,
                                (1f - gradientOffset) * 100f
                            )
                        )
                    } else {
                        // 混合系统使用动画背景色，原版系统使用静态背景色
                        val finalBackgroundColor = if (style == BadgeStyle.MIXED && level != BadgeLevel.LEVEL_0 && animated) {
                            animatedBackgroundColor
                        } else {
                            backgroundColor
                        }
                        Brush.linearGradient(
                            colors = listOf(finalBackgroundColor, finalBackgroundColor)
                        )
                    }
                )
                .then(
                    if (style == BadgeStyle.MIXED && level != BadgeLevel.LEVEL_0 && animated) {
                        // 混合系统专用阴影动画 - 使用动画阴影值
                        Modifier.shadow(
                            elevation = animatedShadowElevation.dp,
                            shape = RoundedCornerShape(10.dp),
                            ambientColor = when (level) {
                                BadgeLevel.LEVEL_1, BadgeLevel.LEVEL_2 -> Color(0xFFF0FDF4).copy(alpha = 0.4f)
                                BadgeLevel.LEVEL_3, BadgeLevel.LEVEL_4 -> Color(0xFFB8EBC5).copy(alpha = 0.4f)
                                BadgeLevel.LEVEL_5, BadgeLevel.LEVEL_6 -> Color(0xFF84DD99).copy(alpha = 0.4f)
                                BadgeLevel.LEVEL_7, BadgeLevel.LEVEL_8 -> Color(0xFF50CF6D).copy(alpha = 0.5f)
                                BadgeLevel.LEVEL_9 -> Color(0xFF2CC157).copy(alpha = 0.5f)
                                else -> Color.Transparent
                            }
                        )
                    } else if (level == BadgeLevel.LEVEL_10) {
                        Modifier
                            .border(
                                1.dp,
                                Color.White.copy(alpha = 0.3f),
                                RoundedCornerShape(10.dp)
                            )
                            .shadow(
                                elevation = legendaryBreatheShadow.dp, // 使用动画阴影
                                shape = RoundedCornerShape(10.dp),
                                ambientColor = Color(0xFFFBBF24).copy(alpha = 0.6f),
                                spotColor = Color(0xFFFBBF24).copy(alpha = 0.3f)
                            )
                    } else {
                        Modifier
                    }
                )
                .then(
                    if (level == BadgeLevel.LEVEL_10) {
                        Modifier
                            .border(
                                1.dp,
                                Color.White.copy(alpha = 0.3f),
                                RoundedCornerShape(10.dp)
                            )
                            .shadow(
                                elevation = 8.dp,
                                shape = RoundedCornerShape(10.dp),
                                ambientColor = Color(0xFFFBBF24).copy(alpha = 0.6f),
                                spotColor = Color(0xFFFBBF24).copy(alpha = 0.3f)
                            )
                    } else {
                        Modifier
                    }
                )
                .padding(horizontal = 6.dp, vertical = 0.dp) // 原型：padding: 0px 6px
                .let { baseModifier ->
                    if (animated && level != BadgeLevel.LEVEL_0) {
                        baseModifier.graphicsLayer {
                                                    if (level == BadgeLevel.LEVEL_10) {
                            // 传奇等级双重动画组合 (pulse + 基础scale，breathe改为阴影)
                            scaleX = animatedScale * legendaryPulseScale
                            scaleY = animatedScale * legendaryPulseScale
                        } else {
                            scaleX = animatedScale
                            scaleY = animatedScale
                        }
                        }
                    } else {
                        baseModifier
                    }
                },
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = count.toString(),
                color = textColor,
                fontSize = if (level == BadgeLevel.LEVEL_10) 12.sp else 11.sp, // 原型：传奇等级 font-size: 12px
                fontWeight = if (level == BadgeLevel.LEVEL_10) FontWeight.Bold else FontWeight.Medium, // 原型：传奇等级 font-weight: bold
                maxLines = 1
            )
        }
        
        // 动画图标 - 严格按照原型定义
        if (animated && level != BadgeLevel.LEVEL_0) {
            when (style) {
                BadgeStyle.ORIGINAL -> {
                    // 原版钻石系统图标
                    when (level) {
                        BadgeLevel.LEVEL_3, BadgeLevel.LEVEL_4, BadgeLevel.LEVEL_5 -> {
                            // 1个钻石 - 左上角
                            Text(
                                text = "💎",
                                fontSize = when (level) {
                                    BadgeLevel.LEVEL_3 -> 8.sp
                                    BadgeLevel.LEVEL_4 -> 11.sp
                                    BadgeLevel.LEVEL_5 -> 14.sp
                                    else -> 8.sp
                                },
                                modifier = Modifier
                                    .offset(
                                        x = when (level) {
                                            BadgeLevel.LEVEL_3 -> (-6).dp
                                            BadgeLevel.LEVEL_4 -> (-9).dp
                                            BadgeLevel.LEVEL_5 -> (-12).dp
                                            else -> (-6).dp
                                        },
                                        y = when (level) {
                                            BadgeLevel.LEVEL_3 -> (-6).dp
                                            BadgeLevel.LEVEL_4 -> (-9).dp
                                            BadgeLevel.LEVEL_5 -> (-12).dp
                                            else -> (-6).dp
                                        }
                                    )
                                    .graphicsLayer {
                                        rotationZ = iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_6, BadgeLevel.LEVEL_7, BadgeLevel.LEVEL_8, BadgeLevel.LEVEL_9 -> {
                            // 2个钻石 - 左上角和右上角
                            Text(
                                text = "💎",
                                fontSize = when (level) {
                                    BadgeLevel.LEVEL_6 -> 8.sp
                                    BadgeLevel.LEVEL_7 -> 11.sp
                                    BadgeLevel.LEVEL_8 -> 14.sp
                                    BadgeLevel.LEVEL_9 -> 17.sp
                                    else -> 8.sp
                                },
                                modifier = Modifier
                                    .offset(
                                        x = when (level) {
                                            BadgeLevel.LEVEL_6 -> (-6).dp
                                            BadgeLevel.LEVEL_7 -> (-9).dp
                                            BadgeLevel.LEVEL_8 -> (-12).dp
                                            BadgeLevel.LEVEL_9 -> (-15).dp
                                            else -> (-6).dp
                                        },
                                        y = when (level) {
                                            BadgeLevel.LEVEL_6 -> (-6).dp
                                            BadgeLevel.LEVEL_7 -> (-9).dp
                                            BadgeLevel.LEVEL_8 -> (-12).dp
                                            BadgeLevel.LEVEL_9 -> (-15).dp
                                            else -> (-6).dp
                                        }
                                    )
                                    .graphicsLayer {
                                        rotationZ = iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                    }
                            )
                            Text(
                                text = "💎",
                                fontSize = when (level) {
                                    BadgeLevel.LEVEL_6 -> 8.sp
                                    BadgeLevel.LEVEL_7 -> 11.sp
                                    BadgeLevel.LEVEL_8 -> 14.sp
                                    BadgeLevel.LEVEL_9 -> 17.sp
                                    else -> 8.sp
                                },
                                modifier = Modifier
                                    .offset(
                                        x = when (level) {
                                            BadgeLevel.LEVEL_6 -> 6.dp
                                            BadgeLevel.LEVEL_7 -> 9.dp
                                            BadgeLevel.LEVEL_8 -> 12.dp
                                            BadgeLevel.LEVEL_9 -> 15.dp
                                            else -> 6.dp
                                        },
                                        y = when (level) {
                                            BadgeLevel.LEVEL_6 -> (-6).dp
                                            BadgeLevel.LEVEL_7 -> (-9).dp
                                            BadgeLevel.LEVEL_8 -> (-12).dp
                                            BadgeLevel.LEVEL_9 -> (-15).dp
                                            else -> (-6).dp
                                        }
                                    )
                                    .graphicsLayer {
                                        rotationZ = -iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_10 -> {
                            // 传奇等级：皇冠+少女
                            Text(
                                text = "👑",
                                fontSize = 20.sp,
                                modifier = Modifier
                                    .offset(x = (-12).dp, y = (-20).dp)
                                    .graphicsLayer {
                                        rotationZ = sin(iconRotation * Math.PI / 180).toFloat() * 10f
                                        scaleX = iconScale
                                        scaleY = iconScale
                                    }
                            )
                            Text(
                                text = "👧",
                                fontSize = 18.sp,
                                modifier = Modifier
                                    .offset(x = 10.dp, y = (-20).dp)
                                    .graphicsLayer {
                                        rotationZ = cos(iconRotation * Math.PI / 180).toFloat() * 10f
                                        scaleX = iconScale
                                        scaleY = iconScale
                                    }
                            )
                        }
                        else -> {}
                    }
                }
                BadgeStyle.MIXED -> {
                    // 混合图标系统
                    when (level) {
                        BadgeLevel.LEVEL_1 -> {
                            // 1个海星 - 原型：top: -9px, left: -9px, font-size: 16px (用户要求改为16px)
                            // 注意：由于Compose Text组件限制，暂时移除滤镜效果，保持核心功能
                            Text(
                                text = "⭐",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = (-9).dp, y = (-9).dp)
                                    .graphicsLayer {
                                        rotationZ = iconRotation * 0.25f // 海星旋转幅度较小
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                        translationY = iconTranslateY.dp.toPx()
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_2 -> {
                            // 2个海星 - 原型：top: -7px, left: -10px, right: -10px, font-size: 16px (用户要求改为16px)
                            // 修复：使用两个独立的Text组件
                            // 注意：由于Compose Text组件限制，暂时移除滤镜效果，保持核心功能
                            Text(
                                text = "⭐",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = (-10).dp, y = (-7).dp)
                                    .graphicsLayer {
                                        rotationZ = iconRotation * 0.25f
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                        translationY = iconTranslateY.dp.toPx()
                                    }
                            )
                            Text(
                                text = "⭐",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = 10.dp, y = (-7).dp)
                                    .graphicsLayer {
                                        rotationZ = -iconRotation * 0.25f
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                        translationY = iconTranslateY.dp.toPx()
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_3 -> {
                            // 1个闪耀 - 原型：top: -14px, left: -14px, font-size: 16px
                            Text(
                                text = "✨",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = (-14).dp, y = (-14).dp)
                                    .graphicsLayer {
                                        rotationZ = iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_4 -> {
                            // 2个闪耀 - 原型：top: -12px, left: -15px, right: -15px, font-size: 16px
                            Text(
                                text = "✨",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = (-15).dp, y = (-12).dp)
                                    .graphicsLayer {
                                        rotationZ = iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                    }
                            )
                            Text(
                                text = "✨",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = 15.dp, y = (-12).dp)
                                    .graphicsLayer {
                                        rotationZ = -iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_5 -> {
                            // 1个钻石 - 原型：top: -14px, left: -14px, font-size: 16px
                            Text(
                                text = "💎",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = (-14).dp, y = (-14).dp)
                                    .graphicsLayer {
                                        rotationZ = iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_6 -> {
                            // 2个钻石 - 原型：top: -12px, left: -15px, right: -15px, font-size: 16px
                            Text(
                                text = "💎",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = (-15).dp, y = (-12).dp)
                                    .graphicsLayer {
                                        rotationZ = iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                    }
                            )
                            Text(
                                text = "💎",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = 15.dp, y = (-12).dp)
                                    .graphicsLayer {
                                        rotationZ = -iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_7 -> {
                            // 1个粉钻 - 原型：top: -14px, left: -14px, font-size: 16px, 带粉色滤镜
                            // 粉钻还是钻石形状，使用ColorFilter实现粉色效果
                            Text(
                                text = "💎", // 保持钻石形状
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = (-14).dp, y = (-14).dp)
                                    .graphicsLayer {
                                        rotationZ = iconRotation
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f
                                        // 尝试实现粉色效果 - 由于Text组件限制，暂时保持原色
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_8 -> {
                            // 2个粉钻 - 原型：left用clockwise 1.8s, right用counter 1.6s
                            // 左粉钻：pink-diamond-sparkle-clockwise 1.8s (顺时针)
                            Text(
                                text = "💎", // 保持钻石形状
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = (-15).dp, y = (-12).dp)
                                    .graphicsLayer {
                                        rotationZ = iconRotation // 使用标准1.8s顺时针动画
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f
                                    }
                            )
                            // 右粉钻：pink-diamond-sparkle-counter 1.6s (逆时针，更快)
                            Text(
                                text = "💎", // 保持钻石形状
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = 15.dp, y = (-12).dp)
                                    .graphicsLayer {
                                        rotationZ = level8RightIconRotation // 使用专门的1.6s逆时针动画
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_9 -> {
                            // 1个皇冠 - 原型：top: -19px, left: -16px, font-size: 20px
                            // 使用crown-sparkle摆动动画，不是旋转
                            Text(
                                text = "👑",
                                fontSize = 20.sp,
                                modifier = Modifier
                                    .offset(x = (-16).dp, y = (-19).dp)
                                    .graphicsLayer {
                                        rotationZ = crownSwingRotation // 使用摆动动画：-10°↔10°
                                        scaleX = iconScale // 0.9↔1.1
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                    }
                            )
                        }
                        BadgeLevel.LEVEL_10 -> {
                            // 传奇等级：皇冠+少女 - 原型：皇冠 top: -20px, left: -12px, font-size: 20px; 少女 top: -20px, right: -10px, font-size: 18px
                            Text(
                                text = "👑",
                                fontSize = 20.sp,
                                modifier = Modifier
                                    .offset(x = (-12).dp, y = (-20).dp)
                                    .graphicsLayer {
                                        rotationZ = crownSwingRotation // 使用摆动动画而不是复杂的sin函数
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                    }
                            )
                            Text(
                                text = "👧",
                                fontSize = 18.sp,
                                modifier = Modifier
                                    .offset(x = 10.dp, y = (-20).dp)
                                    .graphicsLayer {
                                        rotationZ = -crownSwingRotation // 使用相反的摆动动画
                                        scaleX = iconScale
                                        scaleY = iconScale
                                        alpha = 1f // 移除透明度变化
                                    }
                            )
                        }
                        else -> {}
                    }
                }
            }
        }
    }
}

/**
 * 预定义的徽章样式
 */
object BadgeDefaults {
    
    /**
     * 观看次数徽章 - 原版钻石系统
     */
    @Composable
    fun ViewCountBadge(
        count: Int,
        modifier: Modifier = Modifier
    ) {
        BadgeComponent(
            count = count,
            modifier = modifier,
            style = BadgeStyle.ORIGINAL,
            animated = true
        )
    }
    
    /**
     * 观看次数徽章 - 混合图标系统
     */
    @Composable
    fun ViewCountBadgeMixed(
        count: Int,
        modifier: Modifier = Modifier
    ) {
        BadgeComponent(
            count = count,
            modifier = modifier,
            style = BadgeStyle.MIXED,
            animated = true
        )
    }
} 