package com.shuimu.videocourse.presentation.components

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

/**
 * 水幕视频课程App - 组件注册管理系统
 * 
 * 核心功能：
 * - 23个核心组件的统一注册和分类管理
 * - 智能组件搜索和推荐机制
 * - 组件使用验证和统计系统
 * - 组件依赖关系分析功能
 * - 强制组件复用保障机制
 */

/**
 * 组件分类枚举
 */
enum class ComponentCategory(val displayName: String, val description: String) {
    BASIC("基础组件", "提供基础UI元素，如按钮、卡片、输入框等"),
    DISPLAY("展示组件", "用于内容展示的组件，如视频项、分类卡片等"),
    NAVIGATION("导航组件", "页面导航相关组件，如顶部栏、底部导航等"),
    MODAL("弹窗组件", "弹窗和对话框组件，如分享、支付、购买弹窗等"),
    VIDEO("视频组件", "视频播放相关组件，如信息面板、播放列表等"),
    USER("用户组件", "用户信息相关组件，如头像、资料、菜单等"),
    STATE("状态组件", "状态展示组件，如加载、错误、缓存状态等")
}

/**
 * 组件状态枚举
 */
enum class ComponentStatus {
    ACTIVE,      // 活跃使用
    DEPRECATED,  // 已废弃
    BETA,        // 测试版本
    STABLE       // 稳定版本
}

/**
 * 组件信息数据类
 */
data class ComponentInfo(
    val name: String,                           // 组件名称
    val category: ComponentCategory,            // 组件分类
    val filePath: String,                      // 文件路径
    val description: String,                   // 组件描述
    val useCases: List<String>,               // 使用场景
    val dependencies: List<String>,           // 依赖组件
    val status: ComponentStatus,              // 组件状态
    val version: String,                      // 版本号
    val lastUpdated: String,                  // 最后更新时间
    val usageCount: Int = 0                   // 使用次数统计
)

/**
 * 组件使用验证结果
 */
data class ComponentValidationResult(
    val isValid: Boolean,                     // 是否符合规范
    val suggestions: List<String>,            // 建议使用的组件
    val reasons: List<String>                 // 不符合规范的原因
)

/**
 * 组件注册管理系统
 */
object ComponentRegistry {
    
    // 组件注册表
    private val components = mutableMapOf<String, ComponentInfo>()
    
    // 使用统计
    private val usageStats = mutableMapOf<String, Int>()
    
    init {
        registerAllComponents()
    }
    
    /**
     * 注册所有23个核心组件
     */
    private fun registerAllComponents() {
        // 基础组件（5个）
        registerComponent(ComponentInfo(
            name = "ShuimuButton",
            category = ComponentCategory.BASIC,
            filePath = "presentation/components/basic/ShuimuButton.kt",
            description = "统一按钮组件，支持多种样式和状态",
            useCases = listOf("表单提交", "操作确认", "导航跳转", "功能触发"),
            dependencies = emptyList(),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "ShuimuCard",
            category = ComponentCategory.BASIC,
            filePath = "presentation/components/basic/ShuimuCard.kt",
            description = "统一卡片组件，支持阴影和圆角",
            useCases = listOf("内容容器", "信息展示", "分组布局", "列表项"),
            dependencies = emptyList(),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "ShuimuTextField",
            category = ComponentCategory.BASIC,
            filePath = "presentation/components/basic/ShuimuTextField.kt",
            description = "统一输入框组件，支持验证和多种样式",
            useCases = listOf("用户输入", "表单填写", "搜索框", "密码输入"),
            dependencies = emptyList(),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "BadgeComponent",
            category = ComponentCategory.BASIC,
            filePath = "presentation/components/basic/BadgeComponent.kt",
            description = "徽章组件，支持等级显示和动画效果",
            useCases = listOf("观看次数", "等级显示", "状态标识", "数量提示"),
            dependencies = emptyList(),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "ProgressBar",
            category = ComponentCategory.BASIC,
            filePath = "presentation/components/basic/ProgressBar.kt",
            description = "进度条组件，支持多种样式和动画",
            useCases = listOf("学习进度", "下载进度", "加载状态", "完成度显示"),
            dependencies = emptyList(),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        // 展示组件（4个）
        registerComponent(ComponentInfo(
            name = "VideoItem",
            category = ComponentCategory.DISPLAY,
            filePath = "presentation/components/display/VideoItem.kt",
            description = "视频项展示组件，支持购买状态和播放进度",
            useCases = listOf("视频列表", "课程展示", "播放历史", "搜索结果"),
            dependencies = listOf("BadgeComponent", "ProgressBar"),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "CategoryCard",
            category = ComponentCategory.DISPLAY,
            filePath = "presentation/components/display/CategoryCard.kt",
            description = "分类卡片组件，支持展开收起和视频列表",
            useCases = listOf("分类展示", "课程分组", "内容分类", "层级导航"),
            dependencies = listOf("ShuimuCard", "VideoItem", "BadgeComponent"),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "SearchItem",
            category = ComponentCategory.DISPLAY,
            filePath = "presentation/components/display/SearchItem.kt",
            description = "搜索结果项组件，统一搜索结果样式",
            useCases = listOf("搜索结果", "推荐内容", "相关视频", "历史搜索"),
            dependencies = listOf("BadgeComponent"),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "SeriesHeader",
            category = ComponentCategory.DISPLAY,
            filePath = "presentation/components/display/SeriesHeader.kt",
            description = "系列头部组件，支持价格显示和状态标识",
            useCases = listOf("系列展示", "课程头部", "价格展示", "购买状态"),
            dependencies = listOf("ShuimuButton"),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        // 导航组件（2个）
        registerComponent(ComponentInfo(
            name = "TopAppBar",
            category = ComponentCategory.NAVIGATION,
            filePath = "presentation/components/navigation/TopAppBar.kt",
            description = "顶部应用栏组件，支持标题、返回和操作按钮",
            useCases = listOf("页面标题", "导航返回", "操作入口", "状态展示"),
            dependencies = emptyList(),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "BottomNavigationBar",
            category = ComponentCategory.NAVIGATION,
            filePath = "presentation/components/navigation/BottomNavigationBar.kt",
            description = "底部导航栏组件，支持多页面切换",
            useCases = listOf("主导航", "页面切换", "功能入口", "状态指示"),
            dependencies = emptyList(),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        // 弹窗组件（3个）
        registerComponent(ComponentInfo(
            name = "ShareModal",
            category = ComponentCategory.MODAL,
            filePath = "presentation/components/modal/ShareModal.kt",
            description = "分享弹窗组件，支持多种分享方式",
            useCases = listOf("内容分享", "链接分享", "社交分享", "收益分享"),
            dependencies = listOf("ShuimuButton"),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "PaymentModal",
            category = ComponentCategory.MODAL,
            filePath = "presentation/components/modal/PaymentModal.kt",
            description = "支付结果弹窗组件，显示支付状态和结果",
            useCases = listOf("支付结果", "交易状态", "错误提示", "成功确认"),
            dependencies = listOf("ShuimuButton", "LoadingIndicator"),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "PurchaseModal",
            category = ComponentCategory.MODAL,
            filePath = "presentation/components/modal/PurchaseModal.kt",
            description = "购买确认弹窗组件，支持支付方式选择",
            useCases = listOf("购买确认", "支付选择", "价格展示", "订单确认"),
            dependencies = listOf("ShuimuButton", "ShuimuCard"),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        // 视频组件（2个）
        registerComponent(ComponentInfo(
            name = "VideoInfoPanel",
            category = ComponentCategory.VIDEO,
            filePath = "presentation/components/video/VideoInfoPanel.kt",
            description = "视频信息面板组件，显示视频详情和操作",
            useCases = listOf("视频详情", "播放信息", "操作面板", "进度显示"),
            dependencies = listOf("BadgeComponent", "ProgressBar", "ShuimuButton"),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "PlaylistPanel",
            category = ComponentCategory.VIDEO,
            filePath = "presentation/components/video/PlaylistPanel.kt",
            description = "播放列表面板组件，支持视频列表和切换",
            useCases = listOf("播放列表", "视频切换", "播放队列", "连续播放"),
            dependencies = listOf("VideoItem"),
            status = ComponentStatus.STABLE,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        // 用户组件（4个）
        registerComponent(ComponentInfo(
            name = "UserProfileHeader",
            category = ComponentCategory.USER,
            filePath = "presentation/components/user/UserProfileHeader.kt",
            description = "用户资料头部组件，显示用户信息和统计",
            useCases = listOf("用户资料", "个人中心", "统计展示", "等级显示"),
            dependencies = listOf("UserAvatar", "BadgeComponent"),
            status = ComponentStatus.COMPLETED,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "UserAvatar",
            category = ComponentCategory.USER,
            filePath = "presentation/components/user/UserAvatar.kt",
            description = "用户头像组件，支持多种尺寸和状态",
            useCases = listOf("用户头像", "个人标识", "评论头像", "作者信息"),
            dependencies = emptyList(),
            status = ComponentStatus.COMPLETED,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "MenuItem",
            category = ComponentCategory.USER,
            filePath = "presentation/components/user/MenuItem.kt",
            description = "菜单项组件，支持图标和操作",
            useCases = listOf("设置菜单", "功能列表", "操作选项", "导航菜单"),
            dependencies = emptyList(),
            status = ComponentStatus.COMPLETED,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "SettingItem",
            category = ComponentCategory.USER,
            filePath = "presentation/components/user/SettingItem.kt",
            description = "设置项组件，支持开关和选择",
            useCases = listOf("应用设置", "用户偏好", "功能开关", "配置选项"),
            dependencies = emptyList(),
            status = ComponentStatus.COMPLETED,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        // 状态组件（3个）
        registerComponent(ComponentInfo(
            name = "LoadingIndicator",
            category = ComponentCategory.STATE,
            filePath = "presentation/components/status/LoadingIndicator.kt",
            description = "加载指示器组件，支持多种加载样式",
            useCases = listOf("页面加载", "数据请求", "操作等待", "内容刷新"),
            dependencies = emptyList(),
            status = ComponentStatus.COMPLETED,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "ErrorMessage",
            category = ComponentCategory.STATE,
            filePath = "presentation/components/status/ErrorMessage.kt",
            description = "错误消息组件，支持重试和错误类型",
            useCases = listOf("错误提示", "异常处理", "重试操作", "状态反馈"),
            dependencies = listOf("ShuimuButton"),
            status = ComponentStatus.COMPLETED,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
        
        registerComponent(ComponentInfo(
            name = "CacheStatusIndicator",
            category = ComponentCategory.STATE,
            filePath = "presentation/components/status/CacheStatusIndicator.kt",
            description = "缓存状态指示器组件，显示缓存状态和下载进度",
            useCases = listOf("缓存状态", "下载进度", "离线标识", "存储管理"),
            dependencies = emptyList(),
            status = ComponentStatus.COMPLETED,
            version = "1.0.0",
            lastUpdated = "2025-06-14"
        ))
    }
    
    /**
     * 注册单个组件
     */
    private fun registerComponent(componentInfo: ComponentInfo) {
        components[componentInfo.name] = componentInfo
    }
    
    /**
     * 获取所有组件
     */
    fun getAllComponents(): List<ComponentInfo> {
        return components.values.toList()
    }
    
    /**
     * 根据名称获取组件信息
     */
    fun getComponentInfo(name: String): ComponentInfo? {
        return components[name]
    }
    
    /**
     * 根据分类获取组件列表
     */
    fun getComponentsByCategory(category: ComponentCategory): List<ComponentInfo> {
        return components.values.filter { it.category == category }
    }
    
    /**
     * 智能搜索组件
     * 支持按名称、描述、使用场景搜索
     */
    fun findComponentsByUseCase(useCase: String): List<ComponentInfo> {
        val keyword = useCase.lowercase()
        return components.values.filter { component ->
            component.name.lowercase().contains(keyword) ||
            component.description.lowercase().contains(keyword) ||
            component.useCases.any { it.lowercase().contains(keyword) }
        }.sortedByDescending { it.usageCount }
    }
    
    /**
     * 获取组件依赖关系
     */
    fun getComponentDependencies(componentName: String): List<ComponentInfo> {
        val component = components[componentName] ?: return emptyList()
        return component.dependencies.mapNotNull { components[it] }
    }
    
    /**
     * 查找依赖某个组件的其他组件
     */
    fun findDependentComponents(componentName: String): List<ComponentInfo> {
        return components.values.filter { it.dependencies.contains(componentName) }
    }
    
    /**
     * 验证组件使用是否符合规范
     */
    fun validateComponentUsage(newComponentName: String): ComponentValidationResult {
        // 检查是否已存在相似功能的组件
        val suggestions = mutableListOf<String>()
        val reasons = mutableListOf<String>()
        
        // 基于名称相似度检查
        val similarComponents = components.values.filter { existing ->
            val similarity = calculateNameSimilarity(newComponentName, existing.name)
            similarity > 0.6 // 相似度阈值
        }
        
        if (similarComponents.isNotEmpty()) {
            reasons.add("发现相似功能的现有组件")
            suggestions.addAll(similarComponents.map { "${it.name} - ${it.description}" })
        }
        
        // 基于功能关键词检查
        val keywords = extractKeywords(newComponentName)
        val functionalMatches = components.values.filter { existing ->
            keywords.any { keyword ->
                existing.name.lowercase().contains(keyword) ||
                existing.description.lowercase().contains(keyword) ||
                existing.useCases.any { useCase -> useCase.lowercase().contains(keyword) }
            }
        }
        
        if (functionalMatches.isNotEmpty()) {
            reasons.add("发现具有相似功能的现有组件")
            suggestions.addAll(functionalMatches.map { "${it.name} - ${it.description}" })
        }
        
        val isValid = suggestions.isEmpty()
        
        if (!isValid) {
            reasons.add("建议使用现有组件而非创建新组件")
            reasons.add("如需修改，请更新现有组件以满足需求")
        }
        
        return ComponentValidationResult(
            isValid = isValid,
            suggestions = suggestions.distinct(),
            reasons = reasons
        )
    }
    
    /**
     * 记录组件使用
     */
    fun recordComponentUsage(componentName: String) {
        usageStats[componentName] = usageStats.getOrDefault(componentName, 0) + 1
    }
    
    /**
     * 获取使用统计
     */
    fun getUsageStats(): Map<String, Int> {
        return usageStats.toMap()
    }
    
    /**
     * 生成组件使用报告
     */
    fun generateUsageReport(): String {
        val report = StringBuilder()
        report.appendLine("# 水幕视频课程App - 组件使用报告")
        report.appendLine()
        
        // 总体统计
        report.appendLine("## 📊 总体统计")
        report.appendLine("- 注册组件总数: ${components.size}")
        report.appendLine("- 活跃组件数量: ${components.values.count { it.status == ComponentStatus.ACTIVE }}")
        report.appendLine("- 稳定组件数量: ${components.values.count { it.status == ComponentStatus.STABLE }}")
        report.appendLine()
        
        // 分类统计
        report.appendLine("## 📋 分类统计")
        ComponentCategory.values().forEach { category ->
            val categoryComponents = getComponentsByCategory(category)
            report.appendLine("- ${category.displayName}: ${categoryComponents.size} 个组件")
        }
        report.appendLine()
        
        // 使用频率排行
        report.appendLine("## 🏆 使用频率排行")
        val sortedUsage = usageStats.toList().sortedByDescending { it.second }
        sortedUsage.take(10).forEachIndexed { index, (name, count) ->
            report.appendLine("${index + 1}. $name: $count 次")
        }
        report.appendLine()
        
        // 依赖关系分析
        report.appendLine("## 🔗 依赖关系分析")
        val dependencyCount = components.values.map { it.name to it.dependencies.size }.sortedByDescending { it.second }
        dependencyCount.take(5).forEach { (name, count) ->
            if (count > 0) {
                report.appendLine("- $name: 依赖 $count 个组件")
            }
        }
        
        return report.toString()
    }
    
    /**
     * 计算名称相似度
     */
    private fun calculateNameSimilarity(name1: String, name2: String): Double {
        val s1 = name1.lowercase()
        val s2 = name2.lowercase()
        
        // 简单的编辑距离算法
        val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }
        
        for (i in 0..s1.length) dp[i][0] = i
        for (j in 0..s2.length) dp[0][j] = j
        
        for (i in 1..s1.length) {
            for (j in 1..s2.length) {
                dp[i][j] = if (s1[i - 1] == s2[j - 1]) {
                    dp[i - 1][j - 1]
                } else {
                    1 + minOf(dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1])
                }
            }
        }
        
        val maxLength = maxOf(s1.length, s2.length)
        return if (maxLength == 0) 1.0 else 1.0 - dp[s1.length][s2.length].toDouble() / maxLength
    }
    
    /**
     * 提取关键词
     */
    private fun extractKeywords(name: String): List<String> {
        return name.lowercase()
            .replace(Regex("[^a-z]"), " ")
            .split(" ")
            .filter { it.length > 2 }
    }
    
    /**
     * 获取推荐组件
     */
    fun getRecommendedComponents(context: String): List<ComponentInfo> {
        return findComponentsByUseCase(context).take(5)
    }
    
    /**
     * 检查组件完整性
     */
    fun validateComponentIntegrity(): List<String> {
        val issues = mutableListOf<String>()
        
        // 检查依赖完整性
        components.values.forEach { component ->
            component.dependencies.forEach { dependency ->
                if (!components.containsKey(dependency)) {
                    issues.add("组件 ${component.name} 依赖的组件 $dependency 不存在")
                }
            }
        }
        
        // 检查循环依赖
        components.values.forEach { component ->
            if (hasCircularDependency(component.name, mutableSetOf())) {
                issues.add("组件 ${component.name} 存在循环依赖")
            }
        }
        
        return issues
    }
    
    /**
     * 检查循环依赖
     */
    private fun hasCircularDependency(componentName: String, visited: MutableSet<String>): Boolean {
        if (visited.contains(componentName)) return true
        
        visited.add(componentName)
        val component = components[componentName] ?: return false
        
        component.dependencies.forEach { dependency ->
            if (hasCircularDependency(dependency, visited)) return true
        }
        
        visited.remove(componentName)
        return false
    }
    
    /**
     * 🚨 强制组件使用检查 🚨
     * 检查组件是否已存在，防止重复创建
     * 
     * @param componentName 要创建的组件名称
     * @return 如果组件已存在，返回警告信息；否则返回null
     */
    fun checkComponentExists(componentName: String): String? {
        // 检查完全匹配的组件名
        val exactMatch = components.find { 
            it.name.equals(componentName, ignoreCase = true)
        }
        
        if (exactMatch != null) {
            return """
                🚨 组件 '$componentName' 已存在！
                
                📍 现有组件信息：
                • 名称：${exactMatch.name}
                • 位置：${exactMatch.filePath}
                • 描述：${exactMatch.description}
                • 用途：${exactMatch.useCases.joinToString(", ")}
                • 状态：${exactMatch.status}
                
                ❌ 禁止创建重复组件！
                ✅ 请使用现有组件：${exactMatch.name}
                
                📖 使用示例请参考组件文档
            """.trimIndent()
        }
        
        // 检查功能相似的组件
        val similarComponents = components.filter { component ->
            // 检查名称相似性
            val nameMatch = component.name.contains(componentName, ignoreCase = true) ||
                           componentName.contains(component.name, ignoreCase = true)
            
            // 检查描述相似性
            val descriptionMatch = component.description.contains(componentName, ignoreCase = true) ||
                                 componentName.lowercase().let { name ->
                                     component.description.lowercase().contains(name) ||
                                     component.useCases.any { it.lowercase().contains(name) }
                                 }
            
            nameMatch || descriptionMatch
        }
        
        if (similarComponents.isNotEmpty()) {
            val suggestions = similarComponents.joinToString("\n") { component ->
                "  • ${component.name} - ${component.description}"
            }
            
            return """
                ⚠️ 发现功能相似的组件！
                
                🔍 相似组件列表：
                $suggestions
                
                💡 建议：
                1. 检查上述组件是否满足需求
                2. 如果满足，请使用现有组件
                3. 如果不满足，考虑扩展现有组件功能
                4. 避免创建功能重复的新组件
                
                📋 查看所有组件：ComponentRegistry.showAllComponents()
            """.trimIndent()
        }
        
        return null
    }
    
    /**
     * 显示所有可用组件的详细信息
     */
    fun showAllComponents(): String {
        val componentsByCategory = components.groupBy { it.category }
        
        return buildString {
            appendLine("📋 水幕视频课程App - 所有可用组件")
            appendLine("=" * 50)
            
            componentsByCategory.forEach { (category, components) ->
                appendLine("\n${getCategoryIcon(category)} ${category.displayName}（${components.size}个）")
                appendLine("-" * 30)
                
                components.forEach { component ->
                    appendLine("• ${component.name}")
                    appendLine("  📍 ${component.filePath}")
                    appendLine("  📝 ${component.description}")
                    appendLine("  🎯 ${component.useCases.joinToString(", ")}")
                    appendLine("  📊 ${component.status} | v${component.version}")
                    appendLine()
                }
            }
            
            appendLine("🔍 使用检查：ComponentRegistry.checkComponentExists(\"组件名\")")
            appendLine("📖 详细文档：.cursor/rules/组件使用保障规范.mdc")
        }
    }
    
    /**
     * 获取分类图标
     */
    private fun getCategoryIcon(category: ComponentCategory): String {
        return when (category) {
            ComponentCategory.BASIC -> "🔧"
            ComponentCategory.DISPLAY -> "📱"
            ComponentCategory.NAVIGATION -> "🧭"
            ComponentCategory.MODAL -> "🔔"
            ComponentCategory.VIDEO -> "🎬"
            ComponentCategory.USER -> "👤"
            ComponentCategory.STATE -> "⚡"
        }
    }
    
    /**
     * 快速搜索组件
     */
    fun searchComponents(keyword: String): List<ComponentInfo> {
        return components.filter { component ->
            component.name.contains(keyword, ignoreCase = true) ||
            component.description.contains(keyword, ignoreCase = true) ||
            component.useCases.any { it.contains(keyword, ignoreCase = true) }
        }
    }
    
    /**
     * 验证项目组件使用规范
     */
    fun validateProjectComponents(): String {
        val totalComponents = components.size
        val stableComponents = components.count { it.status == ComponentStatus.STABLE }
        val experimentalComponents = components.count { it.status == ComponentStatus.EXPERIMENTAL }
        
        return """
            ✅ 项目组件验证报告
            
            📊 组件统计：
            • 总组件数：$totalComponents
            • 稳定组件：$stableComponents
            • 实验组件：$experimentalComponents
            
            🎯 组件覆盖率：${(stableComponents.toFloat() / totalComponents * 100).toInt()}%
            
            💡 使用建议：
            1. 优先使用稳定组件（STABLE）
            2. 谨慎使用实验组件（EXPERIMENTAL）
            3. 遵循组件使用保障规范
            
            🔍 检查组件：ComponentRegistry.checkComponentExists("组件名")
        """.trimIndent()
    }
} 