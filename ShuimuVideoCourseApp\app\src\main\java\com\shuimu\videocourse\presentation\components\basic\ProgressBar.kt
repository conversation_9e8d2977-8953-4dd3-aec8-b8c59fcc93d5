package com.shuimu.videocourse.presentation.components.basic

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.theme.*
import kotlin.math.cos
import kotlin.math.sin

/**
 * 水幕进度条组件
 * 
 * 基于UI原型设计：UI Prototype/01-首页.html 第470-550行和第840-920行进度条样式
 * 原型样式规范：
 * - 背景：transparent, border-radius: 2px, overflow: hidden
 * - 尺寸变体：progress-thin(1px), progress-medium(2px), progress-thick(3px)
 * - 填充：linear-gradient(90deg, #84dd99, #22c55e)
 * - 动画：width 0.8s cubic-bezier(0.4, 0, 0.2, 1)
 * - 闪光效果：shimmer 2s infinite
 * - 进度动画：progressFill 1.2s ease-out
 * 
 * 符合度优化：
 * - 完全匹配原型的渐变色彩系统
 * - 实现原型的三种尺寸变体
 * - 添加原型的闪光动画效果
 * - 优化动画时长和缓动函数
 * - 完善布局和间距规范
 * 
 * 功能特性：
 * - 线性进度条：height: 1px-3px, 绿色渐变填充
 * - 圆形进度条：stroke-width: 4px, 直径: 40px
 * - 环形进度条：支持自定义尺寸和颜色
 * - 动画效果：transition: all 0.3s ease
 * - 文本显示：font-size: 14px, font-weight: 500, color: #374151
 * - 闪光效果：白色半透明渐变滑动
 */

/**
 * 进度条类型枚举
 */
enum class ProgressBarType {
    LINEAR,     // 线性进度条
    CIRCULAR,   // 圆形进度条
    RING        // 环形进度条
}

/**
 * 进度条尺寸枚举 - 完全匹配原型
 */
enum class ProgressBarSize {
    SMALL,      // 小型进度条 - progress-thin: 1px
    MEDIUM,     // 中型进度条 - progress-medium: 2px  
    LARGE       // 大型进度条 - progress-thick: 3px
}

/**
 * 进度条样式枚举
 */
enum class ProgressBarStyle {
    DEFAULT,    // 默认样式 - 原型绿色渐变 #84dd99 -> #22c55e
    SUCCESS,    // 成功样式 - 绿色
    WARNING,    // 警告样式 - 黄色
    ERROR,      // 错误样式 - 红色
    CUSTOM      // 自定义样式
}

/**
 * 获取进度条颜色配置 - 基于原型色彩系统
 */
private fun getProgressColors(style: ProgressBarStyle): Pair<Color, Color> {
    return when (style) {
        ProgressBarStyle.DEFAULT -> Pair(
            Color(0xFF84DD99), // 原型：#84dd99
            Color(0xFF22C55E)  // 原型：#22c55e
        )
        ProgressBarStyle.SUCCESS -> Pair(
            Color(0xFF10B981), // emerald-500
            Color(0xFF059669)  // emerald-600
        )
        ProgressBarStyle.WARNING -> Pair(
            Color(0xFFF59E0B), // amber-500
            Color(0xFFD97706)  // amber-600
        )
        ProgressBarStyle.ERROR -> Pair(
            Color(0xFFEF4444), // red-500
            Color(0xFFDC2626)  // red-600
        )
        ProgressBarStyle.CUSTOM -> Pair(
            Color(0xFF84DD99),
            Color(0xFF22C55E)
        )
    }
}

/**
 * 线性进度条组件 - 完全匹配原型设计
 * 
 * @param progress 进度值 (0.0 - 1.0)
 * @param modifier 修饰符
 * @param size 进度条尺寸
 * @param style 进度条样式
 * @param showText 是否显示进度文本
 * @param animated 是否启用动画
 * @param showShimmer 是否显示闪光效果
 * @param backgroundColor 背景颜色
 * @param progressColor 进度颜色
 */
@Composable
fun LinearProgressBar(
    progress: Float,
    modifier: Modifier = Modifier,
    size: ProgressBarSize = ProgressBarSize.MEDIUM,
    style: ProgressBarStyle = ProgressBarStyle.DEFAULT,
    showText: Boolean = false,
    animated: Boolean = true,
    showShimmer: Boolean = true, // 原型闪光效果
    backgroundColor: Color = Color.Transparent, // 原型：background: transparent
    progressColor: Color? = null
) {
    val (startColor, endColor) = getProgressColors(style)
    val actualProgressColor = progressColor ?: startColor
    
    // 动画进度 - 原型：width 0.8s cubic-bezier(0.4, 0, 0.2, 1)
    val animatedProgress by animateFloatAsState(
        targetValue = progress.coerceIn(0f, 1f),
        animationSpec = if (animated) {
            tween(
                durationMillis = 800, // 原型：0.8s
                easing = CubicBezierEasing(0.4f, 0f, 0.2f, 1f) // 原型：cubic-bezier(0.4, 0, 0.2, 1)
            )
        } else {
            tween(0)
        },
        label = "progress"
    )
    
    // 闪光动画 - 原型：shimmer 2s infinite
    val shimmerOffset by animateFloatAsState(
        targetValue = if (showShimmer && progress > 0) 1f else 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing), // 原型：2s
            repeatMode = RepeatMode.Restart
        ),
        label = "shimmer"
    )
    
    // 尺寸配置 - 完全匹配原型
    val (height, marginRight, textSize) = when (size) {
        ProgressBarSize.SMALL -> Triple(1.dp, 3.dp, 12.sp)   // 原型：progress-thin: 1px, margin-right: 3px
        ProgressBarSize.MEDIUM -> Triple(2.dp, 0.dp, 14.sp)  // 原型：progress-medium: 2px, margin-right: 0px
        ProgressBarSize.LARGE -> Triple(3.dp, 0.dp, 16.sp)   // 原型：progress-thick: 3px, margin: 4px 0
    }
    
    Column(modifier = modifier) {
        // 进度条 - 原型：progress-component
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(height)
                .padding(end = marginRight) // 原型：margin-right
                .clip(RoundedCornerShape(2.dp)) // 原型：border-radius: 2px
                .background(backgroundColor) // 原型：background: transparent
        ) {
            // 进度填充 - 原型：progress-fill-component
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(animatedProgress)
                    .clip(RoundedCornerShape(2.dp))
                    .background(
                        // 原型：linear-gradient(90deg, #84dd99, #22c55e)
                        androidx.compose.ui.graphics.Brush.horizontalGradient(
                            colors = listOf(startColor, endColor)
                        )
                    )
            ) {
                // 闪光效果 - 原型：shimmer animation
                if (showShimmer && progress > 0) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                androidx.compose.ui.graphics.Brush.horizontalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        Color.White.copy(alpha = 0.4f), // 原型：rgba(255,255,255,0.4)
                                        Color.Transparent
                                    ),
                                    startX = (shimmerOffset - 1f) * 300f,
                                    endX = shimmerOffset * 300f
                                )
                            )
                    )
                }
            }
        }
        
        // 进度文本
        if (showText) {
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = "${(progress * 100).toInt()}%",
                fontSize = textSize,
                fontWeight = FontWeight.Medium, // 原型：font-weight: 500
                color = Color(0xFF374151), // 原型：color: #374151
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 圆形进度条组件 - 基于原型设计
 * 
 * @param progress 进度值 (0.0 - 1.0)
 * @param modifier 修饰符
 * @param size 进度条尺寸
 * @param style 进度条样式
 * @param showText 是否显示进度文本
 * @param animated 是否启用动画
 */
@Composable
fun CircularProgressBar(
    progress: Float,
    modifier: Modifier = Modifier,
    size: ProgressBarSize = ProgressBarSize.MEDIUM,
    style: ProgressBarStyle = ProgressBarStyle.DEFAULT,
    showText: Boolean = true,
    animated: Boolean = true
) {
    val (startColor, endColor) = getProgressColors(style)
    
    // 动画进度
    val animatedProgress by animateFloatAsState(
        targetValue = progress.coerceIn(0f, 1f),
        animationSpec = if (animated) {
            tween(durationMillis = 800, easing = EaseOutCubic)
        } else {
            tween(0)
        },
        label = "circularProgress"
    )
    
    // 尺寸配置 - 原型：直径: 40px, stroke-width: 4px
    val (diameter, strokeWidth, textSize) = when (size) {
        ProgressBarSize.SMALL -> Triple(32.dp, 3.dp, 12.sp)
        ProgressBarSize.MEDIUM -> Triple(40.dp, 4.dp, 14.sp) // 原型规范
        ProgressBarSize.LARGE -> Triple(56.dp, 6.dp, 16.sp)
    }
    
    Box(
        modifier = modifier.size(diameter),
        contentAlignment = Alignment.Center
    ) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            val canvasSize = size.minDimension
            val radius = (canvasSize - strokeWidth.toPx()) / 2
            val center = Offset(canvasSize / 2, canvasSize / 2)
            
            // 背景圆环
            drawCircle(
                color = Color(0xFFE5E7EB), // gray-200
                radius = radius,
                center = center,
                style = Stroke(width = strokeWidth.toPx(), cap = StrokeCap.Round)
            )
            
            // 进度圆弧
            if (animatedProgress > 0) {
                drawArc(
                    brush = androidx.compose.ui.graphics.Brush.sweepGradient(
                        colors = listOf(startColor, endColor)
                    ),
                    startAngle = -90f,
                    sweepAngle = 360f * animatedProgress,
                    useCenter = false,
                    style = Stroke(width = strokeWidth.toPx(), cap = StrokeCap.Round),
                    topLeft = Offset(strokeWidth.toPx() / 2, strokeWidth.toPx() / 2),
                    size = Size(canvasSize - strokeWidth.toPx(), canvasSize - strokeWidth.toPx())
                )
            }
        }
        
        // 进度文本
        if (showText) {
            Text(
                text = "${(progress * 100).toInt()}%",
                fontSize = textSize,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF374151)
            )
        }
    }
}

/**
 * 环形进度条组件
 * 
 * @param progress 进度值 (0.0 - 1.0)
 * @param modifier 修饰符
 * @param size 进度条尺寸
 * @param style 进度条样式
 * @param showText 是否显示进度文本
 * @param animated 是否启用动画
 */
@Composable
fun RingProgressBar(
    progress: Float,
    modifier: Modifier = Modifier,
    size: ProgressBarSize = ProgressBarSize.MEDIUM,
    style: ProgressBarStyle = ProgressBarStyle.DEFAULT,
    showText: Boolean = true,
    animated: Boolean = true
) {
    val (startColor, endColor) = getProgressColors(style)
    
    // 动画进度
    val animatedProgress by animateFloatAsState(
        targetValue = progress.coerceIn(0f, 1f),
        animationSpec = if (animated) {
            tween(durationMillis = 800, easing = EaseOutCubic)
        } else {
            tween(0)
        },
        label = "ringProgress"
    )
    
    // 尺寸配置
    val (diameter, strokeWidth, textSize) = when (size) {
        ProgressBarSize.SMALL -> Triple(48.dp, 4.dp, 12.sp)
        ProgressBarSize.MEDIUM -> Triple(64.dp, 6.dp, 14.sp)
        ProgressBarSize.LARGE -> Triple(80.dp, 8.dp, 16.sp)
    }
    
    Box(
        modifier = modifier.size(diameter),
        contentAlignment = Alignment.Center
    ) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            val canvasSize = size.minDimension
            val radius = (canvasSize - strokeWidth.toPx()) / 2
            val center = Offset(canvasSize / 2, canvasSize / 2)
            
            // 背景圆环
            drawCircle(
                color = Color(0xFFE5E7EB),
                radius = radius,
                center = center,
                style = Stroke(width = strokeWidth.toPx())
            )
            
            // 进度圆弧
            if (animatedProgress > 0) {
                drawArc(
                    brush = androidx.compose.ui.graphics.Brush.sweepGradient(
                        colors = listOf(startColor, endColor)
                    ),
                    startAngle = -90f,
                    sweepAngle = 360f * animatedProgress,
                    useCenter = false,
                    style = Stroke(width = strokeWidth.toPx()),
                    topLeft = Offset(strokeWidth.toPx() / 2, strokeWidth.toPx() / 2),
                    size = Size(canvasSize - strokeWidth.toPx(), canvasSize - strokeWidth.toPx())
                )
            }
        }
        
        // 进度文本
        if (showText) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "${(progress * 100).toInt()}%",
                    fontSize = textSize,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF374151)
                )
                Text(
                    text = "完成",
                    fontSize = (textSize.value - 2).sp,
                    color = Color(0xFF6B7280)
                )
            }
        }
    }
}

/**
 * 通用进度条组件
 * 
 * @param progress 进度值 (0.0 - 1.0)
 * @param modifier 修饰符
 * @param type 进度条类型
 * @param size 进度条尺寸
 * @param style 进度条样式
 * @param showText 是否显示进度文本
 * @param animated 是否启用动画
 */
@Composable
fun ProgressBar(
    progress: Float,
    modifier: Modifier = Modifier,
    type: ProgressBarType = ProgressBarType.LINEAR,
    size: ProgressBarSize = ProgressBarSize.MEDIUM,
    style: ProgressBarStyle = ProgressBarStyle.DEFAULT,
    showText: Boolean = false,
    animated: Boolean = true
) {
    when (type) {
        ProgressBarType.LINEAR -> {
            LinearProgressBar(
                progress = progress,
                modifier = modifier,
                size = size,
                style = style,
                showText = showText,
                animated = animated
            )
        }
        ProgressBarType.CIRCULAR -> {
            CircularProgressBar(
                progress = progress,
                modifier = modifier,
                size = size,
                style = style,
                showText = showText,
                animated = animated
            )
        }
        ProgressBarType.RING -> {
            RingProgressBar(
                progress = progress,
                modifier = modifier,
                size = size,
                style = style,
                showText = showText,
                animated = animated
            )
        }
    }
}

/**
 * 预定义的进度条样式组合
 */
object ProgressBarDefaults {
    
    /**
     * 课程学习进度条
     */
    @Composable
    fun CourseProgressBar(
        progress: Float,
        modifier: Modifier = Modifier,
        showText: Boolean = true
    ) {
        LinearProgressBar(
            progress = progress,
            modifier = modifier,
            size = ProgressBarSize.MEDIUM,
            style = ProgressBarStyle.DEFAULT,
            showText = showText,
            animated = true
        )
    }
    
    /**
     * 下载进度条
     */
    @Composable
    fun DownloadProgressBar(
        progress: Float,
        modifier: Modifier = Modifier,
        showText: Boolean = true
    ) {
        LinearProgressBar(
            progress = progress,
            modifier = modifier,
            size = ProgressBarSize.SMALL,
            style = ProgressBarStyle.SUCCESS,
            showText = showText,
            animated = true
        )
    }
    
    /**
     * 用户等级进度环
     */
    @Composable
    fun UserLevelProgress(
        progress: Float,
        modifier: Modifier = Modifier,
        showText: Boolean = true
    ) {
        RingProgressBar(
            progress = progress,
            modifier = modifier,
            size = ProgressBarSize.LARGE,
            style = ProgressBarStyle.DEFAULT,
            showText = showText,
            animated = true
        )
    }
    
    /**
     * 加载进度圆环
     */
    @Composable
    fun LoadingProgress(
        modifier: Modifier = Modifier,
        size: ProgressBarSize = ProgressBarSize.MEDIUM
    ) {
        // 无限循环动画
        val infiniteTransition = rememberInfiniteTransition(label = "loading")
        val progress by infiniteTransition.animateFloat(
            initialValue = 0f,
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(2000, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            ),
            label = "progress"
        )
        
        CircularProgressBar(
            progress = progress,
            modifier = modifier,
            size = size,
            style = ProgressBarStyle.DEFAULT,
            showText = false,
            animated = false
        )
    }
}

/**
 * 辅助数据类 - 四元组
 */
private data class ProgressQuadruple<A, B, C, D>(
    val first: A,
    val second: B,
    val third: C,
    val fourth: D
) 