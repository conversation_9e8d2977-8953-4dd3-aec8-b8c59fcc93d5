package com.shuimu.videocourse.domain.usecase.course;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SearchCoursesUseCase_Factory implements Factory<SearchCoursesUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public SearchCoursesUseCase_Factory(Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public SearchCoursesUseCase get() {
    return newInstance(courseRepositoryProvider.get());
  }

  public static SearchCoursesUseCase_Factory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new SearchCoursesUseCase_Factory(courseRepositoryProvider);
  }

  public static SearchCoursesUseCase newInstance(CourseRepository courseRepository) {
    return new SearchCoursesUseCase(courseRepository);
  }
}
