package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.usecase.course.GetCoursesUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideGetCoursesUseCaseFactory implements Factory<GetCoursesUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public DomainModule_ProvideGetCoursesUseCaseFactory(
      Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public GetCoursesUseCase get() {
    return provideGetCoursesUseCase(courseRepositoryProvider.get());
  }

  public static DomainModule_ProvideGetCoursesUseCaseFactory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new DomainModule_ProvideGetCoursesUseCaseFactory(courseRepositoryProvider);
  }

  public static GetCoursesUseCase provideGetCoursesUseCase(CourseRepository courseRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideGetCoursesUseCase(courseRepository));
  }
}
