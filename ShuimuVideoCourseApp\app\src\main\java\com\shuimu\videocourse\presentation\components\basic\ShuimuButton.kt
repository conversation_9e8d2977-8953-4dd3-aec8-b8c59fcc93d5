package com.shuimu.videocourse.presentation.components.basic

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.theme.*

/**
 * 水幕按钮组件
 * 
 * 基于UI原型设计：UI Prototype/01-首页.html 第2062-2081行支付弹窗按钮区域
 * 原型样式：
 * - 主要按钮：bg-green-500 text-white py-3 rounded-lg font-semibold text-lg
 * - 次要按钮：bg-gray-100 text-gray-600 py-3 rounded-lg font-medium
 * - 圆角：rounded-lg (12dp)
 * - 内边距：py-3 (12dp垂直)
 * - 字体：font-semibold/font-medium
 */

/**
 * 按钮类型枚举
 */
enum class ShuimuButtonType {
    PRIMARY,    // 主要按钮 - 绿色背景
    SECONDARY,  // 次要按钮 - 灰色背景
    TEXT,       // 文本按钮 - 透明背景
    GRADIENT    // 渐变按钮 - 紫蓝色渐变
}

/**
 * 按钮尺寸枚举
 */
enum class ShuimuButtonSize {
    SMALL,      // 小型按钮 - 32dp高度
    MEDIUM,     // 中型按钮 - 40dp高度
    LARGE       // 大型按钮 - 48dp高度（默认）
}

/**
 * 水幕按钮组件
 * 
 * @param text 按钮文字
 * @param onClick 点击事件
 * @param modifier 修饰符
 * @param type 按钮类型
 * @param size 按钮尺寸
 * @param enabled 是否启用
 * @param loading 是否显示加载状态
 * @param icon 按钮图标
 * @param fullWidth 是否占满宽度
 */
@Composable
fun ShuimuButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    type: ShuimuButtonType = ShuimuButtonType.PRIMARY,
    size: ShuimuButtonSize = ShuimuButtonSize.LARGE,
    enabled: Boolean = true,
    loading: Boolean = false,
    icon: ImageVector? = null,
    fullWidth: Boolean = false
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    // 根据原型定义按钮样式
    val (backgroundColor, contentColor, borderColor) = when (type) {
        ShuimuButtonType.PRIMARY -> {
            // 原型：bg-green-500 (#4caf50) text-white
            val bgColor = if (enabled) {
                if (isPressed) Color(0xFF388E3C) else Secondary // #10b981
            } else {
                Color(0xFFE0E0E0)
            }
            Triple(bgColor, Color.White, Color.Transparent)
        }
        ShuimuButtonType.SECONDARY -> {
            // 原型：bg-gray-100 text-gray-600
            val bgColor = if (enabled) {
                if (isPressed) Color(0xFFE5E7EB) else Color(0xFFF3F4F6)
            } else {
                Color(0xFFF5F5F5)
            }
            val textColor = if (enabled) Color(0xFF6B7280) else Color(0xFF9CA3AF)
            Triple(bgColor, textColor, Color.Transparent)
        }
        ShuimuButtonType.TEXT -> {
            val textColor = if (enabled) {
                if (isPressed) Primary.copy(alpha = 0.8f) else Primary
            } else {
                Color(0xFF9CA3AF)
            }
            Triple(Color.Transparent, textColor, Color.Transparent)
        }
        ShuimuButtonType.GRADIENT -> {
            // 使用紫蓝色渐变主题
            val textColor = Color.White
            Triple(Color.Transparent, textColor, Color.Transparent)
        }
    }
    
    // 动画颜色过渡
    val animatedBackgroundColor by animateColorAsState(
        targetValue = backgroundColor,
        animationSpec = tween(300),
        label = "backgroundColor"
    )
    
    val animatedContentColor by animateColorAsState(
        targetValue = contentColor,
        animationSpec = tween(300),
        label = "contentColor"
    )
    
    // 按钮尺寸
    val (height, horizontalPadding, fontSize, fontWeight) = when (size) {
        ShuimuButtonSize.SMALL -> Tuple4(32.dp, 16.dp, 14.sp, FontWeight.Medium)
        ShuimuButtonSize.MEDIUM -> Tuple4(40.dp, 20.dp, 16.sp, FontWeight.Medium)
        ShuimuButtonSize.LARGE -> Tuple4(48.dp, 24.dp, 18.sp, FontWeight.SemiBold) // 原型：text-lg font-semibold
    }
    
    // 按钮修饰符
    val buttonModifier = modifier
        .let { if (fullWidth) it.fillMaxWidth() else it }
        .height(height)
        .clip(RoundedCornerShape(12.dp)) // 原型：rounded-lg
        .then(
            if (type == ShuimuButtonType.GRADIENT) {
                Modifier.background(
                    brush = Brush.linearGradient(
                        colors = listOf(Primary, PrimaryVariant)
                    )
                )
            } else {
                Modifier.background(animatedBackgroundColor)
            }
        )
        .clickable(
            interactionSource = interactionSource,
            indication = null,
            enabled = enabled && !loading,
            onClick = onClick
        )
        .padding(horizontal = horizontalPadding)
    
    Box(
        modifier = buttonModifier,
        contentAlignment = Alignment.Center
    ) {
        if (loading) {
            // 加载状态
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    color = animatedContentColor,
                    strokeWidth = 2.dp
                )
                if (text.isNotEmpty()) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = text,
                        color = animatedContentColor,
                        fontSize = fontSize,
                        fontWeight = fontWeight,
                        textAlign = TextAlign.Center
                    )
                }
            }
        } else {
            // 正常状态
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                icon?.let { iconVector ->
                    Icon(
                        imageVector = iconVector,
                        contentDescription = null,
                        tint = animatedContentColor,
                        modifier = Modifier.size(20.dp)
                    )
                    if (text.isNotEmpty()) {
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                }
                
                if (text.isNotEmpty()) {
                    Text(
                        text = text,
                        color = animatedContentColor,
                        fontSize = fontSize,
                        fontWeight = fontWeight,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

/**
 * 辅助数据类，用于返回四个值
 */
private data class Tuple4<A, B, C, D>(
    val first: A,
    val second: B,
    val third: C,
    val fourth: D
)

/**
 * 预定义的按钮样式组合
 */
object ShuimuButtonDefaults {
    
    /**
     * 支付按钮 - 基于原型支付弹窗按钮
     * 原型：w-4/5 bg-green-500 text-white py-3 rounded-lg font-semibold text-lg
     */
    @Composable
    fun PaymentButton(
        text: String = "立即支付",
        onClick: () -> Unit,
        modifier: Modifier = Modifier,
        enabled: Boolean = true,
        loading: Boolean = false
    ) {
        ShuimuButton(
            text = text,
            onClick = onClick,
            modifier = modifier.fillMaxWidth(0.8f), // w-4/5
            type = ShuimuButtonType.PRIMARY,
            size = ShuimuButtonSize.LARGE,
            enabled = enabled,
            loading = loading
        )
    }
    
    /**
     * 取消按钮 - 基于原型分享弹窗取消按钮
     * 原型：w-full bg-gray-100 text-gray-600 py-3 rounded-lg font-medium
     */
    @Composable
    fun CancelButton(
        text: String = "取消",
        onClick: () -> Unit,
        modifier: Modifier = Modifier,
        enabled: Boolean = true
    ) {
        ShuimuButton(
            text = text,
            onClick = onClick,
            modifier = modifier,
            type = ShuimuButtonType.SECONDARY,
            size = ShuimuButtonSize.LARGE,
            enabled = enabled,
            fullWidth = true
        )
    }
    
    /**
     * 渐变按钮 - 使用紫蓝色渐变主题
     */
    @Composable
    fun GradientButton(
        text: String,
        onClick: () -> Unit,
        modifier: Modifier = Modifier,
        enabled: Boolean = true,
        loading: Boolean = false,
        icon: ImageVector? = null
    ) {
        ShuimuButton(
            text = text,
            onClick = onClick,
            modifier = modifier,
            type = ShuimuButtonType.GRADIENT,
            size = ShuimuButtonSize.LARGE,
            enabled = enabled,
            loading = loading,
            icon = icon
        )
    }
} 