package com.shuimu.videocourse.presentation.components.modal

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.shuimu.videocourse.presentation.components.display.SearchItem
import com.shuimu.videocourse.presentation.components.state.LoadingIndicator
import com.shuimu.videocourse.presentation.components.state.LoadingType

/**
 * 搜索弹窗组件
 * 
 * 基于原型设计：UI Prototype/01-首页.html 第2020-2080行搜索弹窗结构
 * 原型样式规范：
 * - 位置：position: fixed, bottom: 0, width: 359px, height: 60%
 * - 背景：background: white, border-radius: 16px 16px 0 0
 * - 阴影：box-shadow: 0 -4px 20px rgba(0,0,0,0.15)
 * - 内边距：padding: 20px
 * - 层级：z-index: 9999
 * 
 * 功能特性：
 * - 搜索输入框（带清除按钮）
 * - 搜索历史标签（可点击搜索）
 * - 实时搜索结果展示
 * - 加载状态指示
 * - 空状态提示
 * - 键盘交互支持
 * 
 * @param isVisible 是否显示弹窗
 * @param searchQuery 当前搜索关键词
 * @param searchResults 搜索结果列表
 * @param searchHistory 搜索历史列表
 * @param isSearching 是否正在搜索
 * @param onSearchQueryChange 搜索关键词变化回调
 * @param onSearchHistoryClick 搜索历史点击回调
 * @param onClearHistory 清空搜索历史回调
 * @param onSearchResultClick 搜索结果点击回调
 * @param onDismiss 关闭弹窗回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchModal(
    isVisible: Boolean,
    searchQuery: String = "",
    searchResults: List<SearchResultItem> = emptyList(),
    searchHistory: List<String> = emptyList(),
    isSearching: Boolean = false,
    onSearchQueryChange: (String) -> Unit = {},
    onSearchHistoryClick: (String) -> Unit = {},
    onClearHistory: () -> Unit = {},
    onSearchResultClick: (SearchResultItem) -> Unit = {},
    onDismiss: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 动画状态
    val animationSpec = tween<Float>(300, easing = EaseOutCubic)
    
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn(animationSpec) + slideInVertically(
            animationSpec = animationSpec,
            initialOffsetY = { it }
        ),
        exit = fadeOut(animationSpec) + slideOutVertically(
            animationSpec = animationSpec,
            targetOffsetY = { it }
        )
    ) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                usePlatformDefaultWidth = false,
                decorFitsSystemWindows = false
            )
        ) {
            // 背景遮罩
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null
                    ) { onDismiss() },
                contentAlignment = Alignment.BottomCenter
            ) {
                // 搜索弹窗内容 - 原型：height: 60%
                SearchModalContent(
                    searchQuery = searchQuery,
                    searchResults = searchResults,
                    searchHistory = searchHistory,
                    isSearching = isSearching,
                    onSearchQueryChange = onSearchQueryChange,
                    onSearchHistoryClick = onSearchHistoryClick,
                    onClearHistory = onClearHistory,
                    onSearchResultClick = onSearchResultClick,
                    onDismiss = onDismiss,
                    modifier = modifier
                        .fillMaxWidth()
                        .fillMaxHeight(0.6f) // 原型：height: 60%
                        .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)) // 原型：border-radius: 16px 16px 0 0
                        .background(Color.White) // 原型：background: white
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null
                        ) { /* 阻止点击穿透 */ }
                )
            }
        }
    }
}

/**
 * 搜索弹窗内容组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SearchModalContent(
    searchQuery: String,
    searchResults: List<SearchResultItem>,
    searchHistory: List<String>,
    isSearching: Boolean,
    onSearchQueryChange: (String) -> Unit,
    onSearchHistoryClick: (String) -> Unit,
    onClearHistory: () -> Unit,
    onSearchResultClick: (SearchResultItem) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    
    // 自动聚焦搜索框
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
    
    Column(
        modifier = modifier.padding(20.dp) // 原型：padding: 20px
    ) {
        // 弹窗头部
        SearchModalHeader(
            onDismiss = onDismiss,
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 搜索输入框
        SearchInputField(
            query = searchQuery,
            onQueryChange = onSearchQueryChange,
            onSearch = { 
                keyboardController?.hide()
            },
            focusRequester = focusRequester,
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(20.dp))
        
        // 搜索内容区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            when {
                // 正在搜索
                isSearching -> {
                    LoadingIndicator(
                        type = LoadingType.Circular,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                // 有搜索结果
                searchQuery.isNotEmpty() && searchResults.isNotEmpty() -> {
                    SearchResultsList(
                        results = searchResults,
                        searchQuery = searchQuery,
                        onResultClick = onSearchResultClick,
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                // 搜索无结果
                searchQuery.isNotEmpty() && searchResults.isEmpty() && !isSearching -> {
                    SearchEmptyState(
                        query = searchQuery,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                // 显示搜索历史
                else -> {
                    SearchHistorySection(
                        history = searchHistory,
                        onHistoryClick = onSearchHistoryClick,
                        onClearHistory = onClearHistory,
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}

/**
 * 搜索弹窗头部
 */
@Composable
private fun SearchModalHeader(
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "搜索课程",
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF1F2937)
        )
        
        IconButton(
            onClick = onDismiss,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "关闭",
                tint = Color(0xFF9CA3AF),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

/**
 * 搜索输入框组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SearchInputField(
    query: String,
    onQueryChange: (String) -> Unit,
    onSearch: () -> Unit,
    focusRequester: FocusRequester,
    modifier: Modifier = Modifier
) {
    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        placeholder = {
            Text(
                text = "搜索视频标题...",
                color = Color(0xFF9CA3AF),
                fontSize = 16.sp
            )
        },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "搜索",
                tint = Color(0xFF6B7280),
                modifier = Modifier.size(20.dp)
            )
        },
        trailingIcon = {
            if (query.isNotEmpty()) {
                IconButton(
                    onClick = { onQueryChange("") }
                ) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "清除",
                        tint = Color(0xFF9CA3AF),
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
        },
        keyboardOptions = KeyboardOptions(
            imeAction = ImeAction.Search
        ),
        keyboardActions = KeyboardActions(
            onSearch = { onSearch() }
        ),
        singleLine = true,
        colors = OutlinedTextFieldDefaults.colors(
            focusedBorderColor = Color(0xFF3B82F6),
            unfocusedBorderColor = Color(0xFFE5E7EB),
            focusedTextColor = Color(0xFF1F2937),
            unfocusedTextColor = Color(0xFF1F2937)
        ),
        shape = RoundedCornerShape(12.dp),
        modifier = modifier.focusRequester(focusRequester)
    )
}

/**
 * 搜索历史区域
 */
@Composable
private fun SearchHistorySection(
    history: List<String>,
    onHistoryClick: (String) -> Unit,
    onClearHistory: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
    ) {
        // 搜索历史标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "搜索历史",
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF374151)
            )
            
            if (history.isNotEmpty()) {
                Text(
                    text = "清空",
                    fontSize = 12.sp,
                    color = Color(0xFF3B82F6),
                    modifier = Modifier.clickable { onClearHistory() }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 搜索历史标签
        if (history.isNotEmpty()) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                items(history) { historyItem ->
                    SearchHistoryTag(
                        text = historyItem,
                        onClick = { onHistoryClick(historyItem) }
                    )
                }
            }
        } else {
            Text(
                text = "暂无搜索历史",
                fontSize = 14.sp,
                color = Color(0xFF9CA3AF),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 40.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(20.dp))
        
        // 搜索提示
        Text(
            text = "输入关键词开始搜索",
            fontSize = 14.sp,
            color = Color(0xFF9CA3AF),
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 搜索历史标签
 */
@Composable
private fun SearchHistoryTag(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        fontSize = 12.sp,
        color = Color(0xFF6B7280),
        modifier = modifier
            .background(
                color = Color(0xFFF3F4F6),
                shape = RoundedCornerShape(16.dp)
            )
            .clickable { onClick() }
            .padding(horizontal = 12.dp, vertical = 6.dp)
    )
}

/**
 * 搜索结果列表
 */
@Composable
private fun SearchResultsList(
    results: List<SearchResultItem>,
    searchQuery: String,
    onResultClick: (SearchResultItem) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        items(results) { result ->
            SearchItem(
                title = result.title,
                category = result.category,
                progress = result.progress,
                watchCount = result.watchCount,
                cacheStatus = result.cacheStatus,
                isPurchased = result.isPurchased,
                searchQuery = searchQuery,
                onClick = { onResultClick(result) }
            )
        }
    }
}

/**
 * 搜索空状态
 */
@Composable
private fun SearchEmptyState(
    query: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "未找到相关内容",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF6B7280)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "试试其他关键词",
            fontSize = 14.sp,
            color = Color(0xFF9CA3AF)
        )
    }
}

/**
 * 搜索结果数据类
 */
data class SearchResultItem(
    val id: String,
    val title: String,
    val category: String,
    val progress: Float = 0f,
    val watchCount: Int = 0,
    val cacheStatus: String = "",
    val isPurchased: Boolean = true
)