package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.UserRepository;
import com.shuimu.videocourse.domain.usecase.user.RegisterUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideRegisterUseCaseFactory implements Factory<RegisterUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public DomainModule_ProvideRegisterUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public RegisterUseCase get() {
    return provideRegisterUseCase(userRepositoryProvider.get());
  }

  public static DomainModule_ProvideRegisterUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new DomainModule_ProvideRegisterUseCaseFactory(userRepositoryProvider);
  }

  public static RegisterUseCase provideRegisterUseCase(UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideRegisterUseCase(userRepository));
  }
}
