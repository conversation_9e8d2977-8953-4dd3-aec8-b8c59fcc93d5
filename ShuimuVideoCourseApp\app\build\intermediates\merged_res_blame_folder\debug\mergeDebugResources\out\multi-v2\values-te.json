{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "50,51,52,53,54,55,56,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3987,4089,4197,4299,4400,4506,4613,14034", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "4084,4192,4294,4395,4501,4608,4732,14130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,485,574,663,759,847,931,1004,1077,1161,1251,1328,1405,1474", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "196,283,380,480,569,658,754,842,926,999,1072,1156,1246,1323,1400,1469,1586"}, "to": {"startLines": "57,58,91,92,94,147,148,149,150,151,152,153,154,157,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4737,4833,8638,8735,8907,13019,13108,13204,13292,13376,13449,13522,13606,13869,14238,14315,14384", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "4828,4915,8730,8830,8991,13103,13199,13287,13371,13444,13517,13601,13691,13941,14310,14379,14496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,695,787,879,968,1068,1172,1249,1314,1406,1498,1569,1639,1700,1770,1907,2039,2174,2249,2333,2408,2479,2573,2667,2731,2810,2863,2921,2969,3030,3097,3159,3224,3291,3350,3412,3478,3542,3609,3663,3723,3797,3871", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,91,88,99,103,76,64,91,91,70,69,60,69,136,131,134,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,782,874,963,1063,1167,1244,1309,1401,1493,1564,1634,1695,1765,1902,2034,2169,2244,2328,2403,2474,2568,2662,2726,2805,2858,2916,2964,3025,3092,3154,3219,3286,3345,3407,3473,3537,3604,3658,3718,3792,3866,3920"}, "to": {"startLines": "2,11,15,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,552,8996,9088,9180,9269,9369,9473,9550,9615,9707,9799,9870,9940,10001,10071,10208,10340,10475,10550,10634,10709,10780,10874,10968,11032,11821,11874,11932,11980,12041,12108,12170,12235,12302,12361,12423,12489,12553,12620,12674,12734,12808,12882", "endLines": "10,14,18,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145", "endColumns": "17,12,12,91,91,88,99,103,76,64,91,91,70,69,60,69,136,131,134,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "331,547,740,9083,9175,9264,9364,9468,9545,9610,9702,9794,9865,9935,9996,10066,10203,10335,10470,10545,10629,10704,10775,10869,10963,11027,11106,11869,11927,11975,12036,12103,12165,12230,12297,12356,12418,12484,12548,12615,12669,12729,12803,12877,12931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,307,417,543,624,719,832,968,1076,1215,1295,1394,1484,1578,1690,1816,1920,2065,2207,2344,2536,2668,2780,2898,3035,3128,3223,3344,3468,3570,3672,3774,3912,4058,4162,4261,4333,4416,4506,4594,4697,4773,4852,4949,5050,5143,5241,5325,5432,5530,5627,5746,5822,5926", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "175,302,412,538,619,714,827,963,1071,1210,1290,1389,1479,1573,1685,1811,1915,2060,2202,2339,2531,2663,2775,2893,3030,3123,3218,3339,3463,3565,3667,3769,3907,4053,4157,4256,4328,4411,4501,4589,4692,4768,4847,4944,5045,5138,5236,5320,5427,5525,5622,5741,5817,5921,6014"}, "to": {"startLines": "46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,146,155,158,160,164,165,166,167,168,169,170,171,172,173,174,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3499,3624,3751,3861,4920,5001,5096,5209,5345,5453,5592,5672,5771,5861,5955,6067,6193,6297,6442,6584,6721,6913,7045,7157,7275,7412,7505,7600,7721,7845,7947,8049,8151,8289,8435,8539,8835,12936,13696,13946,14135,14501,14577,14656,14753,14854,14947,15045,15129,15236,15334,15431,15550,15626,15730", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "3619,3746,3856,3982,4996,5091,5204,5340,5448,5587,5667,5766,5856,5950,6062,6188,6292,6437,6579,6716,6908,7040,7152,7270,7407,7500,7595,7716,7840,7942,8044,8146,8284,8430,8534,8633,8902,13014,13781,14029,14233,14572,14651,14748,14849,14942,15040,15124,15231,15329,15426,15545,15621,15725,15818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11111,11183,11251,11324,11392,11472,11549,11650,11743", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "11178,11246,11319,11387,11467,11544,11645,11738,11816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "745,862,974,1087,1177,1282,1401,1479,1555,1646,1739,1834,1928,2028,2121,2216,2311,2402,2493,2582,2696,2800,2899,3014,3119,3234,3396,13786", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "857,969,1082,1172,1277,1396,1474,1550,1641,1734,1829,1923,2023,2116,2211,2306,2397,2488,2577,2691,2795,2894,3009,3114,3229,3391,3494,13864"}}]}]}