package com.shuimu.videocourse.presentation.components.state

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 加载指示器组件
 * 
 * 基于原型的加载指示器设计
 * 
 * 功能特性：
 * - 加载动画效果和多种加载样式
 * - 文本提示支持和主题色适配
 * - 圆形、线性等样式变体
 * - #667eea紫蓝色主题适配
 * - 响应式尺寸设计
 * 
 * @param type 加载指示器类型
 * @param size 指示器尺寸
 * @param text 加载文本（可选）
 * @param modifier 修饰符
 */
@Composable
fun LoadingIndicator(
    type: LoadingType = LoadingType.Circular,
    size: LoadingSize = LoadingSize.Medium,
    text: String? = null,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        when (type) {
            LoadingType.Circular -> {
                CircularLoadingIndicator(size = size)
            }
            LoadingType.Linear -> {
                LinearLoadingIndicator(size = size)
            }
            LoadingType.Dots -> {
                DotsLoadingIndicator(size = size)
            }
            LoadingType.Pulse -> {
                PulseLoadingIndicator(size = size)
            }
        }
        
        if (text != null) {
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = text,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = when (size) {
                        LoadingSize.Small -> 12.sp
                        LoadingSize.Medium -> 14.sp
                        LoadingSize.Large -> 16.sp
                    },
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 圆形加载指示器
 */
@Composable
private fun CircularLoadingIndicator(size: LoadingSize) {
    val infiniteTransition = rememberInfiniteTransition(label = "circular_loading")
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    val sizeValue = when (size) {
        LoadingSize.Small -> 24.dp
        LoadingSize.Medium -> 32.dp
        LoadingSize.Large -> 48.dp
    }
    
    CircularProgressIndicator(
        modifier = Modifier
            .size(sizeValue)
            .rotate(rotation),
        color = Color(0xFF667eea),
        strokeWidth = when (size) {
            LoadingSize.Small -> 2.dp
            LoadingSize.Medium -> 3.dp
            LoadingSize.Large -> 4.dp
        }
    )
}

/**
 * 线性加载指示器
 */
@Composable
private fun LinearLoadingIndicator(size: LoadingSize) {
    val infiniteTransition = rememberInfiniteTransition(label = "linear_loading")
    val progress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "progress"
    )
    
    val height = when (size) {
        LoadingSize.Small -> 2.dp
        LoadingSize.Medium -> 4.dp
        LoadingSize.Large -> 6.dp
    }
    
    Box(
        modifier = Modifier
            .width(120.dp)
            .height(height)
            .background(
                color = Color(0xFF667eea).copy(alpha = 0.2f),
                shape = RoundedCornerShape(height / 2)
            )
    ) {
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .fillMaxWidth(progress)
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFF667eea),
                            Color(0xFF764ba2)
                        )
                    ),
                    shape = RoundedCornerShape(height / 2)
                )
        )
    }
}

/**
 * 点状加载指示器
 */
@Composable
private fun DotsLoadingIndicator(size: LoadingSize) {
    val dotSize = when (size) {
        LoadingSize.Small -> 6.dp
        LoadingSize.Medium -> 8.dp
        LoadingSize.Large -> 12.dp
    }
    
    val spacing = when (size) {
        LoadingSize.Small -> 4.dp
        LoadingSize.Medium -> 6.dp
        LoadingSize.Large -> 8.dp
    }
    
    Row(
        horizontalArrangement = Arrangement.spacedBy(spacing),
        verticalAlignment = Alignment.CenterVertically
    ) {
        repeat(3) { index ->
            val infiniteTransition = rememberInfiniteTransition(label = "dot_$index")
            val scale by infiniteTransition.animateFloat(
                initialValue = 0.5f,
                targetValue = 1f,
                animationSpec = infiniteRepeatable(
                    animation = tween(600, delayMillis = index * 200),
                    repeatMode = RepeatMode.Reverse
                ),
                label = "scale_$index"
            )
            
            Box(
                modifier = Modifier
                    .size(dotSize * scale)
                    .background(
                        color = Color(0xFF667eea),
                        shape = CircleShape
                    )
            )
        }
    }
}

/**
 * 脉冲加载指示器
 */
@Composable
private fun PulseLoadingIndicator(size: LoadingSize) {
    val infiniteTransition = rememberInfiniteTransition(label = "pulse_loading")
    val scale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scale"
    )
    
    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "alpha"
    )
    
    val sizeValue = when (size) {
        LoadingSize.Small -> 24.dp
        LoadingSize.Medium -> 32.dp
        LoadingSize.Large -> 48.dp
    }
    
    Box(
        modifier = Modifier
            .size(sizeValue * scale)
            .background(
                color = Color(0xFF667eea).copy(alpha = alpha),
                shape = CircleShape
            )
    )
}

/**
 * 加载类型枚举
 */
enum class LoadingType {
    Circular,   // 圆形
    Linear,     // 线性
    Dots,       // 点状
    Pulse       // 脉冲
}

/**
 * 加载尺寸枚举
 */
enum class LoadingSize {
    Small,      // 小
    Medium,     // 中
    Large       // 大
}

/**
 * 全屏加载遮罩
 * 
 * @param isVisible 是否可见
 * @param text 加载文本
 * @param type 加载类型
 * @param modifier 修饰符
 */
@Composable
fun LoadingOverlay(
    isVisible: Boolean,
    text: String = "加载中...",
    type: LoadingType = LoadingType.Circular,
    modifier: Modifier = Modifier
) {
    if (isVisible) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.5f)),
            contentAlignment = Alignment.Center
        ) {
            Card(
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Box(
                    modifier = Modifier.padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    LoadingIndicator(
                        type = type,
                        size = LoadingSize.Large,
                        text = text
                    )
                }
            }
        }
    }
}

// 预览组件
@Composable
fun LoadingIndicatorPreview() {
    ShuimuTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 不同类型的加载指示器
            LoadingIndicator(
                type = LoadingType.Circular,
                size = LoadingSize.Medium,
                text = "圆形加载中..."
            )
            
            LoadingIndicator(
                type = LoadingType.Linear,
                size = LoadingSize.Medium,
                text = "线性加载中..."
            )
            
            LoadingIndicator(
                type = LoadingType.Dots,
                size = LoadingSize.Medium,
                text = "点状加载中..."
            )
            
            LoadingIndicator(
                type = LoadingType.Pulse,
                size = LoadingSize.Medium,
                text = "脉冲加载中..."
            )
        }
    }
} 