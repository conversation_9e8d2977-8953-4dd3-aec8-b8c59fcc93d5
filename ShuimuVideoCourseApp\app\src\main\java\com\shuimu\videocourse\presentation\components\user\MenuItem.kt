package com.shuimu.videocourse.presentation.components.user

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 菜单项组件
 * 
 * 基于原型设计：UI Prototype/03-我的页面.html 菜单项列表区域
 * - 支持图标、标题、箭头布局
 * - 实现统一菜单项样式和点击状态反馈
 * - 添加分割线支持
 * - 支持自定义图标背景色
 * 
 * @param icon 菜单图标
 * @param title 菜单标题
 * @param subtitle 菜单副标题（可选）
 * @param iconBackgroundColor 图标背景色
 * @param showArrow 是否显示右箭头
 * @param showDivider 是否显示分割线
 * @param onClick 点击回调
 * @param modifier 修饰符
 */
@Composable
fun MenuItem(
    icon: ImageVector,
    title: String,
    subtitle: String? = null,
    iconBackgroundColor: Color = Color(0xFF667eea),
    showArrow: Boolean = true,
    showDivider: Boolean = false,
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .background(Color.White)
                .clickable { onClick() }
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 菜单图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(RoundedCornerShape(10.dp))
                    .background(iconBackgroundColor),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    modifier = Modifier.size(18.dp),
                    tint = Color.White
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 标题和副标题
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF374151)
                )
                
                if (subtitle != null) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = subtitle,
                        fontSize = 12.sp,
                        color = Color(0xFF6b7280)
                    )
                }
            }
            
            // 右箭头
            if (showArrow) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "进入",
                    modifier = Modifier.size(20.dp),
                    tint = Color(0xFF9ca3af)
                )
            }
        }
        
        // 分割线
        if (showDivider) {
            Divider(
                modifier = Modifier.padding(horizontal = 16.dp),
                color = Color(0xFFf3f4f6),
                thickness = 1.dp
            )
        }
    }
}

/**
 * 菜单项列表组件
 * 
 * @param items 菜单项列表
 * @param modifier 修饰符
 */
@Composable
fun MenuItemList(
    items: List<MenuItemData>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(vertical = 8.dp)
        ) {
            items.forEachIndexed { index, item ->
                MenuItem(
                    icon = item.icon,
                    title = item.title,
                    subtitle = item.subtitle,
                    iconBackgroundColor = item.iconBackgroundColor,
                    showArrow = item.showArrow,
                    showDivider = index < items.size - 1,
                    onClick = item.onClick
                )
            }
        }
    }
}

/**
 * 菜单项数据类
 */
data class MenuItemData(
    val icon: ImageVector,
    val title: String,
    val subtitle: String? = null,
    val iconBackgroundColor: Color = Color(0xFF667eea),
    val showArrow: Boolean = true,
    val onClick: () -> Unit = {}
)

// 预览组件
@Composable
fun MenuItemPreview() {
    ShuimuTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 单个菜单项
            MenuItem(
                icon = Icons.Default.KeyboardArrowRight,
                title = "我的课程",
                subtitle = "已购买15门课程",
                iconBackgroundColor = Color(0xFF667eea),
                onClick = {}
            )
            
            // 菜单项组
            MenuItemGroup(
                title = "学习管理",
                items = listOf(
                    MenuItemData(
                        icon = Icons.Default.KeyboardArrowRight,
                        title = "学习记录",
                        subtitle = "查看学习进度",
                        iconBackgroundColor = Color(0xFF667eea)
                    ),
                    MenuItemData(
                        icon = Icons.Default.KeyboardArrowRight,
                        title = "离线缓存",
                        subtitle = "管理下载内容",
                        iconBackgroundColor = Color(0xFF667eea),
                        showArrow = false
                    ),
                    MenuItemData(
                        icon = Icons.Default.KeyboardArrowRight,
                        title = "我的收藏",
                        subtitle = "收藏的课程和视频",
                        iconBackgroundColor = Color(0xFF667eea)
                    )
                )
            )
        }
    }
} 