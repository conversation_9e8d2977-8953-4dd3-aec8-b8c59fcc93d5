package com.shuimu.videocourse.presentation.components.modal

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Error
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.shuimu.videocourse.presentation.components.basic.ShuimuButton
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 支付状态枚举
 */
enum class PaymentStatus {
    SUCCESS,    // 支付成功
    FAILED,     // 支付失败
    PROCESSING  // 支付处理中
}

/**
 * 支付结果数据类
 */
data class PaymentResult(
    val status: PaymentStatus,
    val title: String,
    val message: String,
    val amount: String? = null,
    val orderId: String? = null,
    val timestamp: String? = null
)

/**
 * 水幕视频课程App - 支付结果弹窗组件
 * 
 * 基于原型设计：UI Prototype/01-首页.html 第2062-2081行支付结果弹窗
 * 
 * 功能特性：
 * - 支持支付成功/失败状态显示
 * - 实现操作按钮区域和支付信息展示
 * - 添加24%高度的底部弹窗样式
 * - 使用原型的#667eea紫蓝色渐变主题
 * 
 * @param isVisible 弹窗是否可见
 * @param paymentResult 支付结果数据
 * @param onDismiss 关闭弹窗回调
 * @param onPrimaryAction 主要操作按钮回调
 * @param onSecondaryAction 次要操作按钮回调
 * @param primaryActionText 主要操作按钮文字
 * @param secondaryActionText 次要操作按钮文字
 * @param modifier 修饰符
 */
@Composable
fun PaymentModal(
    isVisible: Boolean,
    paymentResult: PaymentResult,
    onDismiss: () -> Unit,
    onPrimaryAction: () -> Unit = {},
    onSecondaryAction: (() -> Unit)? = null,
    primaryActionText: String = "确定",
    secondaryActionText: String = "重试",
    modifier: Modifier = Modifier
) {
    if (isVisible) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false // 支付结果不允许点击外部关闭
            )
        ) {
            // 底部弹出动画
            AnimatedVisibility(
                visible = isVisible,
                enter = slideInVertically(
                    initialOffsetY = { it },
                    animationSpec = tween(300, easing = EaseOutCubic)
                ) + fadeIn(animationSpec = tween(300)),
                exit = slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(200, easing = EaseInCubic)
                ) + fadeOut(animationSpec = tween(200))
            ) {
                PaymentModalContent(
                    paymentResult = paymentResult,
                    onDismiss = onDismiss,
                    onPrimaryAction = onPrimaryAction,
                    onSecondaryAction = onSecondaryAction,
                    primaryActionText = primaryActionText,
                    secondaryActionText = secondaryActionText,
                    modifier = modifier
                )
            }
        }
    }
}

/**
 * 支付弹窗内容组件
 */
@Composable
private fun PaymentModalContent(
    paymentResult: PaymentResult,
    onDismiss: () -> Unit,
    onPrimaryAction: () -> Unit,
    onSecondaryAction: (() -> Unit)?,
    primaryActionText: String,
    secondaryActionText: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 关闭按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = Color(0xFF6B7280),
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 状态图标
            PaymentStatusIcon(status = paymentResult.status)
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 标题
            Text(
                text = paymentResult.title,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1F2937),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 消息
            Text(
                text = paymentResult.message,
                fontSize = 14.sp,
                color = Color(0xFF6B7280),
                textAlign = TextAlign.Center,
                lineHeight = 20.sp
            )
            
            // 支付详情信息
            if (paymentResult.amount != null || paymentResult.orderId != null) {
                Spacer(modifier = Modifier.height(20.dp))
                PaymentDetailsSection(paymentResult = paymentResult)
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 操作按钮区域
            PaymentActionButtons(
                paymentResult = paymentResult,
                onPrimaryAction = onPrimaryAction,
                onSecondaryAction = onSecondaryAction,
                primaryActionText = primaryActionText,
                secondaryActionText = secondaryActionText
            )
        }
    }
}

/**
 * 支付状态图标组件
 */
@Composable
private fun PaymentStatusIcon(
    status: PaymentStatus,
    modifier: Modifier = Modifier
) {
    val (icon, backgroundColor, iconColor) = when (status) {
        PaymentStatus.SUCCESS -> Triple(
            Icons.Default.CheckCircle,
            Color(0xFF10B981),
            Color.White
        )
        PaymentStatus.FAILED -> Triple(
            Icons.Default.Error,
            Color(0xFFEF4444),
            Color.White
        )
        PaymentStatus.PROCESSING -> Triple(
            Icons.Default.CheckCircle, // 可以替换为加载图标
            Color(0xFF3B82F6),
            Color.White
        )
    }
    
    Box(
        modifier = modifier
            .size(64.dp)
            .clip(CircleShape)
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        if (status == PaymentStatus.PROCESSING) {
            // 处理中状态显示加载动画
            CircularProgressIndicator(
                color = Color.White,
                strokeWidth = 3.dp,
                modifier = Modifier.size(32.dp)
            )
        } else {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = iconColor,
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

/**
 * 支付详情区域组件
 */
@Composable
private fun PaymentDetailsSection(
    paymentResult: PaymentResult,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF9FAFB)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            if (paymentResult.amount != null) {
                PaymentDetailItem(
                    label = "支付金额",
                    value = paymentResult.amount
                )
            }
            
            if (paymentResult.orderId != null) {
                if (paymentResult.amount != null) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
                PaymentDetailItem(
                    label = "订单号",
                    value = paymentResult.orderId
                )
            }
            
            if (paymentResult.timestamp != null) {
                if (paymentResult.amount != null || paymentResult.orderId != null) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
                PaymentDetailItem(
                    label = "支付时间",
                    value = paymentResult.timestamp
                )
            }
        }
    }
}

/**
 * 支付详情项组件
 */
@Composable
private fun PaymentDetailItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = Color(0xFF6B7280)
        )
        Text(
            text = value,
            fontSize = 14.sp,
            color = Color(0xFF1F2937),
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 支付操作按钮区域组件
 */
@Composable
private fun PaymentActionButtons(
    paymentResult: PaymentResult,
    onPrimaryAction: () -> Unit,
    onSecondaryAction: (() -> Unit)?,
    primaryActionText: String,
    secondaryActionText: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 主要操作按钮
        ShuimuButton(
            text = primaryActionText,
            onClick = onPrimaryAction,
            modifier = Modifier.fillMaxWidth()
        )
        
        // 次要操作按钮（仅在支付失败时显示）
        if (paymentResult.status == PaymentStatus.FAILED && onSecondaryAction != null) {
            ShuimuButton(
                text = secondaryActionText,
                onClick = onSecondaryAction,
                type = com.shuimu.videocourse.presentation.components.basic.ShuimuButtonType.SECONDARY,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 预览组件
 */
@Composable
fun PaymentModalPreview() {
    ShuimuTheme {
        var isVisible by remember { mutableStateOf(true) }
        var currentStatus by remember { mutableStateOf(PaymentStatus.SUCCESS) }
        
        Column {
            // 状态切换按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Button(onClick = { currentStatus = PaymentStatus.SUCCESS }) {
                    Text("成功")
                }
                Button(onClick = { currentStatus = PaymentStatus.FAILED }) {
                    Text("失败")
                }
                Button(onClick = { currentStatus = PaymentStatus.PROCESSING }) {
                    Text("处理中")
                }
            }
            
            Button(
                onClick = { isVisible = true },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            ) {
                Text("显示支付弹窗")
            }
        }
        
        // 支付弹窗
        PaymentModal(
            isVisible = isVisible,
            paymentResult = when (currentStatus) {
                PaymentStatus.SUCCESS -> PaymentResult(
                    status = PaymentStatus.SUCCESS,
                    title = "支付成功",
                    message = "恭喜您成功购买课程，现在可以开始学习了！",
                    amount = "¥199.00",
                    orderId = "202506141845001",
                    timestamp = "2025-06-14 18:45:30"
                )
                PaymentStatus.FAILED -> PaymentResult(
                    status = PaymentStatus.FAILED,
                    title = "支付失败",
                    message = "支付过程中出现问题，请重试或联系客服",
                    amount = "¥199.00",
                    orderId = "202506141845001"
                )
                PaymentStatus.PROCESSING -> PaymentResult(
                    status = PaymentStatus.PROCESSING,
                    title = "支付处理中",
                    message = "正在处理您的支付，请稍候...",
                    amount = "¥199.00"
                )
            },
            onDismiss = { isVisible = false },
            onPrimaryAction = { isVisible = false },
            onSecondaryAction = if (currentStatus == PaymentStatus.FAILED) {
                { /* 重试支付 */ }
            } else null
        )
    }
} 