-- Merging decision tree log ---
manifest
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:1-45:12
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:1-45:12
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:1-45:12
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:1-45:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bc7688b5534c260acddb8fa6d26fc04\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7197af669fb7f1b6d1809e004200518\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\548847645c5060a8e09192425e9ae9c0\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba47f6f1c22dc6cef6d31c25681185a\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\982a3d5b37c26b685d4cf4e2e5ebc532\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8255166624e0f5c0f0935b39c77f43b7\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa811942c8e43e1d0da86d4f080d8236\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77986f78dc41ccae752dd70d6e958c96\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffd1588ed6aea344175281e9979fc139\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7f8ee867ea51c7e078a027f9a0aea05\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\27ed4fa71bb6f3d8fc65c7f5778548e0\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b47b9cd49e44cd07d4be9b14eaffc92\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\31c4177c16f25981baff1a934c7a9642\transformed\navigation-compose-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\96269eac36e54192709622584090080f\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\c96f19fc07f17fb20010708be2d75c1a\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ef2e3c04683d95cc1fdd1a0d95405e3\transformed\activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72373185c1e14d184691335e7ab6a10e\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb7abd4809ccccce173ae7cc8e4976ad\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1df2d9b632a458d1cb3c4fcdb59271a\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1a953054351f2bba2305409b9f86f87\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b77bba033011331d43588cdac4063f8a\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1562e79b65c424295e52129c56217f6\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae565f33c2627ab135ea1ba04b64023d\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c6e5aeb6335b1d3d811eba7e101a128\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67392171ecdad6503be099a1b806cd2\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9354082842fca352484dc1ebae09648c\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\34770ec1910a159183bf9f8278ca205b\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1dd081a460d57e19320a509d8065d488\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fa04082afdc72167169e0545ff820dc\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4695992459027d14ddb9c7008116e4aa\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca030952d354a1acd3c491d10f96210\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\efcda0b86b8df48b0d3272761e383b9c\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bc70a30fb5a58be107a1266dbda4c71\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ca5ab744628cbfe9194aa0be91293ac\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d47c47804099285dd30893b8e57f6d2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fde558e318bbf291b137a3db9764bed\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b81c99ed267bbd4348855bf18fe3d063\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aac13e98018516f2988e890db2fb12d5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dde7cf5a412963a095ca3eef616aa2f5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fd43d62958b8e614e08927327272143\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8de163980da4a1c8da720f17f268c8f3\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50d778d6a8e171c743d44686ee6dd401\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\174f010f9decb921b32b6ae1840ff9e1\transformed\media3-ui-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fc71035d8fc17a21e98a9e73dc7adc1\transformed\media3-extractor-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c0c434ffd0706729a473e1cec0653a4\transformed\media3-container-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\291421643bc94180f60fc82121e27ac3\transformed\media3-datasource-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8cded3eb78495c4513def9cf4923c00\transformed\media3-decoder-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8bb04dd0312d15f237508f2abbd61fe\transformed\media3-database-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b3c5c6a304431425822ea3310867276\transformed\media3-common-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51217fba78aae74607c1afa7de4307cd\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b59b431625c9d51b67b5ea5642d0cae\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b99ce37de41192ac66063c64114bdfa7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e762d35924f16f9f435613a39d192283\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0186690b9525e59f89fee8514673c5be\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04ef683a139c188ec55a27e1cc776c0d\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9be784bbdf1d060e009a43a39e4d9414\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9401edc14c628718d016fc2ff944daa4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f7010c0ad30291becf34ca1d71066c2\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e17042af5832684273c8a2232bd5319\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a43fe8eb7cb897b58f35fe39f3eef6c3\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b985255ad6db5cd24a6a1552c4bb9ecc\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ed988505f3133b410cbfeb81a768f26\transformed\lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20cb99043421bd742d9bf62da91a9a50\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de9d29563384227c15503186603106a1\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\080b4ef16bbea6635f57f830611a441d\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb11a69ad02dfbc6b3d00a9c1881012d\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\792cb359f5ecd5e2d42aebd303e10fd2\transformed\activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbe6f09412baccfb94c6dc61acd19f46\transformed\activity-compose-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec8720b62c96050862c113826e71183b\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e747e13c69548a3ec0ab18cb00bc41c8\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2faf3f82d5002238304dfc9d9be3da\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd28c625b2ebe0e78f9df016203d1a5f\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abbdec539f0c77f9e4cf1c50a470b0c2\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0978f9de91cd8c2e8405567a9f95fa\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\238f6c677efceeb31bff01c47c785559\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d99e1c4837b25cf12e2189e35e6eea2\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\406d719abd717bc2d28c5d5b3826b7e6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4badd4833547c40b648d4b762981b0be\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\791089f9a94d6b7a41d50536282a5d38\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bffc5a9dece2075931683201af2cd00f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7002902cb15e715a58a9e7bfd010cca\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6039848a853a4eeb189fcbf833c4109a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c66048e9004561b13ecd2bc0c90a13bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b185b4a0a1e3be86297237f95767020\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b3c5c6a304431425822ea3310867276\transformed\media3-common-1.2.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b3c5c6a304431425822ea3310867276\transformed\media3-common-1.2.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51217fba78aae74607c1afa7de4307cd\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51217fba78aae74607c1afa7de4307cd\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:8:22-77
application
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:10:5-43:19
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:10:5-43:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bffc5a9dece2075931683201af2cd00f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bffc5a9dece2075931683201af2cd00f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c66048e9004561b13ecd2bc0c90a13bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c66048e9004561b13ecd2bc0c90a13bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:18:9-35
	android:label
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:16:9-41
	android:fullBackupContent
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:14:9-54
	android:roundIcon
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:17:9-63
	tools:targetApi
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:20:9-29
	android:icon
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:15:9-58
	android:allowBackup
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:12:9-35
	android:theme
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:19:9-58
	android:dataExtractionRules
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:13:9-65
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:11:9-42
activity#com.shuimu.videocourse.MainActivity
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:21:9-30:20
	android:label
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:24:13-45
	android:exported
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:23:13-36
	android:theme
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:25:13-62
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:22:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:26:13-29:29
action#android.intent.action.MAIN
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:17-69
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:27:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:17-77
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:28:27-74
activity#com.shuimu.videocourse.presentation.components.basic.BadgeTestActivity
ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:33:9-42:20
	android:label
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:36:13-33
	android:exported
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:37:13-62
	android:name
		ADDED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml:34:13-76
uses-sdk
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bc7688b5534c260acddb8fa6d26fc04\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bc7688b5534c260acddb8fa6d26fc04\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7197af669fb7f1b6d1809e004200518\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7197af669fb7f1b6d1809e004200518\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\548847645c5060a8e09192425e9ae9c0\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\548847645c5060a8e09192425e9ae9c0\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba47f6f1c22dc6cef6d31c25681185a\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba47f6f1c22dc6cef6d31c25681185a\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\982a3d5b37c26b685d4cf4e2e5ebc532\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\982a3d5b37c26b685d4cf4e2e5ebc532\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8255166624e0f5c0f0935b39c77f43b7\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8255166624e0f5c0f0935b39c77f43b7\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa811942c8e43e1d0da86d4f080d8236\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa811942c8e43e1d0da86d4f080d8236\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77986f78dc41ccae752dd70d6e958c96\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77986f78dc41ccae752dd70d6e958c96\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffd1588ed6aea344175281e9979fc139\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffd1588ed6aea344175281e9979fc139\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7f8ee867ea51c7e078a027f9a0aea05\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7f8ee867ea51c7e078a027f9a0aea05\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\27ed4fa71bb6f3d8fc65c7f5778548e0\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\27ed4fa71bb6f3d8fc65c7f5778548e0\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b47b9cd49e44cd07d4be9b14eaffc92\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b47b9cd49e44cd07d4be9b14eaffc92\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\31c4177c16f25981baff1a934c7a9642\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\31c4177c16f25981baff1a934c7a9642\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\96269eac36e54192709622584090080f\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\96269eac36e54192709622584090080f\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\c96f19fc07f17fb20010708be2d75c1a\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\c96f19fc07f17fb20010708be2d75c1a\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ef2e3c04683d95cc1fdd1a0d95405e3\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ef2e3c04683d95cc1fdd1a0d95405e3\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72373185c1e14d184691335e7ab6a10e\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72373185c1e14d184691335e7ab6a10e\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb7abd4809ccccce173ae7cc8e4976ad\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb7abd4809ccccce173ae7cc8e4976ad\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1df2d9b632a458d1cb3c4fcdb59271a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1df2d9b632a458d1cb3c4fcdb59271a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1a953054351f2bba2305409b9f86f87\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1a953054351f2bba2305409b9f86f87\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b77bba033011331d43588cdac4063f8a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b77bba033011331d43588cdac4063f8a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1562e79b65c424295e52129c56217f6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1562e79b65c424295e52129c56217f6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae565f33c2627ab135ea1ba04b64023d\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae565f33c2627ab135ea1ba04b64023d\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c6e5aeb6335b1d3d811eba7e101a128\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c6e5aeb6335b1d3d811eba7e101a128\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67392171ecdad6503be099a1b806cd2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67392171ecdad6503be099a1b806cd2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9354082842fca352484dc1ebae09648c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9354082842fca352484dc1ebae09648c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\34770ec1910a159183bf9f8278ca205b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\34770ec1910a159183bf9f8278ca205b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1dd081a460d57e19320a509d8065d488\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1dd081a460d57e19320a509d8065d488\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fa04082afdc72167169e0545ff820dc\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fa04082afdc72167169e0545ff820dc\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4695992459027d14ddb9c7008116e4aa\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4695992459027d14ddb9c7008116e4aa\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca030952d354a1acd3c491d10f96210\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca030952d354a1acd3c491d10f96210\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\efcda0b86b8df48b0d3272761e383b9c\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\efcda0b86b8df48b0d3272761e383b9c\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bc70a30fb5a58be107a1266dbda4c71\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bc70a30fb5a58be107a1266dbda4c71\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ca5ab744628cbfe9194aa0be91293ac\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ca5ab744628cbfe9194aa0be91293ac\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d47c47804099285dd30893b8e57f6d2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d47c47804099285dd30893b8e57f6d2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fde558e318bbf291b137a3db9764bed\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fde558e318bbf291b137a3db9764bed\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b81c99ed267bbd4348855bf18fe3d063\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b81c99ed267bbd4348855bf18fe3d063\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aac13e98018516f2988e890db2fb12d5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aac13e98018516f2988e890db2fb12d5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dde7cf5a412963a095ca3eef616aa2f5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dde7cf5a412963a095ca3eef616aa2f5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fd43d62958b8e614e08927327272143\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fd43d62958b8e614e08927327272143\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8de163980da4a1c8da720f17f268c8f3\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8de163980da4a1c8da720f17f268c8f3\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50d778d6a8e171c743d44686ee6dd401\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50d778d6a8e171c743d44686ee6dd401\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\174f010f9decb921b32b6ae1840ff9e1\transformed\media3-ui-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\174f010f9decb921b32b6ae1840ff9e1\transformed\media3-ui-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fc71035d8fc17a21e98a9e73dc7adc1\transformed\media3-extractor-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fc71035d8fc17a21e98a9e73dc7adc1\transformed\media3-extractor-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c0c434ffd0706729a473e1cec0653a4\transformed\media3-container-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c0c434ffd0706729a473e1cec0653a4\transformed\media3-container-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\291421643bc94180f60fc82121e27ac3\transformed\media3-datasource-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\291421643bc94180f60fc82121e27ac3\transformed\media3-datasource-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8cded3eb78495c4513def9cf4923c00\transformed\media3-decoder-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8cded3eb78495c4513def9cf4923c00\transformed\media3-decoder-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8bb04dd0312d15f237508f2abbd61fe\transformed\media3-database-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8bb04dd0312d15f237508f2abbd61fe\transformed\media3-database-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b3c5c6a304431425822ea3310867276\transformed\media3-common-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b3c5c6a304431425822ea3310867276\transformed\media3-common-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51217fba78aae74607c1afa7de4307cd\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51217fba78aae74607c1afa7de4307cd\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b59b431625c9d51b67b5ea5642d0cae\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b59b431625c9d51b67b5ea5642d0cae\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b99ce37de41192ac66063c64114bdfa7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b99ce37de41192ac66063c64114bdfa7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e762d35924f16f9f435613a39d192283\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e762d35924f16f9f435613a39d192283\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0186690b9525e59f89fee8514673c5be\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0186690b9525e59f89fee8514673c5be\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04ef683a139c188ec55a27e1cc776c0d\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04ef683a139c188ec55a27e1cc776c0d\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9be784bbdf1d060e009a43a39e4d9414\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9be784bbdf1d060e009a43a39e4d9414\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9401edc14c628718d016fc2ff944daa4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9401edc14c628718d016fc2ff944daa4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f7010c0ad30291becf34ca1d71066c2\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f7010c0ad30291becf34ca1d71066c2\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e17042af5832684273c8a2232bd5319\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e17042af5832684273c8a2232bd5319\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a43fe8eb7cb897b58f35fe39f3eef6c3\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a43fe8eb7cb897b58f35fe39f3eef6c3\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b985255ad6db5cd24a6a1552c4bb9ecc\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b985255ad6db5cd24a6a1552c4bb9ecc\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ed988505f3133b410cbfeb81a768f26\transformed\lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ed988505f3133b410cbfeb81a768f26\transformed\lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20cb99043421bd742d9bf62da91a9a50\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20cb99043421bd742d9bf62da91a9a50\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de9d29563384227c15503186603106a1\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de9d29563384227c15503186603106a1\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\080b4ef16bbea6635f57f830611a441d\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\080b4ef16bbea6635f57f830611a441d\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb11a69ad02dfbc6b3d00a9c1881012d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb11a69ad02dfbc6b3d00a9c1881012d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\792cb359f5ecd5e2d42aebd303e10fd2\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\792cb359f5ecd5e2d42aebd303e10fd2\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbe6f09412baccfb94c6dc61acd19f46\transformed\activity-compose-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbe6f09412baccfb94c6dc61acd19f46\transformed\activity-compose-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec8720b62c96050862c113826e71183b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec8720b62c96050862c113826e71183b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e747e13c69548a3ec0ab18cb00bc41c8\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e747e13c69548a3ec0ab18cb00bc41c8\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2faf3f82d5002238304dfc9d9be3da\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2faf3f82d5002238304dfc9d9be3da\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd28c625b2ebe0e78f9df016203d1a5f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd28c625b2ebe0e78f9df016203d1a5f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abbdec539f0c77f9e4cf1c50a470b0c2\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abbdec539f0c77f9e4cf1c50a470b0c2\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0978f9de91cd8c2e8405567a9f95fa\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d0978f9de91cd8c2e8405567a9f95fa\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\238f6c677efceeb31bff01c47c785559\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\238f6c677efceeb31bff01c47c785559\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d99e1c4837b25cf12e2189e35e6eea2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d99e1c4837b25cf12e2189e35e6eea2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\406d719abd717bc2d28c5d5b3826b7e6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\406d719abd717bc2d28c5d5b3826b7e6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4badd4833547c40b648d4b762981b0be\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4badd4833547c40b648d4b762981b0be\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\791089f9a94d6b7a41d50536282a5d38\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\791089f9a94d6b7a41d50536282a5d38\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bffc5a9dece2075931683201af2cd00f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bffc5a9dece2075931683201af2cd00f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7002902cb15e715a58a9e7bfd010cca\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7002902cb15e715a58a9e7bfd010cca\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6039848a853a4eeb189fcbf833c4109a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6039848a853a4eeb189fcbf833c4109a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c66048e9004561b13ecd2bc0c90a13bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c66048e9004561b13ecd2bc0c90a13bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b185b4a0a1e3be86297237f95767020\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b185b4a0a1e3be86297237f95767020\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\01-shuimu_01\ShuimuVideoCourseApp\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7c45ccea0d28a383881179875a769cc\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bffc5a9dece2075931683201af2cd00f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bffc5a9dece2075931683201af2cd00f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6c129f44d5ee633de57904fdcea88fd\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a674cdd347c28cfe8379382db04586\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.shuimu.videocourse.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.shuimu.videocourse.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b4dfaf8f521cdef86a5e180be948ba\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f990072a682e42ea1d1224a68768183e\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\741642e7f091970f1d6af093961c8690\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fcd84adcd045ba5af8650649642961\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
