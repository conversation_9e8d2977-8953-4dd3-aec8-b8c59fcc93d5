package com.shuimu.videocourse.presentation.components.display

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.basic.ShuimuButton
import com.shuimu.videocourse.presentation.components.basic.ShuimuButtonType
import com.shuimu.videocourse.presentation.components.basic.ShuimuButtonSize
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 系列头部组件
 * 
 * 基于原型设计：UI Prototype/01-首页.html 系列卡片头部区域（category-header-component）
 * 
 * 支持功能：
 * - 系列标题、价格、状态显示
 * - 免费标签和购买状态
 * - 购买按钮集成
 * - 渐变背景效果
 * - 状态指示器
 */
@Composable
fun SeriesHeader(
    title: String,
    price: String = "", // 价格，如"¥199"，空字符串表示免费
    originalPrice: String = "", // 原价，如"¥299"，用于显示折扣
    isFree: Boolean = false, // 是否免费
    isPurchased: Boolean = false, // 是否已购买
    isOnSale: Boolean = false, // 是否促销中
    description: String = "", // 系列描述
    onPurchaseClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color(0xFF667EEA), // #667eea
                            Color(0xFF764BA2)  // #764ba2
                        )
                    ),
                    shape = RoundedCornerShape(12.dp)
                )
                .padding(20.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 标题行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    // 标题和状态标签
                    Column(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = title,
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        // 状态标签行
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 免费标签
                            if (isFree) {
                                StatusTag(
                                    text = "免费",
                                    backgroundColor = Color(0xFF10B981), // 绿色
                                    textColor = Color.White
                                )
                            }
                            
                            // 已购买标签
                            if (isPurchased) {
                                StatusTag(
                                    text = "已购买",
                                    backgroundColor = Color(0xFF3B82F6), // 蓝色
                                    textColor = Color.White
                                )
                            }
                            
                            // 促销标签
                            if (isOnSale && !isFree && !isPurchased) {
                                StatusTag(
                                    text = "限时优惠",
                                    backgroundColor = Color(0xFFEF4444), // 红色
                                    textColor = Color.White
                                )
                            }
                        }
                    }
                    
                    // 价格区域
                    if (!isFree && !isPurchased) {
                        PriceSection(
                            price = price,
                            originalPrice = originalPrice,
                            isOnSale = isOnSale
                        )
                    }
                }
                
                // 描述文本
                if (description.isNotEmpty()) {
                    Text(
                        text = description,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color.White.copy(alpha = 0.9f),
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis,
                        lineHeight = 20.sp
                    )
                }
                
                // 操作按钮区域
                if (!isPurchased) {
                    ActionButtonSection(
                        isFree = isFree,
                        price = price,
                        onPurchaseClick = onPurchaseClick
                    )
                }
            }
        }
    }
}

/**
 * 状态标签组件
 */
@Composable
private fun StatusTag(
    text: String,
    backgroundColor: Color,
    textColor: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = textColor
        )
    }
}

/**
 * 价格区域组件
 */
@Composable
private fun PriceSection(
    price: String,
    originalPrice: String,
    isOnSale: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.End,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // 当前价格
        Text(
            text = price,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
        
        // 原价（促销时显示）
        if (isOnSale && originalPrice.isNotEmpty()) {
            Text(
                text = originalPrice,
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal,
                color = Color.White.copy(alpha = 0.7f),
                style = androidx.compose.ui.text.TextStyle(
                    textDecoration = androidx.compose.ui.text.style.TextDecoration.LineThrough
                )
            )
        }
    }
}

/**
 * 操作按钮区域组件
 */
@Composable
private fun ActionButtonSection(
    isFree: Boolean,
    price: String,
    onPurchaseClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.End
    ) {
        ShuimuButton(
            text = if (isFree) "免费学习" else "立即购买 $price",
            type = ShuimuButtonType.PRIMARY,
            size = ShuimuButtonSize.MEDIUM,
            onClick = onPurchaseClick,
            modifier = Modifier.widthIn(min = 120.dp)
        )
    }
}

/**
 * 系列头部预览组合
 */
@Composable
fun SeriesHeaderPreview() {
    ShuimuTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 免费系列
            SeriesHeader(
                title = "Kotlin入门基础教程",
                description = "从零开始学习Kotlin编程语言，掌握现代Android开发的核心技能。包含语法基础、面向对象编程、函数式编程等内容。",
                isFree = true,
                isPurchased = false
            )
            
            // 已购买系列
            SeriesHeader(
                title = "Android Jetpack Compose实战",
                description = "深入学习Jetpack Compose现代UI开发框架，构建美观高效的Android应用界面。",
                price = "¥199",
                isPurchased = true
            )
            
            // 付费系列 - 正常价格
            SeriesHeader(
                title = "高级架构设计模式",
                description = "学习企业级Android应用架构设计，包含MVVM、Clean Architecture、依赖注入等高级主题。",
                price = "¥299",
                isPurchased = false
            )
            
            // 付费系列 - 促销价格
            SeriesHeader(
                title = "Flutter跨平台开发完整教程",
                description = "一套代码，多端运行。掌握Flutter框架，开发iOS和Android应用。包含Dart语言、Widget系统、状态管理等。",
                price = "¥199",
                originalPrice = "¥299",
                isOnSale = true,
                isPurchased = false
            )
        }
    }
} 