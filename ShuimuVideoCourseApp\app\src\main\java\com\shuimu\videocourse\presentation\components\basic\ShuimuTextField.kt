package com.shuimu.videocourse.presentation.components.basic

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.EaseOutCubic
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.theme.*

/**
 * 水幕输入框组件
 * 
 * 基于UI原型设计：UI Prototype/02-搜索页.html 第1-30行搜索框样式定义
 * 原型样式：
 * - 背景：background: #f8fafc
 * - 圆角：border-radius: 8px
 * - 内边距：padding: 12px 16px
 * - 边框：border: 1px solid #e2e8f0
 * - 聚焦边框：border: 1px solid #667eea
 * - 字体：font-size: 16px, color: #1e293b
 * - 占位符：color: #94a3b8
 * - 过渡动画：transition: all 0.2s ease
 */

/**
 * 输入框类型枚举
 */
enum class ShuimuTextFieldType {
    DEFAULT,    // 默认输入框
    SEARCH,     // 搜索输入框 - 带搜索图标
    PASSWORD,   // 密码输入框 - 带显示/隐藏切换
    EMAIL       // 邮箱输入框 - 邮箱键盘类型
}

/**
 * 输入框尺寸枚举
 */
enum class ShuimuTextFieldSize {
    SMALL,      // 小型输入框 - 8dp内边距
    MEDIUM,     // 中型输入框 - 12dp内边距
    LARGE       // 大型输入框 - 16dp内边距
}

/**
 * 水幕输入框组件
 * 
 * @param value 输入值
 * @param onValueChange 值变化回调
 * @param modifier 修饰符
 * @param type 输入框类型
 * @param size 输入框尺寸
 * @param placeholder 占位符文本
 * @param label 标签文本
 * @param leadingIcon 前置图标
 * @param trailingIcon 后置图标
 * @param isError 是否显示错误状态
 * @param errorMessage 错误信息
 * @param enabled 是否启用
 * @param readOnly 是否只读
 * @param singleLine 是否单行
 * @param maxLines 最大行数
 * @param keyboardOptions 键盘选项
 * @param keyboardActions 键盘动作
 * @param visualTransformation 视觉转换
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShuimuTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    type: ShuimuTextFieldType = ShuimuTextFieldType.DEFAULT,
    size: ShuimuTextFieldSize = ShuimuTextFieldSize.MEDIUM,
    placeholder: String = "",
    label: String = "",
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null,
    isError: Boolean = false,
    errorMessage: String = "",
    enabled: Boolean = true,
    readOnly: Boolean = false,
    singleLine: Boolean = true,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    visualTransformation: VisualTransformation = VisualTransformation.None
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isFocused by interactionSource.collectIsFocusedAsState()
    val focusRequester = remember { FocusRequester() }
    
    // 密码可见性状态
    var passwordVisible by remember { mutableStateOf(false) }
    
    // 根据类型设置默认配置
    val (defaultKeyboardOptions, defaultVisualTransformation, defaultLeadingIcon, defaultTrailingIcon) = when (type) {
        ShuimuTextFieldType.SEARCH -> {
            val kbOptions = keyboardOptions.copy(
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Search
            )
            Quadruple(kbOptions, visualTransformation, Icons.Default.Search, trailingIcon)
        }
        ShuimuTextFieldType.PASSWORD -> {
            val kbOptions = keyboardOptions.copy(
                keyboardType = KeyboardType.Password,
                imeAction = ImeAction.Done
            )
            val visualTrans = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation()
            val trailingIconVector = if (passwordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff
            Quadruple(kbOptions, visualTrans, leadingIcon, trailingIconVector)
        }
        ShuimuTextFieldType.EMAIL -> {
            val kbOptions = keyboardOptions.copy(
                keyboardType = KeyboardType.Email,
                imeAction = ImeAction.Next
            )
            Quadruple(kbOptions, visualTransformation, leadingIcon, trailingIcon)
        }
        ShuimuTextFieldType.DEFAULT -> {
            Quadruple(keyboardOptions, visualTransformation, leadingIcon, trailingIcon)
        }
    }
    
    // 动画颜色 - 优化视觉反馈
    val borderColor by animateColorAsState(
        targetValue = when {
            isError -> Color(0xFFEF4444) // red-500
            isFocused -> Primary // #667eea
            else -> Color(0xFFE2E8F0) // slate-200
        },
        animationSpec = tween(200),
        label = "borderColor"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = when {
            isError -> Color(0xFFFEF2F2) // red-50
            isFocused -> Color.White
            else -> Color(0xFFF8FAFC) // slate-50
        },
        animationSpec = tween(200),
        label = "backgroundColor"
    )
    
    // 阴影动画 - 聚焦时增加阴影效果
    val shadowElevation by animateFloatAsState(
        targetValue = if (isFocused) 4f else 0f,
        animationSpec = tween(200),
        label = "shadowElevation"
    )
    
    // 缩放动画 - 移动端触摸反馈
    val scale by animateFloatAsState(
        targetValue = if (isFocused) 1.02f else 1f,
        animationSpec = tween(200, easing = EaseOutCubic),
        label = "scale"
    )
    
    // 内边距
    val padding = when (size) {
        ShuimuTextFieldSize.SMALL -> PaddingValues(horizontal = 12.dp, vertical = 8.dp)
        ShuimuTextFieldSize.MEDIUM -> PaddingValues(horizontal = 16.dp, vertical = 12.dp) // 原型：padding: 12px 16px
        ShuimuTextFieldSize.LARGE -> PaddingValues(horizontal = 20.dp, vertical = 16.dp)
    }
    
    // 字体大小
    val fontSize = when (size) {
        ShuimuTextFieldSize.SMALL -> 14.sp
        ShuimuTextFieldSize.MEDIUM -> 16.sp // 原型：font-size: 16px
        ShuimuTextFieldSize.LARGE -> 18.sp
    }
    
    Column(modifier = modifier) {
        // 标签
        if (label.isNotEmpty()) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF475569) // slate-600
                ),
                modifier = Modifier.padding(bottom = 6.dp)
            )
        }
        
        // 输入框容器
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .graphicsLayer {
                    scaleX = scale
                    scaleY = scale
                }
                .clip(RoundedCornerShape(8.dp)) // 原型：border-radius: 8px
                .shadow(
                    elevation = shadowElevation.dp,
                    shape = RoundedCornerShape(8.dp),
                    ambientColor = Primary.copy(alpha = 0.1f),
                    spotColor = Primary.copy(alpha = 0.1f)
                )
                .background(backgroundColor)
                .border(
                    width = 1.dp,
                    color = borderColor,
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(padding)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 前置图标
                (defaultLeadingIcon ?: leadingIcon)?.let { icon ->
                    Icon(
                        imageVector = icon,
                        contentDescription = null,
                        tint = if (isFocused) Primary else Color(0xFF94A3B8), // slate-400
                        modifier = Modifier
                            .size(20.dp)
                            .padding(end = 8.dp)
                    )
                }
                
                // 输入框
                Box(modifier = Modifier.weight(1f)) {
                    BasicTextField(
                        value = value,
                        onValueChange = onValueChange,
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester),
                        textStyle = TextStyle(
                            fontSize = fontSize,
                            color = Color(0xFF1E293B), // slate-800
                            fontWeight = FontWeight.Normal
                        ),
                        cursorBrush = SolidColor(Primary),
                        visualTransformation = defaultVisualTransformation,
                        keyboardOptions = defaultKeyboardOptions,
                        keyboardActions = keyboardActions,
                        singleLine = singleLine,
                        maxLines = maxLines,
                        enabled = enabled,
                        readOnly = readOnly,
                        interactionSource = interactionSource
                    )
                    
                    // 占位符
                    if (value.isEmpty() && placeholder.isNotEmpty()) {
                        Text(
                            text = placeholder,
                            style = TextStyle(
                                fontSize = fontSize,
                                color = Color(0xFF94A3B8), // slate-400
                                fontWeight = FontWeight.Normal
                            )
                        )
                    }
                }
                
                // 后置图标
                (defaultTrailingIcon ?: trailingIcon)?.let { icon ->
                    IconButton(
                        onClick = {
                            if (type == ShuimuTextFieldType.PASSWORD) {
                                passwordVisible = !passwordVisible
                            }
                        },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = icon,
                            contentDescription = if (type == ShuimuTextFieldType.PASSWORD) {
                                if (passwordVisible) "隐藏密码" else "显示密码"
                            } else null,
                            tint = Color(0xFF94A3B8), // slate-400
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
        }
        
        // 错误信息
        if (isError && errorMessage.isNotEmpty()) {
            Text(
                text = errorMessage,
                style = MaterialTheme.typography.bodySmall.copy(
                    color = Color(0xFFEF4444) // red-500
                ),
                modifier = Modifier.padding(top = 4.dp, start = 4.dp)
            )
        }
    }
}

/**
 * 预定义的输入框样式组合
 */
object ShuimuTextFieldDefaults {
    
    /**
     * 搜索输入框 - 基于原型搜索框样式
     * 原型：UI Prototype/02-搜索页.html 搜索框
     */
    @Composable
    fun SearchField(
        value: String,
        onValueChange: (String) -> Unit,
        modifier: Modifier = Modifier,
        placeholder: String = "搜索课程、系列...",
        onSearch: (String) -> Unit = {},
        enabled: Boolean = true
    ) {
        ShuimuTextField(
            value = value,
            onValueChange = onValueChange,
            modifier = modifier,
            type = ShuimuTextFieldType.SEARCH,
            size = ShuimuTextFieldSize.MEDIUM,
            placeholder = placeholder,
            enabled = enabled,
            keyboardActions = KeyboardActions(
                onSearch = { onSearch(value) }
            )
        )
    }
    
    /**
     * 密码输入框
     */
    @Composable
    fun PasswordField(
        value: String,
        onValueChange: (String) -> Unit,
        modifier: Modifier = Modifier,
        placeholder: String = "请输入密码",
        label: String = "",
        isError: Boolean = false,
        errorMessage: String = "",
        enabled: Boolean = true
    ) {
        ShuimuTextField(
            value = value,
            onValueChange = onValueChange,
            modifier = modifier,
            type = ShuimuTextFieldType.PASSWORD,
            size = ShuimuTextFieldSize.MEDIUM,
            placeholder = placeholder,
            label = label,
            isError = isError,
            errorMessage = errorMessage,
            enabled = enabled
        )
    }
    
    /**
     * 邮箱输入框
     */
    @Composable
    fun EmailField(
        value: String,
        onValueChange: (String) -> Unit,
        modifier: Modifier = Modifier,
        placeholder: String = "请输入邮箱地址",
        label: String = "",
        isError: Boolean = false,
        errorMessage: String = "",
        enabled: Boolean = true
    ) {
        ShuimuTextField(
            value = value,
            onValueChange = onValueChange,
            modifier = modifier,
            type = ShuimuTextFieldType.EMAIL,
            size = ShuimuTextFieldSize.MEDIUM,
            placeholder = placeholder,
            label = label,
            isError = isError,
            errorMessage = errorMessage,
            enabled = enabled
        )
    }
    
    /**
     * 通用输入框
     */
    @Composable
    fun StandardField(
        value: String,
        onValueChange: (String) -> Unit,
        modifier: Modifier = Modifier,
        placeholder: String = "",
        label: String = "",
        isError: Boolean = false,
        errorMessage: String = "",
        enabled: Boolean = true,
        singleLine: Boolean = true
    ) {
        ShuimuTextField(
            value = value,
            onValueChange = onValueChange,
            modifier = modifier,
            type = ShuimuTextFieldType.DEFAULT,
            size = ShuimuTextFieldSize.MEDIUM,
            placeholder = placeholder,
            label = label,
            isError = isError,
            errorMessage = errorMessage,
            enabled = enabled,
            singleLine = singleLine
        )
    }
}

/**
 * 辅助数据类 - 四元组
 */
private data class Quadruple<A, B, C, D>(
    val first: A,
    val second: B,
    val third: C,
    val fourth: D
) 