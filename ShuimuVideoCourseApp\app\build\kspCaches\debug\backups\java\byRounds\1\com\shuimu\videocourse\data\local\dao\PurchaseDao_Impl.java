package com.shuimu.videocourse.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.shuimu.videocourse.data.local.entity.PurchaseEntity;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PurchaseDao_Impl implements PurchaseDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<PurchaseEntity> __insertionAdapterOfPurchaseEntity;

  private final EntityDeletionOrUpdateAdapter<PurchaseEntity> __deletionAdapterOfPurchaseEntity;

  private final EntityDeletionOrUpdateAdapter<PurchaseEntity> __updateAdapterOfPurchaseEntity;

  private final SharedSQLiteStatement __preparedStmtOfUpdatePaymentStatus;

  private final SharedSQLiteStatement __preparedStmtOfUpdateRefundInfo;

  private final SharedSQLiteStatement __preparedStmtOfCancelPurchase;

  private final SharedSQLiteStatement __preparedStmtOfClearUserPurchases;

  private final SharedSQLiteStatement __preparedStmtOfClearAllPurchases;

  public PurchaseDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfPurchaseEntity = new EntityInsertionAdapter<PurchaseEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `purchases` (`purchaseId`,`userId`,`courseId`,`orderNumber`,`paymentMethod`,`originalPrice`,`actualPrice`,`discount`,`couponId`,`couponDiscount`,`status`,`paymentTime`,`refundTime`,`refundReason`,`transactionId`,`platform`,`deviceInfo`,`createTime`,`updateTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PurchaseEntity entity) {
        statement.bindString(1, entity.getPurchaseId());
        statement.bindString(2, entity.getUserId());
        statement.bindString(3, entity.getCourseId());
        statement.bindString(4, entity.getOrderNumber());
        statement.bindString(5, entity.getPaymentMethod());
        statement.bindDouble(6, entity.getOriginalPrice());
        statement.bindDouble(7, entity.getActualPrice());
        statement.bindDouble(8, entity.getDiscount());
        if (entity.getCouponId() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getCouponId());
        }
        statement.bindDouble(10, entity.getCouponDiscount());
        statement.bindString(11, entity.getStatus());
        if (entity.getPaymentTime() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getPaymentTime());
        }
        if (entity.getRefundTime() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getRefundTime());
        }
        if (entity.getRefundReason() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getRefundReason());
        }
        if (entity.getTransactionId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getTransactionId());
        }
        statement.bindString(16, entity.getPlatform());
        if (entity.getDeviceInfo() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getDeviceInfo());
        }
        statement.bindLong(18, entity.getCreateTime());
        statement.bindLong(19, entity.getUpdateTime());
      }
    };
    this.__deletionAdapterOfPurchaseEntity = new EntityDeletionOrUpdateAdapter<PurchaseEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `purchases` WHERE `purchaseId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PurchaseEntity entity) {
        statement.bindString(1, entity.getPurchaseId());
      }
    };
    this.__updateAdapterOfPurchaseEntity = new EntityDeletionOrUpdateAdapter<PurchaseEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `purchases` SET `purchaseId` = ?,`userId` = ?,`courseId` = ?,`orderNumber` = ?,`paymentMethod` = ?,`originalPrice` = ?,`actualPrice` = ?,`discount` = ?,`couponId` = ?,`couponDiscount` = ?,`status` = ?,`paymentTime` = ?,`refundTime` = ?,`refundReason` = ?,`transactionId` = ?,`platform` = ?,`deviceInfo` = ?,`createTime` = ?,`updateTime` = ? WHERE `purchaseId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PurchaseEntity entity) {
        statement.bindString(1, entity.getPurchaseId());
        statement.bindString(2, entity.getUserId());
        statement.bindString(3, entity.getCourseId());
        statement.bindString(4, entity.getOrderNumber());
        statement.bindString(5, entity.getPaymentMethod());
        statement.bindDouble(6, entity.getOriginalPrice());
        statement.bindDouble(7, entity.getActualPrice());
        statement.bindDouble(8, entity.getDiscount());
        if (entity.getCouponId() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getCouponId());
        }
        statement.bindDouble(10, entity.getCouponDiscount());
        statement.bindString(11, entity.getStatus());
        if (entity.getPaymentTime() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getPaymentTime());
        }
        if (entity.getRefundTime() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getRefundTime());
        }
        if (entity.getRefundReason() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getRefundReason());
        }
        if (entity.getTransactionId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getTransactionId());
        }
        statement.bindString(16, entity.getPlatform());
        if (entity.getDeviceInfo() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getDeviceInfo());
        }
        statement.bindLong(18, entity.getCreateTime());
        statement.bindLong(19, entity.getUpdateTime());
        statement.bindString(20, entity.getPurchaseId());
      }
    };
    this.__preparedStmtOfUpdatePaymentStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE purchases SET \n"
                + "        status = ?,\n"
                + "        paymentTime = ?,\n"
                + "        transactionId = ?,\n"
                + "        updateTime = ?\n"
                + "        WHERE purchaseId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateRefundInfo = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE purchases SET \n"
                + "        status = 'refunded',\n"
                + "        refundTime = ?,\n"
                + "        refundReason = ?,\n"
                + "        updateTime = ?\n"
                + "        WHERE purchaseId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfCancelPurchase = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE purchases SET status = 'cancelled', updateTime = ? WHERE purchaseId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearUserPurchases = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM purchases WHERE userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAllPurchases = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM purchases";
        return _query;
      }
    };
  }

  @Override
  public Object insertPurchase(final PurchaseEntity purchase,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPurchaseEntity.insert(purchase);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertPurchases(final List<PurchaseEntity> purchases,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPurchaseEntity.insert(purchases);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePurchase(final PurchaseEntity purchase,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfPurchaseEntity.handle(purchase);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePurchase(final PurchaseEntity purchase,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPurchaseEntity.handle(purchase);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePaymentStatus(final String purchaseId, final String status,
      final Long paymentTime, final String transactionId, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdatePaymentStatus.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, status);
        _argIndex = 2;
        if (paymentTime == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, paymentTime);
        }
        _argIndex = 3;
        if (transactionId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, transactionId);
        }
        _argIndex = 4;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 5;
        _stmt.bindString(_argIndex, purchaseId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdatePaymentStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateRefundInfo(final String purchaseId, final long refundTime,
      final String refundReason, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateRefundInfo.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, refundTime);
        _argIndex = 2;
        _stmt.bindString(_argIndex, refundReason);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 4;
        _stmt.bindString(_argIndex, purchaseId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateRefundInfo.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object cancelPurchase(final String purchaseId, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfCancelPurchase.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 2;
        _stmt.bindString(_argIndex, purchaseId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfCancelPurchase.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearUserPurchases(final String userId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearUserPurchases.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearUserPurchases.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAllPurchases(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllPurchases.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllPurchases.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getPurchaseById(final String purchaseId,
      final Continuation<? super PurchaseEntity> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE purchaseId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, purchaseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PurchaseEntity>() {
      @Override
      @Nullable
      public PurchaseEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final PurchaseEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPurchaseByOrderNumber(final String orderNumber,
      final Continuation<? super PurchaseEntity> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE orderNumber = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, orderNumber);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PurchaseEntity>() {
      @Override
      @Nullable
      public PurchaseEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final PurchaseEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<PurchaseEntity> observePurchaseById(final String purchaseId) {
    final String _sql = "SELECT * FROM purchases WHERE purchaseId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, purchaseId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"purchases"}, new Callable<PurchaseEntity>() {
      @Override
      @Nullable
      public PurchaseEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final PurchaseEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUserPurchases(final String userId,
      final Continuation<? super List<PurchaseEntity>> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE userId = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PurchaseEntity>>() {
      @Override
      @NonNull
      public List<PurchaseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<PurchaseEntity> _result = new ArrayList<PurchaseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PurchaseEntity _item;
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PurchaseEntity>> observeUserPurchases(final String userId) {
    final String _sql = "SELECT * FROM purchases WHERE userId = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"purchases"}, new Callable<List<PurchaseEntity>>() {
      @Override
      @NonNull
      public List<PurchaseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<PurchaseEntity> _result = new ArrayList<PurchaseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PurchaseEntity _item;
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUserCoursePurchase(final String userId, final String courseId,
      final Continuation<? super PurchaseEntity> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE userId = ? AND courseId = ? AND status = 'paid'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, courseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PurchaseEntity>() {
      @Override
      @Nullable
      public PurchaseEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final PurchaseEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<PurchaseEntity> observeUserCoursePurchase(final String userId,
      final String courseId) {
    final String _sql = "SELECT * FROM purchases WHERE userId = ? AND courseId = ? AND status = 'paid'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, courseId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"purchases"}, new Callable<PurchaseEntity>() {
      @Override
      @Nullable
      public PurchaseEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final PurchaseEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUserPaidPurchases(final String userId,
      final Continuation<? super List<PurchaseEntity>> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE userId = ? AND status = 'paid' ORDER BY paymentTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PurchaseEntity>>() {
      @Override
      @NonNull
      public List<PurchaseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<PurchaseEntity> _result = new ArrayList<PurchaseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PurchaseEntity _item;
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserPendingPurchases(final String userId,
      final Continuation<? super List<PurchaseEntity>> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE userId = ? AND status = 'pending' ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PurchaseEntity>>() {
      @Override
      @NonNull
      public List<PurchaseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<PurchaseEntity> _result = new ArrayList<PurchaseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PurchaseEntity _item;
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserCancelledPurchases(final String userId,
      final Continuation<? super List<PurchaseEntity>> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE userId = ? AND status = 'cancelled' ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PurchaseEntity>>() {
      @Override
      @NonNull
      public List<PurchaseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<PurchaseEntity> _result = new ArrayList<PurchaseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PurchaseEntity _item;
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserRefundedPurchases(final String userId,
      final Continuation<? super List<PurchaseEntity>> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE userId = ? AND status = 'refunded' ORDER BY refundTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PurchaseEntity>>() {
      @Override
      @NonNull
      public List<PurchaseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<PurchaseEntity> _result = new ArrayList<PurchaseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PurchaseEntity _item;
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPurchasesByPaymentMethod(final String userId, final String paymentMethod,
      final Continuation<? super List<PurchaseEntity>> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE userId = ? AND paymentMethod = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, paymentMethod);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PurchaseEntity>>() {
      @Override
      @NonNull
      public List<PurchaseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<PurchaseEntity> _result = new ArrayList<PurchaseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PurchaseEntity _item;
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPurchasesByTimeRange(final String userId, final long startTime,
      final long endTime, final Continuation<? super List<PurchaseEntity>> $completion) {
    final String _sql = "\n"
            + "        SELECT * FROM purchases \n"
            + "        WHERE userId = ? \n"
            + "        AND createTime BETWEEN ? AND ? \n"
            + "        ORDER BY createTime DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 3;
    _statement.bindLong(_argIndex, endTime);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PurchaseEntity>>() {
      @Override
      @NonNull
      public List<PurchaseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPurchaseId = CursorUtil.getColumnIndexOrThrow(_cursor, "purchaseId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "orderNumber");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentMethod");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfCouponId = CursorUtil.getColumnIndexOrThrow(_cursor, "couponId");
          final int _cursorIndexOfCouponDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "couponDiscount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentTime");
          final int _cursorIndexOfRefundTime = CursorUtil.getColumnIndexOrThrow(_cursor, "refundTime");
          final int _cursorIndexOfRefundReason = CursorUtil.getColumnIndexOrThrow(_cursor, "refundReason");
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfPlatform = CursorUtil.getColumnIndexOrThrow(_cursor, "platform");
          final int _cursorIndexOfDeviceInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceInfo");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<PurchaseEntity> _result = new ArrayList<PurchaseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PurchaseEntity _item;
            final String _tmpPurchaseId;
            _tmpPurchaseId = _cursor.getString(_cursorIndexOfPurchaseId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpCourseId;
            _tmpCourseId = _cursor.getString(_cursorIndexOfCourseId);
            final String _tmpOrderNumber;
            _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            final String _tmpPaymentMethod;
            _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final double _tmpActualPrice;
            _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            final String _tmpCouponId;
            if (_cursor.isNull(_cursorIndexOfCouponId)) {
              _tmpCouponId = null;
            } else {
              _tmpCouponId = _cursor.getString(_cursorIndexOfCouponId);
            }
            final double _tmpCouponDiscount;
            _tmpCouponDiscount = _cursor.getDouble(_cursorIndexOfCouponDiscount);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final Long _tmpPaymentTime;
            if (_cursor.isNull(_cursorIndexOfPaymentTime)) {
              _tmpPaymentTime = null;
            } else {
              _tmpPaymentTime = _cursor.getLong(_cursorIndexOfPaymentTime);
            }
            final Long _tmpRefundTime;
            if (_cursor.isNull(_cursorIndexOfRefundTime)) {
              _tmpRefundTime = null;
            } else {
              _tmpRefundTime = _cursor.getLong(_cursorIndexOfRefundTime);
            }
            final String _tmpRefundReason;
            if (_cursor.isNull(_cursorIndexOfRefundReason)) {
              _tmpRefundReason = null;
            } else {
              _tmpRefundReason = _cursor.getString(_cursorIndexOfRefundReason);
            }
            final String _tmpTransactionId;
            if (_cursor.isNull(_cursorIndexOfTransactionId)) {
              _tmpTransactionId = null;
            } else {
              _tmpTransactionId = _cursor.getString(_cursorIndexOfTransactionId);
            }
            final String _tmpPlatform;
            _tmpPlatform = _cursor.getString(_cursorIndexOfPlatform);
            final String _tmpDeviceInfo;
            if (_cursor.isNull(_cursorIndexOfDeviceInfo)) {
              _tmpDeviceInfo = null;
            } else {
              _tmpDeviceInfo = _cursor.getString(_cursorIndexOfDeviceInfo);
            }
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new PurchaseEntity(_tmpPurchaseId,_tmpUserId,_tmpCourseId,_tmpOrderNumber,_tmpPaymentMethod,_tmpOriginalPrice,_tmpActualPrice,_tmpDiscount,_tmpCouponId,_tmpCouponDiscount,_tmpStatus,_tmpPaymentTime,_tmpRefundTime,_tmpRefundReason,_tmpTransactionId,_tmpPlatform,_tmpDeviceInfo,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserPurchaseStats(final String userId,
      final Continuation<? super PurchaseStats> $completion) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            COUNT(*) as totalPurchases,\n"
            + "            SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paidPurchases,\n"
            + "            SUM(CASE WHEN status = 'paid' THEN actualPrice ELSE 0 END) as totalSpent,\n"
            + "            AVG(CASE WHEN status = 'paid' THEN actualPrice ELSE NULL END) as avgSpent\n"
            + "        FROM purchases \n"
            + "        WHERE userId = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PurchaseStats>() {
      @Override
      @Nullable
      public PurchaseStats call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTotalPurchases = 0;
          final int _cursorIndexOfPaidPurchases = 1;
          final int _cursorIndexOfTotalSpent = 2;
          final int _cursorIndexOfAvgSpent = 3;
          final PurchaseStats _result;
          if (_cursor.moveToFirst()) {
            final int _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getInt(_cursorIndexOfTotalPurchases);
            final int _tmpPaidPurchases;
            _tmpPaidPurchases = _cursor.getInt(_cursorIndexOfPaidPurchases);
            final double _tmpTotalSpent;
            _tmpTotalSpent = _cursor.getDouble(_cursorIndexOfTotalSpent);
            final Double _tmpAvgSpent;
            if (_cursor.isNull(_cursorIndexOfAvgSpent)) {
              _tmpAvgSpent = null;
            } else {
              _tmpAvgSpent = _cursor.getDouble(_cursorIndexOfAvgSpent);
            }
            _result = new PurchaseStats(_tmpTotalPurchases,_tmpPaidPurchases,_tmpTotalSpent,_tmpAvgSpent);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserMonthlyStats(final String userId, final long monthStartTime,
      final Continuation<? super MonthlyPurchaseStats> $completion) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            COUNT(*) as totalPurchases,\n"
            + "            SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paidPurchases,\n"
            + "            SUM(CASE WHEN status = 'paid' THEN actualPrice ELSE 0 END) as totalSpent\n"
            + "        FROM purchases \n"
            + "        WHERE userId = ? \n"
            + "        AND createTime >= ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, monthStartTime);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<MonthlyPurchaseStats>() {
      @Override
      @Nullable
      public MonthlyPurchaseStats call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTotalPurchases = 0;
          final int _cursorIndexOfPaidPurchases = 1;
          final int _cursorIndexOfTotalSpent = 2;
          final MonthlyPurchaseStats _result;
          if (_cursor.moveToFirst()) {
            final int _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getInt(_cursorIndexOfTotalPurchases);
            final int _tmpPaidPurchases;
            _tmpPaidPurchases = _cursor.getInt(_cursorIndexOfPaidPurchases);
            final double _tmpTotalSpent;
            _tmpTotalSpent = _cursor.getDouble(_cursorIndexOfTotalSpent);
            _result = new MonthlyPurchaseStats(_tmpTotalPurchases,_tmpPaidPurchases,_tmpTotalSpent);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
