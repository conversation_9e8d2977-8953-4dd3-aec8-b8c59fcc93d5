package com.shuimu.videocourse.presentation.components.video

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.basic.ProgressBar

/**
 * 播放速度选项
 */
enum class PlaybackSpeed(val value: Float, val label: String) {
    SPEED_0_5(0.5f, "0.5x"),
    SPEED_0_75(0.75f, "0.75x"),
    SPEED_1_0(1.0f, "1.0x"),
    SPEED_1_25(1.25f, "1.25x"),
    SPEED_1_5(1.5f, "1.5x"),
    SPEED_2_0(2.0f, "2.0x")
}

/**
 * 视频控制器数据类
 */
data class VideoControlsData(
    val isPlaying: Boolean = false,
    val currentPosition: Long = 0L,
    val duration: Long = 0L,
    val playbackSpeed: PlaybackSpeed = PlaybackSpeed.SPEED_1_0,
    val isFullscreen: Boolean = false,
    val isMuted: Boolean = false,
    val volume: Float = 1.0f,
    val showSpeedSelector: Boolean = false
)

/**
 * 水幕视频课程App - 视频控制器组件
 * 
 * 基于原型设计：UI Prototype/02-视频播放页.html 视频控制器区域
 * 
 * 功能特性：
 * - 实现播放/暂停控制
 * - 支持进度条拖拽和点击跳转
 * - 提供倍速播放选择
 * - 支持全屏切换功能
 * - 音量控制和静音功能
 * - 时间显示和格式化
 * - 使用原型的渐变背景设计
 */
@Composable
fun VideoControls(
    controlsData: VideoControlsData,
    onPlayPause: () -> Unit = {},
    onSeek: (Long) -> Unit = {},
    onSpeedChange: (PlaybackSpeed) -> Unit = {},
    onFullscreenToggle: () -> Unit = {},
    onVolumeChange: (Float) -> Unit = {},
    onMuteToggle: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    var showSpeedSelector by remember { mutableStateOf(controlsData.showSpeedSelector) }
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color.Transparent,
                        Color.Black.copy(alpha = 0.8f)
                    )
                )
            )
            .padding(20.dp, 16.dp)
    ) {
        Column {
            // 进度条区域
            VideoProgressSection(
                currentPosition = controlsData.currentPosition,
                duration = controlsData.duration,
                onSeek = onSeek,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 控制按钮行
            VideoControlButtons(
                controlsData = controlsData,
                onPlayPause = onPlayPause,
                onSpeedToggle = { showSpeedSelector = !showSpeedSelector },
                onFullscreenToggle = onFullscreenToggle,
                onMuteToggle = onMuteToggle,
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        // 播放速度选择器
        if (showSpeedSelector) {
            PlaybackSpeedSelector(
                currentSpeed = controlsData.playbackSpeed,
                onSpeedSelected = { speed ->
                    onSpeedChange(speed)
                    showSpeedSelector = false
                },
                onDismiss = { showSpeedSelector = false },
                modifier = Modifier.align(Alignment.BottomEnd)
            )
        }
    }
}

/**
 * 视频进度条区域
 */
@Composable
private fun VideoProgressSection(
    currentPosition: Long,
    duration: Long,
    onSeek: (Long) -> Unit,
    modifier: Modifier = Modifier
) {
    val progress = if (duration > 0) {
        (currentPosition.toFloat() / duration.toFloat()) * 100f
    } else 0f
    
    Column(modifier = modifier) {
        // 进度条
        ProgressBar(
            progress = progress,
            modifier = Modifier
                .fillMaxWidth()
                .height(4.dp)
                .clickable { /* TODO: 实现点击跳转 */ },
            backgroundColor = Color.White.copy(alpha = 0.3f),
            progressColor = Color(0xFF667EEA)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 时间显示
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = formatTime(currentPosition),
                color = Color.White,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = formatTime(duration),
                color = Color.White.copy(alpha = 0.7f),
                fontSize = 12.sp
            )
        }
    }
}

/**
 * 视频控制按钮行
 */
@Composable
private fun VideoControlButtons(
    controlsData: VideoControlsData,
    onPlayPause: () -> Unit,
    onSpeedToggle: () -> Unit,
    onFullscreenToggle: () -> Unit,
    onMuteToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧控制组
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 播放/暂停按钮
            IconButton(
                onClick = onPlayPause,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    imageVector = if (controlsData.isPlaying) {
                        Icons.Default.Pause
                    } else {
                        Icons.Default.PlayArrow
                    },
                    contentDescription = if (controlsData.isPlaying) "暂停" else "播放",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // 音量控制
            IconButton(
                onClick = onMuteToggle,
                modifier = Modifier.size(36.dp)
            ) {
                Icon(
                    imageVector = if (controlsData.isMuted) {
                        Icons.Default.VolumeOff
                    } else {
                        Icons.Default.VolumeUp
                    },
                    contentDescription = if (controlsData.isMuted) "取消静音" else "静音",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
        
        // 右侧控制组
        Row(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 播放速度按钮
            TextButton(
                onClick = onSpeedToggle,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = Color.White
                ),
                modifier = Modifier.height(32.dp)
            ) {
                Text(
                    text = controlsData.playbackSpeed.label,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
            }
            
            // 全屏按钮
            IconButton(
                onClick = onFullscreenToggle,
                modifier = Modifier.size(36.dp)
            ) {
                Icon(
                    imageVector = if (controlsData.isFullscreen) {
                        Icons.Default.FullscreenExit
                    } else {
                        Icons.Default.Fullscreen
                    },
                    contentDescription = if (controlsData.isFullscreen) "退出全屏" else "全屏",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * 播放速度选择器
 */
@Composable
private fun PlaybackSpeedSelector(
    currentSpeed: PlaybackSpeed,
    onSpeedSelected: (PlaybackSpeed) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(120.dp)
            .offset(y = (-60).dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.9f)
        ),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            PlaybackSpeed.values().forEach { speed ->
                SpeedOptionItem(
                    speed = speed,
                    isSelected = speed == currentSpeed,
                    onClick = { onSpeedSelected(speed) },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * 播放速度选项项
 */
@Composable
private fun SpeedOptionItem(
    speed: PlaybackSpeed,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .clickable { onClick() }
            .padding(12.dp, 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = speed.label,
            color = if (isSelected) Color(0xFF667EEA) else Color.White,
            fontSize = 14.sp,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal
        )
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "已选择",
                tint = Color(0xFF667EEA),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 格式化时间显示
 */
private fun formatTime(timeMs: Long): String {
    val totalSeconds = timeMs / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60
    
    return if (hours > 0) {
        String.format("%02d:%02d:%02d", hours, minutes, seconds)
    } else {
        String.format("%02d:%02d", minutes, seconds)
    }
} 