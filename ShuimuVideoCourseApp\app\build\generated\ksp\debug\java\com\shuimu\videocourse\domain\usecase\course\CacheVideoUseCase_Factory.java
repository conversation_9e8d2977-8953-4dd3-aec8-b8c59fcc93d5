package com.shuimu.videocourse.domain.usecase.course;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CacheVideoUseCase_Factory implements Factory<CacheVideoUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public CacheVideoUseCase_Factory(Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public CacheVideoUseCase get() {
    return newInstance(courseRepositoryProvider.get());
  }

  public static CacheVideoUseCase_Factory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new CacheVideoUseCase_Factory(courseRepositoryProvider);
  }

  public static CacheVideoUseCase newInstance(CourseRepository courseRepository) {
    return new CacheVideoUseCase(courseRepository);
  }
}
