package com.shuimu.videocourse.presentation.components.user

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest

/**
 * 用户头像组件
 * 
 * 基于原型设计：UI Prototype/03-我的页面.html 用户头像
 * - 支持圆形头像容器和默认头像
 * - 实现尺寸变体（48dp、40dp、32dp）
 * - 添加边框和阴影效果
 * - 支持网络图片加载和默认占位符
 * 
 * @param avatarUrl 头像图片URL（可选）
 * @param size 头像尺寸
 * @param showBorder 是否显示边框
 * @param modifier 修饰符
 */
@Composable
fun UserAvatar(
    avatarUrl: String? = null,
    size: Size = Size.Medium,
    showBorder: Boolean = true,
    modifier: Modifier = Modifier
) {
    val avatarSize = when (size) {
        Size.Small -> 32.dp
        Size.Medium -> 40.dp
        Size.Large -> 48.dp
    }
    
    val borderWidth = if (showBorder) 2.dp else 0.dp
    val shadowElevation = when (size) {
        Size.Small -> 2.dp
        Size.Medium -> 4.dp
        Size.Large -> 6.dp
    }
    
    Box(
        modifier = modifier
            .size(avatarSize)
            .shadow(
                elevation = shadowElevation,
                shape = CircleShape
            )
            .clip(CircleShape)
            .background(Color.White)
            .then(
                if (showBorder) {
                    Modifier.border(
                        width = borderWidth,
                        color = Color(0xFFe5e7eb),
                        shape = CircleShape
                    )
                } else Modifier
            ),
        contentAlignment = Alignment.Center
    ) {
        if (!avatarUrl.isNullOrEmpty()) {
            // 网络头像
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(avatarUrl)
                    .crossfade(true)
                    .build(),
                contentDescription = "用户头像",
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape),
                contentScale = ContentScale.Crop,
                fallback = {
                    DefaultAvatarContent(size = size)
                },
                error = {
                    DefaultAvatarContent(size = size)
                }
            )
        } else {
            // 默认头像
            DefaultAvatarContent(size = size)
        }
    }
}

/**
 * 默认头像内容
 */
@Composable
private fun DefaultAvatarContent(
    size: Size
) {
    val iconSize = when (size) {
        Size.Small -> 16.dp
        Size.Medium -> 20.dp
        Size.Large -> 24.dp
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                color = Color(0xFF667eea).copy(alpha = 0.1f),
                shape = CircleShape
            ),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = Icons.Default.Person,
            contentDescription = "默认头像",
            modifier = Modifier.size(iconSize),
            tint = Color(0xFF667eea)
        )
    }
}

/**
 * 头像尺寸枚举
 */
enum class Size {
    Small,   // 32dp
    Medium,  // 40dp
    Large    // 48dp
} 