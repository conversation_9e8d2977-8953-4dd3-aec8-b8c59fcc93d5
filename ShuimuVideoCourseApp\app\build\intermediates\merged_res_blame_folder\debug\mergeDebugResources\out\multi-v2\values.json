{"logs": [{"outputFile": "com.shuimu.videocourse.app-mergeDebugResources-69:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\174f010f9decb921b32b6ae1840ff9e1\\transformed\\media3-ui-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,35,40,47,48,49,50,51,52,57,58,59,60,61,62,63,64,65,66,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,213,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,266,270,274,278,282,286,290,294,295,301,312,316,320,324,328,332,336,340,344,348,352,356,367,372,377,382,393,401,411,415,419,423,426,442,468,497", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1140,1190,1255,1312,1359,1414,1562,1800,1849,1910,1970,2026,2086,2256,2316,2369,2426,2481,2537,2594,2643,2694,2753,3040,3105,3163,3212,3260,3311,3368,3425,3487,3554,3625,3697,3741,3798,3854,3917,3990,4060,4119,4176,4223,4278,4323,4372,4427,4481,4531,4582,4636,4695,4745,4803,4859,4912,4975,5040,5103,5155,5215,5279,5345,5403,5475,5536,5606,5676,5741,5806,5877,5972,6077,6180,6261,6344,6425,6514,6607,6700,6793,6878,6973,7066,7143,7235,7313,7393,7471,7557,7639,7732,7810,7901,7982,8071,8174,8275,8359,8455,8552,8647,8740,8832,8925,9018,9111,9194,9281,9376,9469,9550,9645,9738,9815,9859,9900,9945,9993,10037,10080,10129,10176,10220,10276,10329,10371,10418,10466,10526,10564,10614,10658,10708,10760,10798,10845,10892,10933,10972,11010,11054,11102,11144,11182,11224,11278,11325,11362,11411,11453,11494,11535,11577,11620,11658,11694,11772,11850,12147,12417,12499,12581,12723,12801,12888,12973,13040,13103,13195,13287,13352,13415,13477,13548,13658,13769,13879,13946,14026,14097,14164,14249,14334,14397,14485,14549,14691,14791,14839,14982,15045,15107,15172,15243,15301,15359,15425,15489,15555,15607,15669,15745,15821,15875,16154,16385,16595,16808,17018,17240,17456,17660,17698,18052,18839,19080,19320,19577,19830,20083,20318,20565,20804,21048,21269,21464,22039,22330,22626,22929,23498,24032,24506,24717,24917,25093,25201,25777,26722,27772", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,34,39,46,47,48,49,50,51,56,57,58,59,60,61,62,63,64,65,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,212,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,265,269,273,277,281,285,289,293,294,300,311,315,319,323,327,331,335,339,343,347,351,355,366,371,376,381,392,400,410,414,418,422,425,441,467,496,536", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1135,1185,1250,1307,1354,1409,1557,1795,1844,1905,1965,2021,2081,2251,2311,2364,2421,2476,2532,2589,2638,2689,2748,3035,3100,3158,3207,3255,3306,3363,3420,3482,3549,3620,3692,3736,3793,3849,3912,3985,4055,4114,4171,4218,4273,4318,4367,4422,4476,4526,4577,4631,4690,4740,4798,4854,4907,4970,5035,5098,5150,5210,5274,5340,5398,5470,5531,5601,5671,5736,5801,5872,5967,6072,6175,6256,6339,6420,6509,6602,6695,6788,6873,6968,7061,7138,7230,7308,7388,7466,7552,7634,7727,7805,7896,7977,8066,8169,8270,8354,8450,8547,8642,8735,8827,8920,9013,9106,9189,9276,9371,9464,9545,9640,9733,9810,9854,9895,9940,9988,10032,10075,10124,10171,10215,10271,10324,10366,10413,10461,10521,10559,10609,10653,10703,10755,10793,10840,10887,10928,10967,11005,11049,11097,11139,11177,11219,11273,11320,11357,11406,11448,11489,11530,11572,11615,11653,11689,11767,11845,12142,12412,12494,12576,12718,12796,12883,12968,13035,13098,13190,13282,13347,13410,13472,13543,13653,13764,13874,13941,14021,14092,14159,14244,14329,14392,14480,14544,14686,14786,14834,14977,15040,15102,15167,15238,15296,15354,15420,15484,15550,15602,15664,15740,15816,15870,16149,16380,16590,16803,17013,17235,17451,17655,17693,18047,18834,19075,19315,19572,19825,20078,20313,20560,20799,21043,21264,21459,22034,22325,22621,22924,23493,24027,24501,24712,24912,25088,25196,25772,26717,27767,29125"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,31,32,34,36,37,38,40,45,52,53,54,55,56,57,62,63,64,65,66,67,68,69,70,71,78,80,81,82,83,126,127,128,129,130,131,132,133,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,522,523,525,529,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,1800,1805,1809,1813,1817,1821,1825,1829,1833,1834,1840,1851,1855,1859,1863,1867,1871,1875,1879,1883,1887,1891,1895,1906,1911,1916,1921,1932,1940,1950,1954,1958,3061,3140,3276,3542,3571", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1233,1285,1384,1509,1566,1613,1724,1872,2110,2159,2220,2280,2336,2396,2566,2626,2679,2736,2791,2847,2904,2953,3004,3063,3350,3456,3514,3563,3611,6468,6525,6582,6644,6711,6782,6854,6898,16383,16439,16502,16575,16645,16704,16761,16808,16863,16908,16957,17012,17066,17116,17167,17221,17280,17330,17388,17444,17497,17560,17625,17688,17740,17800,17864,17930,17988,18060,18121,18191,18261,18326,18391,20890,20985,21090,21193,21274,21357,21438,21527,21620,21713,21806,21891,21986,22079,22156,22248,22326,22406,22484,22570,22652,22745,22823,22914,22995,23084,23187,23288,23372,23468,23565,23660,23753,23845,23938,24031,24124,24207,24294,24389,24482,24563,24658,24751,27393,27437,27478,27523,27571,27615,27658,27707,27754,27798,27854,27907,27949,27996,28044,28104,28142,28192,28236,28286,28338,28376,28423,28470,28511,28550,28588,28632,28680,28722,28760,28802,28856,28903,28940,28989,29031,29072,29113,29155,29198,29236,31548,31626,31774,32071,38190,38272,38354,38496,38574,38661,38746,38813,38876,38968,39060,39125,39188,39250,39321,39431,39542,39652,39719,39799,39870,39937,40022,40107,40170,40258,40968,41110,41210,41258,41401,41464,41526,41591,41662,41720,41778,41844,41908,41974,42026,42088,42164,42240,114058,114337,114568,114778,114991,115201,115423,115639,115843,115881,116235,117022,117263,117503,117760,118013,118266,118501,118748,118987,119231,119452,119647,120222,120513,120809,121112,121681,122215,122689,122900,123100,178821,182295,187023,196119,197169", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,31,32,34,36,37,38,44,51,52,53,54,55,56,61,62,63,64,65,66,67,68,69,70,77,78,80,81,82,83,126,127,128,129,130,131,132,133,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,522,523,528,532,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,1804,1808,1812,1816,1820,1824,1828,1832,1833,1839,1850,1854,1858,1862,1866,1870,1874,1878,1882,1886,1890,1894,1905,1910,1915,1920,1931,1939,1949,1953,1957,1961,3063,3155,3301,3570,3610", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1280,1330,1444,1561,1608,1663,1867,2105,2154,2215,2275,2331,2391,2561,2621,2674,2731,2786,2842,2899,2948,2999,3058,3345,3410,3509,3558,3606,3657,6520,6577,6639,6706,6777,6849,6893,6950,16434,16497,16570,16640,16699,16756,16803,16858,16903,16952,17007,17061,17111,17162,17216,17275,17325,17383,17439,17492,17555,17620,17683,17735,17795,17859,17925,17983,18055,18116,18186,18256,18321,18386,18457,20980,21085,21188,21269,21352,21433,21522,21615,21708,21801,21886,21981,22074,22151,22243,22321,22401,22479,22565,22647,22740,22818,22909,22990,23079,23182,23283,23367,23463,23560,23655,23748,23840,23933,24026,24119,24202,24289,24384,24477,24558,24653,24746,24823,27432,27473,27518,27566,27610,27653,27702,27749,27793,27849,27902,27944,27991,28039,28099,28137,28187,28231,28281,28333,28371,28418,28465,28506,28545,28583,28627,28675,28717,28755,28797,28851,28898,28935,28984,29026,29067,29108,29150,29193,29231,29267,31621,31699,32066,32336,38267,38349,38491,38569,38656,38741,38808,38871,38963,39055,39120,39183,39245,39316,39426,39537,39647,39714,39794,39865,39932,40017,40102,40165,40253,40317,41105,41205,41253,41396,41459,41521,41586,41657,41715,41773,41839,41903,41969,42021,42083,42159,42235,42289,114332,114563,114773,114986,115196,115418,115634,115838,115876,116230,117017,117258,117498,117755,118008,118261,118496,118743,118982,119226,119447,119642,120217,120508,120804,121107,121676,122210,122684,122895,123095,123271,178924,182866,187963,197164,198522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04ef683a139c188ec55a27e1cc776c0d\\transformed\\media-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,5,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,288,410,476,598,659,725", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "139,345,471,593,654,720,787"}, "to": {"startLines": "153,487,2245,2247,2248,2253,2255", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8080,29659,144786,144962,145084,145346,145541", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "8164,29716,144847,145079,145140,145407,145603"}}, {"source": "D:\\01-shuimu_01\\ShuimuVideoCourseApp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "33,23,24,25,26,27,28,29,30,7,19,20,35,36,11,12,2,3,4,13,14,17,34,5,6,18,8", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1292,853,904,955,1006,1057,1108,1159,1212,293,741,784,1385,1436,427,472,57,105,153,525,572,651,1340,201,247,696,336", "endColumns": "46,49,49,49,49,49,49,51,52,41,41,40,49,46,43,51,46,46,46,45,53,43,43,44,44,43,41", "endOffsets": "1334,898,949,1000,1051,1102,1153,1206,1260,330,778,820,1430,1478,466,519,99,147,195,566,621,690,1379,241,287,735,373"}, "to": {"startLines": "95,100,101,102,103,104,105,106,107,108,123,138,154,155,156,165,166,167,168,171,176,177,178,183,184,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4440,4806,4856,4906,4956,5006,5056,5106,5158,5211,6305,7235,8169,8219,8266,8898,8950,8997,9044,9206,9548,9602,9646,9976,10021,10187,10231", "endColumns": "46,49,49,49,49,49,49,51,52,41,41,40,49,46,43,51,46,46,46,45,53,43,43,44,44,43,41", "endOffsets": "4482,4851,4901,4951,5001,5051,5101,5153,5206,5248,6342,7271,8214,8261,8305,8945,8992,9039,9086,9247,9597,9641,9685,10016,10061,10226,10268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f7f8ee867ea51c7e078a027f9a0aea05\\transformed\\navigation-runtime-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "488,2517,3531,3534", "startColumns": "4,4,4,4", "startOffsets": "29721,161046,195707,195822", "endLines": "488,2523,3533,3536", "endColumns": "52,24,24,24", "endOffsets": "29769,161345,195817,195932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\792cb359f5ecd5e2d42aebd303e10fd2\\transformed\\activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "492,513", "startColumns": "4,4", "startOffsets": "29937,31024", "endColumns": "41,59", "endOffsets": "29974,31079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b99ce37de41192ac66063c64114bdfa7\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "483,489", "startColumns": "4,4", "startOffsets": "29469,29774", "endColumns": "53,66", "endOffsets": "29518,29836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72373185c1e14d184691335e7ab6a10e\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "479,493,516,3228,3233", "startColumns": "4,4,4,4,4", "startOffsets": "29272,29979,31188,185855,186025", "endLines": "479,493,516,3232,3236", "endColumns": "56,64,63,24,24", "endOffsets": "29324,30039,31247,186020,186169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bba47f6f1c22dc6cef6d31c25681185a\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "434", "startColumns": "4", "startOffsets": "27230", "endColumns": "49", "endOffsets": "27275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\982a3d5b37c26b685d4cf4e2e5ebc532\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2534,2550,2556,3695,3711", "startColumns": "4,4,4,4,4", "startOffsets": "161867,162292,162470,201357,201768", "endLines": "2549,2555,2565,3710,3714", "endColumns": "24,24,24,24,24", "endOffsets": "162287,162465,162749,201763,201890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96269eac36e54192709622584090080f\\transformed\\material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "562,563,564,565,576,577,578,579,580,581,584,585,586,587,588,589,590,591,592,593,594,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,614,669,675,683,687,689,693,694,695,696,697,698,699,700,701,702,703,704,705,706", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "34334,34418,34500,34577,35337,35385,35446,35525,35627,35709,35825,35875,35940,35997,36062,36147,36238,36308,36401,36490,36584,36729,36816,36900,36992,37086,37146,37210,37293,37383,37446,37514,37582,37679,37784,37856,38088,42294,42565,42966,43158,43282,43523,43569,43619,43686,43753,43819,43884,43938,44010,44077,44147,44229,44275,44341", "endLines": "562,563,564,565,576,577,578,579,580,583,584,585,586,587,588,589,590,591,592,593,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,614,669,675,683,687,689,693,694,695,696,697,698,699,700,701,702,703,704,705,706", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "34413,34495,34572,34652,35380,35441,35520,35622,35704,35820,35870,35935,35992,36057,36142,36233,36303,36396,36485,36579,36724,36811,36895,36987,37081,37141,37205,37288,37378,37441,37509,37577,37674,37779,37851,37916,38127,42335,42629,43014,43206,43345,43564,43614,43681,43748,43814,43879,43933,44005,44072,44142,44224,44270,44336,44397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8255166624e0f5c0f0935b39c77f43b7\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "29,30,33,79,84,85,86,87,88,89,90,91,92,96,97,98,99,109,110,111,112,113,114,115,116,119,120,121,122,124,125,134,135,136,137,139,140,141,142,143,144,145,146,147,148,149,150,157,158,159,160,161,162,163,164,169,170,172,173,174,175,179,180,181,182,185,186,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,277,278,317,318,319,320,321,322,323,342,343,344,345,346,347,348,349,429,430,431,432,481,490,491,494,511,518,519,520,521,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,684,709,710,711,712,713,714,722,723,727,731,735,740,746,753,757,761,766,770,774,778,782,786,790,796,800,806,810,816,820,825,829,832,836,842,846,852,856,862,865,869,873,877,881,885,886,887,888,891,894,897,900,904,905,906,907,908,911,913,915,917,922,923,927,933,937,938,940,952,953,957,963,967,968,969,973,1000,1004,1005,1009,1037,1209,1235,1406,1432,1463,1471,1477,1493,1515,1520,1525,1535,1544,1553,1557,1564,1583,1590,1591,1600,1603,1606,1610,1614,1618,1621,1622,1627,1632,1642,1647,1654,1660,1661,1664,1668,1673,1675,1677,1680,1683,1685,1689,1692,1699,1702,1705,1709,1711,1715,1717,1719,1721,1725,1733,1741,1753,1759,1768,1771,1782,1785,1786,1791,1792,1969,2038,2108,2109,2119,2128,2129,2131,2135,2138,2141,2144,2147,2150,2153,2156,2160,2163,2166,2169,2173,2176,2180,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2206,2208,2209,2210,2211,2212,2213,2214,2215,2217,2218,2220,2221,2223,2225,2226,2228,2229,2230,2231,2232,2233,2235,2236,2237,2238,2239,2256,2258,2260,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2276,2277,2278,2279,2280,2281,2282,2284,2288,2304,2305,2306,2307,2308,2309,2313,2314,2315,2316,2318,2320,2322,2324,2326,2327,2328,2329,2331,2333,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2349,2350,2351,2352,2354,2356,2357,2359,2360,2362,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2379,2380,2381,2382,2384,2385,2386,2387,2388,2390,2392,2394,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2413,2488,2491,2494,2497,2511,2524,2566,2569,2598,2625,2634,2698,3064,3074,3112,3156,3302,3326,3332,3338,3359,3483,3611,3617,3621,3648,3683,3715,3781,3801,3856,3868,3894", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1133,1188,1335,3415,3662,3717,3779,3843,3913,3974,4049,4125,4202,4487,4572,4654,4730,5253,5330,5408,5514,5620,5699,5779,5836,6025,6099,6174,6239,6347,6407,6955,7027,7100,7167,7276,7335,7394,7453,7512,7571,7625,7679,7732,7786,7840,7894,8310,8384,8463,8536,8610,8681,8753,8825,9091,9148,9252,9325,9399,9473,9690,9762,9835,9905,10066,10126,10273,10342,10411,10481,10555,10631,10695,10772,10848,10925,10990,11059,11136,11211,11280,11348,11425,11491,11552,11649,11714,11783,11882,11953,12012,12070,12127,12186,12250,12321,12393,12465,12537,12609,12676,12744,12812,12871,12934,12998,13088,13179,13239,13305,13372,13438,13508,13572,13625,13692,13753,13820,13933,13991,14054,14119,14184,14259,14332,14404,14448,14495,14541,14590,14651,14712,14773,14835,14899,14963,15027,15092,15155,15215,15276,15342,15401,15461,15523,15594,15654,16210,16296,18625,18715,18802,18890,18972,19055,19145,20437,20489,20547,20592,20658,20722,20779,20836,26951,27008,27056,27105,29380,29841,29888,30044,30949,31305,31369,31431,31491,32341,32415,32485,32563,32617,32687,32772,32820,32866,32927,32990,33056,33120,33191,33254,33319,33383,33444,33505,33557,33630,33704,33773,33848,33922,33996,34137,43019,44506,44584,44674,44762,44858,44948,45530,45619,45866,46147,46399,46684,47077,47554,47776,47998,48274,48501,48731,48961,49191,49421,49648,50067,50293,50718,50948,51376,51595,51878,52086,52217,52444,52870,53095,53522,53743,54168,54288,54564,54865,55189,55480,55794,55931,56062,56167,56409,56576,56780,56988,57259,57371,57483,57588,57705,57919,58065,58205,58291,58639,58727,58973,59391,59640,59722,59820,60477,60577,60829,61253,61508,61602,61691,61928,63952,64194,64296,64549,66705,77386,78902,89597,91125,92882,93508,93928,95189,96454,96710,96946,97493,97987,98592,98790,99370,100738,101113,101231,101769,101926,102122,102395,102651,102821,102962,103026,103391,103758,104434,104698,105036,105389,105483,105669,105975,106237,106362,106489,106728,106939,107058,107251,107428,107883,108064,108186,108445,108558,108745,108847,108954,109083,109358,109866,110362,111239,111533,112103,112252,112984,113156,113240,113576,113668,123582,128813,134184,134246,134824,135408,135499,135612,135841,136001,136153,136324,136490,136659,136826,136989,137232,137402,137575,137746,138020,138219,138424,138754,138838,138934,139030,139128,139228,139330,139432,139534,139636,139738,139838,139934,140046,140175,140298,140429,140560,140658,140772,140866,141006,141140,141236,141348,141448,141564,141660,141772,141872,142012,142148,142312,142442,142600,142750,142891,143035,143170,143282,143432,143560,143688,143824,143956,144086,144216,144328,145608,145754,145898,146036,146102,146192,146268,146372,146462,146564,146672,146780,146880,146960,147052,147150,147260,147312,147390,147496,147588,147692,147802,147924,148087,148725,148805,148905,148995,149105,149195,149436,149530,149636,149728,149828,149940,150054,150170,150286,150380,150494,150606,150708,150828,150950,151032,151136,151256,151382,151480,151574,151662,151774,151890,152012,152124,152299,152415,152501,152593,152705,152829,152896,153022,153090,153218,153362,153490,153559,153654,153769,153882,153981,154090,154201,154312,154413,154518,154618,154748,154839,154962,155056,155168,155254,155358,155454,155542,155660,155764,155868,155994,156082,156190,156290,156380,156490,156574,156676,156760,156814,156878,156984,157070,157180,157264,157523,160139,160257,160372,160452,160813,161350,162754,162832,164176,165537,165925,168768,178929,179267,180938,182871,187968,188719,188981,189181,189560,193838,198527,198756,198907,199962,201045,201895,204921,205665,207796,208136,209447", "endLines": "29,30,33,79,84,85,86,87,88,89,90,91,92,96,97,98,99,109,110,111,112,113,114,115,116,119,120,121,122,124,125,134,135,136,137,139,140,141,142,143,144,145,146,147,148,149,150,157,158,159,160,161,162,163,164,169,170,172,173,174,175,179,180,181,182,185,186,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,277,278,317,318,319,320,321,322,323,342,343,344,345,346,347,348,349,429,430,431,432,481,490,491,494,511,518,519,520,521,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,684,709,710,711,712,713,721,722,726,730,734,739,745,752,756,760,765,769,773,777,781,785,789,795,799,805,809,815,819,824,828,831,835,841,845,851,855,861,864,868,872,876,880,884,885,886,887,890,893,896,899,903,904,905,906,907,910,912,914,916,921,922,926,932,936,937,939,951,952,956,962,966,967,968,972,999,1003,1004,1008,1036,1208,1234,1405,1431,1462,1470,1476,1492,1514,1519,1524,1534,1543,1552,1556,1563,1582,1589,1590,1599,1602,1605,1609,1613,1617,1620,1621,1626,1631,1641,1646,1653,1659,1660,1663,1667,1672,1674,1676,1679,1682,1684,1688,1691,1698,1701,1704,1708,1710,1714,1716,1718,1720,1724,1732,1740,1752,1758,1767,1770,1781,1784,1785,1790,1791,1796,2037,2107,2108,2118,2127,2128,2130,2134,2137,2140,2143,2146,2149,2152,2155,2159,2162,2165,2168,2172,2175,2179,2183,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2205,2207,2208,2209,2210,2211,2212,2213,2214,2216,2217,2219,2220,2222,2224,2225,2227,2228,2229,2230,2231,2232,2234,2235,2236,2237,2238,2239,2257,2259,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2275,2276,2277,2278,2279,2280,2281,2283,2287,2291,2304,2305,2306,2307,2308,2312,2313,2314,2315,2317,2319,2321,2323,2325,2326,2327,2328,2330,2332,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2348,2349,2350,2351,2353,2355,2356,2358,2359,2361,2363,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2378,2379,2380,2381,2383,2384,2385,2386,2387,2389,2391,2393,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2487,2490,2493,2496,2510,2516,2533,2568,2597,2624,2633,2697,3060,3067,3101,3139,3173,3325,3331,3337,3358,3482,3502,3616,3620,3626,3682,3694,3780,3800,3855,3867,3893,3900", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1183,1228,1379,3451,3712,3774,3838,3908,3969,4044,4120,4197,4275,4567,4649,4725,4801,5325,5403,5509,5615,5694,5774,5831,5889,6094,6169,6234,6300,6402,6463,7022,7095,7162,7230,7330,7389,7448,7507,7566,7620,7674,7727,7781,7835,7889,7943,8379,8458,8531,8605,8676,8748,8820,8893,9143,9201,9320,9394,9468,9543,9757,9830,9900,9971,10121,10182,10337,10406,10476,10550,10626,10690,10767,10843,10920,10985,11054,11131,11206,11275,11343,11420,11486,11547,11644,11709,11778,11877,11948,12007,12065,12122,12181,12245,12316,12388,12460,12532,12604,12671,12739,12807,12866,12929,12993,13083,13174,13234,13300,13367,13433,13503,13567,13620,13687,13748,13815,13928,13986,14049,14114,14179,14254,14327,14399,14443,14490,14536,14585,14646,14707,14768,14830,14894,14958,15022,15087,15150,15210,15271,15337,15396,15456,15518,15589,15649,15717,16291,16378,18710,18797,18885,18967,19050,19140,19231,20484,20542,20587,20653,20717,20774,20831,20885,27003,27051,27100,27151,29409,29883,29932,30085,30976,31364,31426,31486,31543,32410,32480,32558,32612,32682,32767,32815,32861,32922,32985,33051,33115,33186,33249,33314,33378,33439,33500,33552,33625,33699,33768,33843,33917,33991,34132,34202,43067,44579,44669,44757,44853,44943,45525,45614,45861,46142,46394,46679,47072,47549,47771,47993,48269,48496,48726,48956,49186,49416,49643,50062,50288,50713,50943,51371,51590,51873,52081,52212,52439,52865,53090,53517,53738,54163,54283,54559,54860,55184,55475,55789,55926,56057,56162,56404,56571,56775,56983,57254,57366,57478,57583,57700,57914,58060,58200,58286,58634,58722,58968,59386,59635,59717,59815,60472,60572,60824,61248,61503,61597,61686,61923,63947,64189,64291,64544,66700,77381,78897,89592,91120,92877,93503,93923,95184,96449,96705,96941,97488,97982,98587,98785,99365,100733,101108,101226,101764,101921,102117,102390,102646,102816,102957,103021,103386,103753,104429,104693,105031,105384,105478,105664,105970,106232,106357,106484,106723,106934,107053,107246,107423,107878,108059,108181,108440,108553,108740,108842,108949,109078,109353,109861,110357,111234,111528,112098,112247,112979,113151,113235,113571,113663,113941,128808,134179,134241,134819,135403,135494,135607,135836,135996,136148,136319,136485,136654,136821,136984,137227,137397,137570,137741,138015,138214,138419,138749,138833,138929,139025,139123,139223,139325,139427,139529,139631,139733,139833,139929,140041,140170,140293,140424,140555,140653,140767,140861,141001,141135,141231,141343,141443,141559,141655,141767,141867,142007,142143,142307,142437,142595,142745,142886,143030,143165,143277,143427,143555,143683,143819,143951,144081,144211,144323,144463,145749,145893,146031,146097,146187,146263,146367,146457,146559,146667,146775,146875,146955,147047,147145,147255,147307,147385,147491,147583,147687,147797,147919,148082,148239,148800,148900,148990,149100,149190,149431,149525,149631,149723,149823,149935,150049,150165,150281,150375,150489,150601,150703,150823,150945,151027,151131,151251,151377,151475,151569,151657,151769,151885,152007,152119,152294,152410,152496,152588,152700,152824,152891,153017,153085,153213,153357,153485,153554,153649,153764,153877,153976,154085,154196,154307,154408,154513,154613,154743,154834,154957,155051,155163,155249,155353,155449,155537,155655,155759,155863,155989,156077,156185,156285,156375,156485,156569,156671,156755,156809,156873,156979,157065,157175,157259,157379,160134,160252,160367,160447,160808,161041,161862,162827,164171,165532,165920,168763,178816,179059,180632,182290,183438,188714,188976,189176,189555,193833,194439,198751,198902,199117,201040,201352,204916,205660,207791,208131,209442,209645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb11a69ad02dfbc6b3d00a9c1881012d\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "433,435,436,480,482,517,574,575,612,613,615,671,672,677,678,679,680,681,682,685,690,691,692,1797,1962,1965", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27156,27280,27338,29329,29414,31252,35218,35283,37921,37987,38132,42376,42428,42676,42738,42792,42828,42862,42912,43072,43350,43397,43433,113946,123276,123387", "endLines": "433,435,436,480,482,517,574,575,612,613,615,671,672,677,678,679,680,681,682,685,690,691,692,1799,1964,1968", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "27225,27333,27388,29375,29464,31300,35278,35332,37982,38083,38185,42423,42483,42733,42787,42823,42857,42907,42961,43113,43392,43428,43518,114053,123382,123577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bffc5a9dece2075931683201af2cd00f\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "560", "startColumns": "4", "startOffsets": "34207", "endColumns": "82", "endOffsets": "34285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b4dfaf8f521cdef86a5e180be948ba\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "35,93,94,117,118,151,152,270,271,272,273,274,275,276,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,485,486,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,524,567,568,569,570,571,572,573,688,2240,2241,2246,2249,2254,2411,2412,3068,3102,3174,3207,3237,3270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1449,4280,4352,5894,5959,7948,8017,15722,15792,15860,15932,16002,16063,16137,19459,19520,19581,19643,19707,19769,19830,19898,19998,20058,20124,20197,20266,20323,20375,24828,24900,24976,25041,25100,25159,25219,25279,25339,25399,25459,25519,25579,25639,25699,25759,25818,25878,25938,25998,26058,26118,26178,26238,26298,26358,26418,26477,26537,26597,26656,26715,26774,26833,26892,29589,29624,30090,30145,30208,30263,30321,30379,30440,30503,30560,30611,30661,30722,30779,30845,30879,30914,31704,34707,34774,34846,34915,34984,35058,35130,43211,144468,144585,144852,145145,145412,157384,157456,179064,180637,183443,185174,186174,186856", "endLines": "35,93,94,117,118,151,152,270,271,272,273,274,275,276,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,485,486,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,524,567,568,569,570,571,572,573,688,2240,2244,2246,2252,2254,2411,2412,3073,3111,3206,3227,3269,3275", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1504,4347,4435,5954,6020,8012,8075,15787,15855,15927,15997,16058,16132,16205,19515,19576,19638,19702,19764,19825,19893,19993,20053,20119,20192,20261,20318,20370,20432,24895,24971,25036,25095,25154,25214,25274,25334,25394,25454,25514,25574,25634,25694,25754,25813,25873,25933,25993,26053,26113,26173,26233,26293,26353,26413,26472,26532,26592,26651,26710,26769,26828,26887,26946,29619,29654,30140,30203,30258,30316,30374,30435,30498,30555,30606,30656,30717,30774,30840,30874,30909,30944,31769,34769,34841,34910,34979,35053,35125,35213,43277,144580,144781,144957,145341,145536,157451,157518,179262,180933,185169,185850,186851,187018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51217fba78aae74607c1afa7de4307cd\\transformed\\media3-exoplayer-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "642,643,644,645,646,647,648,649,650", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "40322,40392,40454,40519,40583,40660,40725,40815,40899", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "40387,40449,40514,40578,40655,40720,40810,40894,40963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b985255ad6db5cd24a6a1552c4bb9ecc\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "512", "startColumns": "4", "startOffsets": "30981", "endColumns": "42", "endOffsets": "31019"}}, {"source": "D:\\01-shuimu_01\\ShuimuVideoCourseApp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "142", "endLines": "14", "endColumns": "12", "endOffsets": "720"}, "to": {"startLines": "2292", "startColumns": "4", "startOffsets": "148244", "endLines": "2303", "endColumns": "12", "endOffsets": "148720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3b59b431625c9d51b67b5ea5642d0cae\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "39,314,315,316,324,325,326,484,3627", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1668,18462,18521,18569,19236,19311,19387,29523,199122", "endLines": "39,314,315,316,324,325,326,484,3647", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "1719,18516,18564,18620,19306,19382,19454,29584,199957"}}, {"source": "D:\\01-shuimu_01\\ShuimuVideoCourseApp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,9,6,4,5,7,10,8,3", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "57,367,240,161,199,277,418,320,102", "endColumns": "43,49,35,36,39,41,39,45,57", "endOffsets": "96,412,271,193,234,314,453,361,155"}, "to": {"startLines": "561,566,670,673,674,676,686,707,708", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "34290,34657,42340,42488,42525,42634,43118,44402,44448", "endColumns": "43,49,35,36,39,41,39,45,57", "endOffsets": "34329,34702,42371,42520,42560,42671,43153,44443,44501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0ca5ab744628cbfe9194aa0be91293ac\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "514", "startColumns": "4", "startOffsets": "31084", "endColumns": "53", "endOffsets": "31133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\50d778d6a8e171c743d44686ee6dd401\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "515", "startColumns": "4", "startOffsets": "31138", "endColumns": "49", "endOffsets": "31183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ffd1588ed6aea344175281e9979fc139\\transformed\\navigation-common-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3503,3516,3522,3528,3537", "startColumns": "4,4,4,4,4", "startOffsets": "194444,195083,195327,195574,195937", "endLines": "3515,3521,3527,3530,3541", "endColumns": "24,24,24,24,24", "endOffsets": "195078,195322,195569,195702,196114"}}]}]}