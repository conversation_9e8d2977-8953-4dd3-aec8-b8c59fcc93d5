package com.shuimu.videocourse.domain.usecase.course;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetCoursesUseCase_Factory implements Factory<GetCoursesUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public GetCoursesUseCase_Factory(Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public GetCoursesUseCase get() {
    return newInstance(courseRepositoryProvider.get());
  }

  public static GetCoursesUseCase_Factory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new GetCoursesUseCase_Factory(courseRepositoryProvider);
  }

  public static GetCoursesUseCase newInstance(CourseRepository courseRepository) {
    return new GetCoursesUseCase(courseRepository);
  }
}
