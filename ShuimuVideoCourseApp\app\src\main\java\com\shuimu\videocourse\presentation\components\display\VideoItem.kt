package com.shuimu.videocourse.presentation.components.display

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.basic.BadgeComponent
import com.shuimu.videocourse.presentation.components.basic.BadgeStyle
import com.shuimu.videocourse.presentation.components.basic.LinearProgressBar
import com.shuimu.videocourse.presentation.components.basic.ProgressBarSize
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 视频项组件
 * 
 * 基于原型设计：UI Prototype/data/component-templates-with-styles.js 第440-490行
 * createPurchasedVideoItem 和 createUnpurchasedVideoItem 函数
 * 
 * 支持功能：
 * - 已购买状态：显示进度条、观看次数徽章、缓存状态
 * - 未购买状态：显示锁定图标
 * - 最后观看标记：特殊背景色
 * - 悬停动画效果
 * - 播放图标动画
 */
@Composable
fun VideoItem(
    title: String,
    category: String = "",
    progress: Float = 0f, // 0-100
    watchCount: Int = 0,
    cacheStatus: String = "", // 空字符串表示未缓存，"已缓存"表示已缓存，"50%"表示下载中
    isPurchased: Boolean = true,
    isLastWatched: Boolean = false,
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    var isHovered by remember { mutableStateOf(false) }
    
    // 悬停动画
    val hoverScale by animateFloatAsState(
        targetValue = if (isHovered) 1.02f else 1f,
        animationSpec = tween(300)
    )
    
    val hoverTranslationX by animateFloatAsState(
        targetValue = if (isHovered) 4f else 0f,
        animationSpec = tween(300)
    )
    
    // 播放图标动画
    val playIconScale by animateFloatAsState(
        targetValue = if (isHovered) 1.1f else 1f,
        animationSpec = tween(300)
    )

    // 背景色：最后观看的视频有特殊背景
    val backgroundColor = if (isLastWatched) {
        Color(0xFFEAF4FF) // #eaf4ff
    } else if (isHovered) {
        Color(0xFFF4FAFF) // #f4faff
    } else {
        Color.Transparent
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 8.dp, vertical = 8.dp)
            .offset(x = hoverTranslationX.dp)
            .scale(hoverScale),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 播放图标
        Box(
            modifier = Modifier
                .size(20.dp)
                .clip(CircleShape)
                .background(
                    if (isHovered) Color(0xFF1D4ED8) else Color(0xFF3B82F6)
                )
                .scale(playIconScale),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "播放",
                tint = Color.White,
                modifier = Modifier.size(8.dp)
            )
        }

        // 视频内容区域
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(0.dp)
        ) {
            // 标题行
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = title,
                    fontSize = 13.sp,
                    lineHeight = 16.sp,
                    color = if (isHovered) Color(0xFF2563EB) else Color(0xFF4A5568),
                    fontWeight = FontWeight.Normal,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                // 缓存状态指示器
                if (cacheStatus.isNotEmpty()) {
                    CacheStatusIndicator(
                        status = cacheStatus,
                        modifier = Modifier
                    )
                }
            }

            // 进度行（仅已购买状态显示）
            if (isPurchased) {
                Spacer(modifier = Modifier.height(2.dp))
                
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(0.dp)
                ) {
                    // 进度条
                    LinearProgressBar(
                        progress = progress / 100f,
                        size = ProgressBarSize.SMALL,
                        animated = progress > 0,
                        modifier = Modifier.weight(1f)
                    )
                    
                    // 进度信息
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(2.dp),
                        modifier = Modifier
                            .padding(end = 10.dp)
                            .widthIn(min = 80.dp)
                    ) {
                        // 进度百分比
                        Text(
                            text = "${progress.toInt()}%",
                            fontSize = 12.sp,
                            color = Color(0xFF6B7280),
                            fontWeight = FontWeight.Normal
                        )
                        
                        // 观看次数徽章
                        BadgeComponent(
                            count = watchCount,
                            style = BadgeStyle.ORIGINAL
                        )
                    }
                }
            }
        }

        // 锁定图标（仅未购买状态显示）
        if (!isPurchased) {
            Icon(
                imageVector = Icons.Default.Lock,
                contentDescription = "未购买",
                tint = Color(0xFFF59E0B), // #F59E0B
                modifier = Modifier
                    .size(12.dp)
                    .padding(end = 10.dp)
            )
        }
    }
}

/**
 * 缓存状态指示器
 * 基于原型中的缓存状态显示逻辑
 */
@Composable
private fun CacheStatusIndicator(
    status: String,
    modifier: Modifier = Modifier
) {
    val (statusText, statusColor) = when {
        status.contains("%") -> {
            // 下载中状态
            status to Color(0xFF3B82F6) // 蓝色
        }
        status == "已缓存" -> {
            // 已缓存状态
            "已缓存" to Color(0xFF10B981) // 绿色
        }
        else -> {
            status to Color(0xFF6B7280) // 灰色
        }
    }
    
    Text(
        text = statusText,
        fontSize = 10.sp,
        color = statusColor,
        fontWeight = FontWeight.Medium,
        modifier = modifier
            .background(
                color = statusColor.copy(alpha = 0.1f),
                shape = RoundedCornerShape(4.dp)
            )
            .padding(horizontal = 4.dp, vertical = 2.dp)
    )
}

/**
 * 视频项预览组合
 */
@Composable
fun VideoItemPreview() {
    ShuimuTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 已购买状态 - 有进度
            VideoItem(
                title = "Kotlin基础语法详解",
                category = "Kotlin入门",
                progress = 75f,
                watchCount = 12,
                cacheStatus = "已缓存",
                isPurchased = true,
                isLastWatched = false
            )
            
            // 已购买状态 - 最后观看
            VideoItem(
                title = "Android Jetpack Compose实战",
                category = "Android开发",
                progress = 45f,
                watchCount = 8,
                cacheStatus = "50%",
                isPurchased = true,
                isLastWatched = true
            )
            
            // 未购买状态
            VideoItem(
                title = "高级架构设计模式",
                category = "架构设计",
                isPurchased = false
            )
        }
    }
} 