package com.shuimu.videocourse.domain.di;

import com.shuimu.videocourse.domain.repository.CourseRepository;
import com.shuimu.videocourse.domain.usecase.course.GetCourseDetailUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DomainModule_ProvideGetCourseDetailUseCaseFactory implements Factory<GetCourseDetailUseCase> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public DomainModule_ProvideGetCourseDetailUseCaseFactory(
      Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public GetCourseDetailUseCase get() {
    return provideGetCourseDetailUseCase(courseRepositoryProvider.get());
  }

  public static DomainModule_ProvideGetCourseDetailUseCaseFactory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new DomainModule_ProvideGetCourseDetailUseCaseFactory(courseRepositoryProvider);
  }

  public static GetCourseDetailUseCase provideGetCourseDetailUseCase(
      CourseRepository courseRepository) {
    return Preconditions.checkNotNullFromProvides(DomainModule.INSTANCE.provideGetCourseDetailUseCase(courseRepository));
  }
}
