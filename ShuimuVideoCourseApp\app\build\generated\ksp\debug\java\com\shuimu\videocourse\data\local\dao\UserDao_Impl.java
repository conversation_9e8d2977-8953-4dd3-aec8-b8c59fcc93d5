package com.shuimu.videocourse.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.shuimu.videocourse.data.local.entity.UserEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class UserDao_Impl implements UserDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserEntity> __insertionAdapterOfUserEntity;

  private final EntityDeletionOrUpdateAdapter<UserEntity> __deletionAdapterOfUserEntity;

  private final EntityDeletionOrUpdateAdapter<UserEntity> __updateAdapterOfUserEntity;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLoginTime;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserStats;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserLevel;

  private final SharedSQLiteStatement __preparedStmtOfUpdateVipStatus;

  private final SharedSQLiteStatement __preparedStmtOfClearAllUsers;

  public UserDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserEntity = new EntityInsertionAdapter<UserEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `users` (`userId`,`username`,`email`,`phone`,`avatar`,`nickname`,`level`,`experience`,`totalWatchTime`,`totalCourses`,`completedCourses`,`shareEarnings`,`isVip`,`vipExpireTime`,`loginTime`,`createTime`,`updateTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserEntity entity) {
        statement.bindString(1, entity.getUserId());
        statement.bindString(2, entity.getUsername());
        if (entity.getEmail() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getEmail());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getPhone());
        }
        if (entity.getAvatar() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAvatar());
        }
        if (entity.getNickname() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getNickname());
        }
        statement.bindLong(7, entity.getLevel());
        statement.bindLong(8, entity.getExperience());
        statement.bindLong(9, entity.getTotalWatchTime());
        statement.bindLong(10, entity.getTotalCourses());
        statement.bindLong(11, entity.getCompletedCourses());
        statement.bindDouble(12, entity.getShareEarnings());
        final int _tmp = entity.isVip() ? 1 : 0;
        statement.bindLong(13, _tmp);
        if (entity.getVipExpireTime() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getVipExpireTime());
        }
        statement.bindLong(15, entity.getLoginTime());
        statement.bindLong(16, entity.getCreateTime());
        statement.bindLong(17, entity.getUpdateTime());
      }
    };
    this.__deletionAdapterOfUserEntity = new EntityDeletionOrUpdateAdapter<UserEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `users` WHERE `userId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserEntity entity) {
        statement.bindString(1, entity.getUserId());
      }
    };
    this.__updateAdapterOfUserEntity = new EntityDeletionOrUpdateAdapter<UserEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `users` SET `userId` = ?,`username` = ?,`email` = ?,`phone` = ?,`avatar` = ?,`nickname` = ?,`level` = ?,`experience` = ?,`totalWatchTime` = ?,`totalCourses` = ?,`completedCourses` = ?,`shareEarnings` = ?,`isVip` = ?,`vipExpireTime` = ?,`loginTime` = ?,`createTime` = ?,`updateTime` = ? WHERE `userId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserEntity entity) {
        statement.bindString(1, entity.getUserId());
        statement.bindString(2, entity.getUsername());
        if (entity.getEmail() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getEmail());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getPhone());
        }
        if (entity.getAvatar() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAvatar());
        }
        if (entity.getNickname() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getNickname());
        }
        statement.bindLong(7, entity.getLevel());
        statement.bindLong(8, entity.getExperience());
        statement.bindLong(9, entity.getTotalWatchTime());
        statement.bindLong(10, entity.getTotalCourses());
        statement.bindLong(11, entity.getCompletedCourses());
        statement.bindDouble(12, entity.getShareEarnings());
        final int _tmp = entity.isVip() ? 1 : 0;
        statement.bindLong(13, _tmp);
        if (entity.getVipExpireTime() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getVipExpireTime());
        }
        statement.bindLong(15, entity.getLoginTime());
        statement.bindLong(16, entity.getCreateTime());
        statement.bindLong(17, entity.getUpdateTime());
        statement.bindString(18, entity.getUserId());
      }
    };
    this.__preparedStmtOfUpdateLoginTime = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE users SET loginTime = ?, updateTime = ? WHERE userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserStats = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE users SET \n"
                + "        totalWatchTime = ?,\n"
                + "        totalCourses = ?,\n"
                + "        completedCourses = ?,\n"
                + "        updateTime = ?\n"
                + "        WHERE userId = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserLevel = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE users SET level = ?, experience = ?, updateTime = ? WHERE userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateVipStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE users SET isVip = ?, vipExpireTime = ?, updateTime = ? WHERE userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAllUsers = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM users";
        return _query;
      }
    };
  }

  @Override
  public Object insertUser(final UserEntity user, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfUserEntity.insert(user);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteUser(final UserEntity user, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfUserEntity.handle(user);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUser(final UserEntity user, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfUserEntity.handle(user);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLoginTime(final String userId, final long loginTime, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLoginTime.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, loginTime);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 3;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLoginTime.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserStats(final String userId, final long totalWatchTime,
      final int totalCourses, final int completedCourses, final long updateTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserStats.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, totalWatchTime);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, totalCourses);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, completedCourses);
        _argIndex = 4;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 5;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateUserStats.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserLevel(final String userId, final int level, final int experience,
      final long updateTime, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserLevel.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, level);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, experience);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 4;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateUserLevel.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateVipStatus(final String userId, final boolean isVip, final Long vipExpireTime,
      final long updateTime, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateVipStatus.acquire();
        int _argIndex = 1;
        final int _tmp = isVip ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        if (vipExpireTime == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, vipExpireTime);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, updateTime);
        _argIndex = 4;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateVipStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAllUsers(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllUsers.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllUsers.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserById(final String userId,
      final Continuation<? super UserEntity> $completion) {
    final String _sql = "SELECT * FROM users WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserEntity>() {
      @Override
      @Nullable
      public UserEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "avatar");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "level");
          final int _cursorIndexOfExperience = CursorUtil.getColumnIndexOrThrow(_cursor, "experience");
          final int _cursorIndexOfTotalWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWatchTime");
          final int _cursorIndexOfTotalCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCourses");
          final int _cursorIndexOfCompletedCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "completedCourses");
          final int _cursorIndexOfShareEarnings = CursorUtil.getColumnIndexOrThrow(_cursor, "shareEarnings");
          final int _cursorIndexOfIsVip = CursorUtil.getColumnIndexOrThrow(_cursor, "isVip");
          final int _cursorIndexOfVipExpireTime = CursorUtil.getColumnIndexOrThrow(_cursor, "vipExpireTime");
          final int _cursorIndexOfLoginTime = CursorUtil.getColumnIndexOrThrow(_cursor, "loginTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final UserEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpUsername;
            _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAvatar;
            if (_cursor.isNull(_cursorIndexOfAvatar)) {
              _tmpAvatar = null;
            } else {
              _tmpAvatar = _cursor.getString(_cursorIndexOfAvatar);
            }
            final String _tmpNickname;
            if (_cursor.isNull(_cursorIndexOfNickname)) {
              _tmpNickname = null;
            } else {
              _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            }
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpExperience;
            _tmpExperience = _cursor.getInt(_cursorIndexOfExperience);
            final long _tmpTotalWatchTime;
            _tmpTotalWatchTime = _cursor.getLong(_cursorIndexOfTotalWatchTime);
            final int _tmpTotalCourses;
            _tmpTotalCourses = _cursor.getInt(_cursorIndexOfTotalCourses);
            final int _tmpCompletedCourses;
            _tmpCompletedCourses = _cursor.getInt(_cursorIndexOfCompletedCourses);
            final double _tmpShareEarnings;
            _tmpShareEarnings = _cursor.getDouble(_cursorIndexOfShareEarnings);
            final boolean _tmpIsVip;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVip);
            _tmpIsVip = _tmp != 0;
            final Long _tmpVipExpireTime;
            if (_cursor.isNull(_cursorIndexOfVipExpireTime)) {
              _tmpVipExpireTime = null;
            } else {
              _tmpVipExpireTime = _cursor.getLong(_cursorIndexOfVipExpireTime);
            }
            final long _tmpLoginTime;
            _tmpLoginTime = _cursor.getLong(_cursorIndexOfLoginTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new UserEntity(_tmpUserId,_tmpUsername,_tmpEmail,_tmpPhone,_tmpAvatar,_tmpNickname,_tmpLevel,_tmpExperience,_tmpTotalWatchTime,_tmpTotalCourses,_tmpCompletedCourses,_tmpShareEarnings,_tmpIsVip,_tmpVipExpireTime,_tmpLoginTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserByUsername(final String username,
      final Continuation<? super UserEntity> $completion) {
    final String _sql = "SELECT * FROM users WHERE username = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, username);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserEntity>() {
      @Override
      @Nullable
      public UserEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "avatar");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "level");
          final int _cursorIndexOfExperience = CursorUtil.getColumnIndexOrThrow(_cursor, "experience");
          final int _cursorIndexOfTotalWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWatchTime");
          final int _cursorIndexOfTotalCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCourses");
          final int _cursorIndexOfCompletedCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "completedCourses");
          final int _cursorIndexOfShareEarnings = CursorUtil.getColumnIndexOrThrow(_cursor, "shareEarnings");
          final int _cursorIndexOfIsVip = CursorUtil.getColumnIndexOrThrow(_cursor, "isVip");
          final int _cursorIndexOfVipExpireTime = CursorUtil.getColumnIndexOrThrow(_cursor, "vipExpireTime");
          final int _cursorIndexOfLoginTime = CursorUtil.getColumnIndexOrThrow(_cursor, "loginTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final UserEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpUsername;
            _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAvatar;
            if (_cursor.isNull(_cursorIndexOfAvatar)) {
              _tmpAvatar = null;
            } else {
              _tmpAvatar = _cursor.getString(_cursorIndexOfAvatar);
            }
            final String _tmpNickname;
            if (_cursor.isNull(_cursorIndexOfNickname)) {
              _tmpNickname = null;
            } else {
              _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            }
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpExperience;
            _tmpExperience = _cursor.getInt(_cursorIndexOfExperience);
            final long _tmpTotalWatchTime;
            _tmpTotalWatchTime = _cursor.getLong(_cursorIndexOfTotalWatchTime);
            final int _tmpTotalCourses;
            _tmpTotalCourses = _cursor.getInt(_cursorIndexOfTotalCourses);
            final int _tmpCompletedCourses;
            _tmpCompletedCourses = _cursor.getInt(_cursorIndexOfCompletedCourses);
            final double _tmpShareEarnings;
            _tmpShareEarnings = _cursor.getDouble(_cursorIndexOfShareEarnings);
            final boolean _tmpIsVip;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVip);
            _tmpIsVip = _tmp != 0;
            final Long _tmpVipExpireTime;
            if (_cursor.isNull(_cursorIndexOfVipExpireTime)) {
              _tmpVipExpireTime = null;
            } else {
              _tmpVipExpireTime = _cursor.getLong(_cursorIndexOfVipExpireTime);
            }
            final long _tmpLoginTime;
            _tmpLoginTime = _cursor.getLong(_cursorIndexOfLoginTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new UserEntity(_tmpUserId,_tmpUsername,_tmpEmail,_tmpPhone,_tmpAvatar,_tmpNickname,_tmpLevel,_tmpExperience,_tmpTotalWatchTime,_tmpTotalCourses,_tmpCompletedCourses,_tmpShareEarnings,_tmpIsVip,_tmpVipExpireTime,_tmpLoginTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserByEmail(final String email,
      final Continuation<? super UserEntity> $completion) {
    final String _sql = "SELECT * FROM users WHERE email = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, email);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserEntity>() {
      @Override
      @Nullable
      public UserEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "avatar");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "level");
          final int _cursorIndexOfExperience = CursorUtil.getColumnIndexOrThrow(_cursor, "experience");
          final int _cursorIndexOfTotalWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWatchTime");
          final int _cursorIndexOfTotalCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCourses");
          final int _cursorIndexOfCompletedCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "completedCourses");
          final int _cursorIndexOfShareEarnings = CursorUtil.getColumnIndexOrThrow(_cursor, "shareEarnings");
          final int _cursorIndexOfIsVip = CursorUtil.getColumnIndexOrThrow(_cursor, "isVip");
          final int _cursorIndexOfVipExpireTime = CursorUtil.getColumnIndexOrThrow(_cursor, "vipExpireTime");
          final int _cursorIndexOfLoginTime = CursorUtil.getColumnIndexOrThrow(_cursor, "loginTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final UserEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpUsername;
            _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAvatar;
            if (_cursor.isNull(_cursorIndexOfAvatar)) {
              _tmpAvatar = null;
            } else {
              _tmpAvatar = _cursor.getString(_cursorIndexOfAvatar);
            }
            final String _tmpNickname;
            if (_cursor.isNull(_cursorIndexOfNickname)) {
              _tmpNickname = null;
            } else {
              _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            }
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpExperience;
            _tmpExperience = _cursor.getInt(_cursorIndexOfExperience);
            final long _tmpTotalWatchTime;
            _tmpTotalWatchTime = _cursor.getLong(_cursorIndexOfTotalWatchTime);
            final int _tmpTotalCourses;
            _tmpTotalCourses = _cursor.getInt(_cursorIndexOfTotalCourses);
            final int _tmpCompletedCourses;
            _tmpCompletedCourses = _cursor.getInt(_cursorIndexOfCompletedCourses);
            final double _tmpShareEarnings;
            _tmpShareEarnings = _cursor.getDouble(_cursorIndexOfShareEarnings);
            final boolean _tmpIsVip;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVip);
            _tmpIsVip = _tmp != 0;
            final Long _tmpVipExpireTime;
            if (_cursor.isNull(_cursorIndexOfVipExpireTime)) {
              _tmpVipExpireTime = null;
            } else {
              _tmpVipExpireTime = _cursor.getLong(_cursorIndexOfVipExpireTime);
            }
            final long _tmpLoginTime;
            _tmpLoginTime = _cursor.getLong(_cursorIndexOfLoginTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new UserEntity(_tmpUserId,_tmpUsername,_tmpEmail,_tmpPhone,_tmpAvatar,_tmpNickname,_tmpLevel,_tmpExperience,_tmpTotalWatchTime,_tmpTotalCourses,_tmpCompletedCourses,_tmpShareEarnings,_tmpIsVip,_tmpVipExpireTime,_tmpLoginTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCurrentUser(final Continuation<? super UserEntity> $completion) {
    final String _sql = "SELECT * FROM users ORDER BY loginTime DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserEntity>() {
      @Override
      @Nullable
      public UserEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "avatar");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "level");
          final int _cursorIndexOfExperience = CursorUtil.getColumnIndexOrThrow(_cursor, "experience");
          final int _cursorIndexOfTotalWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWatchTime");
          final int _cursorIndexOfTotalCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCourses");
          final int _cursorIndexOfCompletedCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "completedCourses");
          final int _cursorIndexOfShareEarnings = CursorUtil.getColumnIndexOrThrow(_cursor, "shareEarnings");
          final int _cursorIndexOfIsVip = CursorUtil.getColumnIndexOrThrow(_cursor, "isVip");
          final int _cursorIndexOfVipExpireTime = CursorUtil.getColumnIndexOrThrow(_cursor, "vipExpireTime");
          final int _cursorIndexOfLoginTime = CursorUtil.getColumnIndexOrThrow(_cursor, "loginTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final UserEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpUsername;
            _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAvatar;
            if (_cursor.isNull(_cursorIndexOfAvatar)) {
              _tmpAvatar = null;
            } else {
              _tmpAvatar = _cursor.getString(_cursorIndexOfAvatar);
            }
            final String _tmpNickname;
            if (_cursor.isNull(_cursorIndexOfNickname)) {
              _tmpNickname = null;
            } else {
              _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            }
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpExperience;
            _tmpExperience = _cursor.getInt(_cursorIndexOfExperience);
            final long _tmpTotalWatchTime;
            _tmpTotalWatchTime = _cursor.getLong(_cursorIndexOfTotalWatchTime);
            final int _tmpTotalCourses;
            _tmpTotalCourses = _cursor.getInt(_cursorIndexOfTotalCourses);
            final int _tmpCompletedCourses;
            _tmpCompletedCourses = _cursor.getInt(_cursorIndexOfCompletedCourses);
            final double _tmpShareEarnings;
            _tmpShareEarnings = _cursor.getDouble(_cursorIndexOfShareEarnings);
            final boolean _tmpIsVip;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVip);
            _tmpIsVip = _tmp != 0;
            final Long _tmpVipExpireTime;
            if (_cursor.isNull(_cursorIndexOfVipExpireTime)) {
              _tmpVipExpireTime = null;
            } else {
              _tmpVipExpireTime = _cursor.getLong(_cursorIndexOfVipExpireTime);
            }
            final long _tmpLoginTime;
            _tmpLoginTime = _cursor.getLong(_cursorIndexOfLoginTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new UserEntity(_tmpUserId,_tmpUsername,_tmpEmail,_tmpPhone,_tmpAvatar,_tmpNickname,_tmpLevel,_tmpExperience,_tmpTotalWatchTime,_tmpTotalCourses,_tmpCompletedCourses,_tmpShareEarnings,_tmpIsVip,_tmpVipExpireTime,_tmpLoginTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<UserEntity> observeCurrentUser() {
    final String _sql = "SELECT * FROM users ORDER BY loginTime DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"users"}, new Callable<UserEntity>() {
      @Override
      @Nullable
      public UserEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "avatar");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "level");
          final int _cursorIndexOfExperience = CursorUtil.getColumnIndexOrThrow(_cursor, "experience");
          final int _cursorIndexOfTotalWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWatchTime");
          final int _cursorIndexOfTotalCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCourses");
          final int _cursorIndexOfCompletedCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "completedCourses");
          final int _cursorIndexOfShareEarnings = CursorUtil.getColumnIndexOrThrow(_cursor, "shareEarnings");
          final int _cursorIndexOfIsVip = CursorUtil.getColumnIndexOrThrow(_cursor, "isVip");
          final int _cursorIndexOfVipExpireTime = CursorUtil.getColumnIndexOrThrow(_cursor, "vipExpireTime");
          final int _cursorIndexOfLoginTime = CursorUtil.getColumnIndexOrThrow(_cursor, "loginTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final UserEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpUsername;
            _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAvatar;
            if (_cursor.isNull(_cursorIndexOfAvatar)) {
              _tmpAvatar = null;
            } else {
              _tmpAvatar = _cursor.getString(_cursorIndexOfAvatar);
            }
            final String _tmpNickname;
            if (_cursor.isNull(_cursorIndexOfNickname)) {
              _tmpNickname = null;
            } else {
              _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            }
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpExperience;
            _tmpExperience = _cursor.getInt(_cursorIndexOfExperience);
            final long _tmpTotalWatchTime;
            _tmpTotalWatchTime = _cursor.getLong(_cursorIndexOfTotalWatchTime);
            final int _tmpTotalCourses;
            _tmpTotalCourses = _cursor.getInt(_cursorIndexOfTotalCourses);
            final int _tmpCompletedCourses;
            _tmpCompletedCourses = _cursor.getInt(_cursorIndexOfCompletedCourses);
            final double _tmpShareEarnings;
            _tmpShareEarnings = _cursor.getDouble(_cursorIndexOfShareEarnings);
            final boolean _tmpIsVip;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVip);
            _tmpIsVip = _tmp != 0;
            final Long _tmpVipExpireTime;
            if (_cursor.isNull(_cursorIndexOfVipExpireTime)) {
              _tmpVipExpireTime = null;
            } else {
              _tmpVipExpireTime = _cursor.getLong(_cursorIndexOfVipExpireTime);
            }
            final long _tmpLoginTime;
            _tmpLoginTime = _cursor.getLong(_cursorIndexOfLoginTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _result = new UserEntity(_tmpUserId,_tmpUsername,_tmpEmail,_tmpPhone,_tmpAvatar,_tmpNickname,_tmpLevel,_tmpExperience,_tmpTotalWatchTime,_tmpTotalCourses,_tmpCompletedCourses,_tmpShareEarnings,_tmpIsVip,_tmpVipExpireTime,_tmpLoginTime,_tmpCreateTime,_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllUsers(final Continuation<? super List<UserEntity>> $completion) {
    final String _sql = "SELECT * FROM users ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserEntity>>() {
      @Override
      @NonNull
      public List<UserEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAvatar = CursorUtil.getColumnIndexOrThrow(_cursor, "avatar");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "level");
          final int _cursorIndexOfExperience = CursorUtil.getColumnIndexOrThrow(_cursor, "experience");
          final int _cursorIndexOfTotalWatchTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWatchTime");
          final int _cursorIndexOfTotalCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCourses");
          final int _cursorIndexOfCompletedCourses = CursorUtil.getColumnIndexOrThrow(_cursor, "completedCourses");
          final int _cursorIndexOfShareEarnings = CursorUtil.getColumnIndexOrThrow(_cursor, "shareEarnings");
          final int _cursorIndexOfIsVip = CursorUtil.getColumnIndexOrThrow(_cursor, "isVip");
          final int _cursorIndexOfVipExpireTime = CursorUtil.getColumnIndexOrThrow(_cursor, "vipExpireTime");
          final int _cursorIndexOfLoginTime = CursorUtil.getColumnIndexOrThrow(_cursor, "loginTime");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<UserEntity> _result = new ArrayList<UserEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserEntity _item;
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final String _tmpUsername;
            _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAvatar;
            if (_cursor.isNull(_cursorIndexOfAvatar)) {
              _tmpAvatar = null;
            } else {
              _tmpAvatar = _cursor.getString(_cursorIndexOfAvatar);
            }
            final String _tmpNickname;
            if (_cursor.isNull(_cursorIndexOfNickname)) {
              _tmpNickname = null;
            } else {
              _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            }
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpExperience;
            _tmpExperience = _cursor.getInt(_cursorIndexOfExperience);
            final long _tmpTotalWatchTime;
            _tmpTotalWatchTime = _cursor.getLong(_cursorIndexOfTotalWatchTime);
            final int _tmpTotalCourses;
            _tmpTotalCourses = _cursor.getInt(_cursorIndexOfTotalCourses);
            final int _tmpCompletedCourses;
            _tmpCompletedCourses = _cursor.getInt(_cursorIndexOfCompletedCourses);
            final double _tmpShareEarnings;
            _tmpShareEarnings = _cursor.getDouble(_cursorIndexOfShareEarnings);
            final boolean _tmpIsVip;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVip);
            _tmpIsVip = _tmp != 0;
            final Long _tmpVipExpireTime;
            if (_cursor.isNull(_cursorIndexOfVipExpireTime)) {
              _tmpVipExpireTime = null;
            } else {
              _tmpVipExpireTime = _cursor.getLong(_cursorIndexOfVipExpireTime);
            }
            final long _tmpLoginTime;
            _tmpLoginTime = _cursor.getLong(_cursorIndexOfLoginTime);
            final long _tmpCreateTime;
            _tmpCreateTime = _cursor.getLong(_cursorIndexOfCreateTime);
            final long _tmpUpdateTime;
            _tmpUpdateTime = _cursor.getLong(_cursorIndexOfUpdateTime);
            _item = new UserEntity(_tmpUserId,_tmpUsername,_tmpEmail,_tmpPhone,_tmpAvatar,_tmpNickname,_tmpLevel,_tmpExperience,_tmpTotalWatchTime,_tmpTotalCourses,_tmpCompletedCourses,_tmpShareEarnings,_tmpIsVip,_tmpVipExpireTime,_tmpLoginTime,_tmpCreateTime,_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
