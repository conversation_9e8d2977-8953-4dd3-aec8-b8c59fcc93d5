package com.shuimu.videocourse.presentation.components.status

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.cos
import kotlin.math.sin

/**
 * 加载指示器组件
 * 
 * 基于原型设计的加载指示器
 * - 支持加载动画效果和多种加载样式
 * - 实现文本提示支持和主题色适配
 * - 添加圆形、线性等样式变体
 * - 使用#667eea紫蓝色主题
 * 
 * @param isLoading 是否显示加载状态
 * @param loadingText 加载文本提示
 * @param style 加载样式
 * @param size 加载指示器尺寸
 * @param color 主题色
 * @param modifier 修饰符
 */
@Composable
fun LoadingIndicator(
    isLoading: Boolean = true,
    loadingText: String = "加载中...",
    style: LoadingStyle = LoadingStyle.Circular,
    size: Dp = 40.dp,
    color: Color = Color(0xFF667eea),
    modifier: Modifier = Modifier
) {
    if (!isLoading) return
    
    when (style) {
        LoadingStyle.Circular -> {
            CircularLoadingIndicator(
                loadingText = loadingText,
                size = size,
                color = color,
                modifier = modifier
            )
        }
        LoadingStyle.Linear -> {
            LinearLoadingIndicator(
                loadingText = loadingText,
                color = color,
                modifier = modifier
            )
        }
        LoadingStyle.Dots -> {
            DotsLoadingIndicator(
                loadingText = loadingText,
                color = color,
                modifier = modifier
            )
        }
        LoadingStyle.Pulse -> {
            PulseLoadingIndicator(
                loadingText = loadingText,
                size = size,
                color = color,
                modifier = modifier
            )
        }
    }
}

/**
 * 圆形加载指示器
 */
@Composable
private fun CircularLoadingIndicator(
    loadingText: String,
    size: Dp,
    color: Color,
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "circular_loading")
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Canvas(
            modifier = Modifier
                .size(size)
                .rotate(rotation)
        ) {
            drawCircularProgress(color)
        }
        
        if (loadingText.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = loadingText,
                fontSize = 14.sp,
                color = Color(0xFF6b7280),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 线性加载指示器
 */
@Composable
private fun LinearLoadingIndicator(
    loadingText: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "linear_loading")
    val progress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "progress"
    )
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        LinearProgressIndicator(
            progress = progress,
            modifier = Modifier
                .fillMaxWidth()
                .height(4.dp)
                .clip(RoundedCornerShape(2.dp)),
            color = color,
            trackColor = color.copy(alpha = 0.2f)
        )
        
        if (loadingText.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = loadingText,
                fontSize = 14.sp,
                color = Color(0xFF6b7280),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 点状加载指示器
 */
@Composable
private fun DotsLoadingIndicator(
    loadingText: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "dots_loading")
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            repeat(3) { index ->
                val scale by infiniteTransition.animateFloat(
                    initialValue = 0.5f,
                    targetValue = 1f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(600, delayMillis = index * 200),
                        repeatMode = RepeatMode.Reverse
                    ),
                    label = "dot_scale_$index"
                )
                
                Canvas(
                    modifier = Modifier.size(8.dp)
                ) {
                    drawCircle(
                        color = color,
                        radius = size.minDimension / 2 * scale
                    )
                }
            }
        }
        
        if (loadingText.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = loadingText,
                fontSize = 14.sp,
                color = Color(0xFF6b7280),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 脉冲加载指示器
 */
@Composable
private fun PulseLoadingIndicator(
    loadingText: String,
    size: Dp,
    color: Color,
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "pulse_loading")
    val scale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_scale"
    )
    
    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.5f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_alpha"
    )
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Canvas(
            modifier = Modifier.size(size)
        ) {
            drawCircle(
                color = color.copy(alpha = alpha),
                radius = (size.toPx() / 2) * scale
            )
        }
        
        if (loadingText.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = loadingText,
                fontSize = 14.sp,
                color = Color(0xFF6b7280),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 绘制圆形进度
 */
private fun DrawScope.drawCircularProgress(color: Color) {
    val strokeWidth = 4.dp.toPx()
    val radius = (size.minDimension - strokeWidth) / 2
    val center = Offset(size.width / 2, size.height / 2)
    
    // 绘制背景圆环
    drawCircle(
        color = color.copy(alpha = 0.2f),
        radius = radius,
        center = center,
        style = androidx.compose.ui.graphics.drawscope.Stroke(strokeWidth)
    )
    
    // 绘制进度弧
    val sweepAngle = 90f
    drawArc(
        color = color,
        startAngle = -90f,
        sweepAngle = sweepAngle,
        useCenter = false,
        style = androidx.compose.ui.graphics.drawscope.Stroke(
            width = strokeWidth,
            cap = StrokeCap.Round
        )
    )
}

/**
 * 加载样式枚举
 */
enum class LoadingStyle {
    Circular,  // 圆形
    Linear,    // 线性
    Dots,      // 点状
    Pulse      // 脉冲
} 