package com.shuimu.videocourse.presentation.components.display

import androidx.compose.animation.core.*
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.videocourse.presentation.components.basic.BadgeComponent
import com.shuimu.videocourse.presentation.components.basic.BadgeStyle
import com.shuimu.videocourse.presentation.components.basic.LinearProgressBar
import com.shuimu.videocourse.presentation.components.basic.ProgressBarSize
import com.shuimu.videocourse.presentation.theme.ShuimuTheme

/**
 * 分类卡片组件
 * 
 * 基于原型设计：UI Prototype/data/component-templates-with-styles.js 第520-580行
 * createPurchasedCategoryCard 和 createUnpurchasedCategoryCard 函数
 */

data class VideoItemData(
    val title: String,
    val category: String = "",
    val progress: Float = 0f,
    val watchCount: Int = 0,
    val cacheStatus: String = "",
    val isPurchased: Boolean = true,
    val isLastWatched: Boolean = false
)

@Composable
fun CategoryCard(
    title: String,
    progress: Float = 0f,
    watchCount: Int = 0,
    price: String = "",
    videos: List<VideoItemData> = emptyList(),
    isExpanded: Boolean = false,
    onExpandToggle: () -> Unit = {},
    onVideoClick: (VideoItemData) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val isPurchased = price.isEmpty()
    
    val arrowRotation by animateFloatAsState(
        targetValue = if (isExpanded) 180f else 0f,
        animationSpec = tween(300)
    )

    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // 头部
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onExpandToggle() }
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF374151),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f, fill = false)
                    )
                    
                    if (price.isNotEmpty()) {
                        Text(
                            text = "($price)",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Normal,
                            color = Color(0xFF6B7280)
                        )
                    }
                }

                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = if (isExpanded) "收起" else "展开",
                    tint = Color(0xFF6B7280),
                    modifier = Modifier
                        .size(20.dp)
                        .rotate(arrowRotation)
                )
            }

            // 进度条（已购买状态）
            if (isPurchased) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    LinearProgressBar(
                        progress = progress / 100f,
                        size = ProgressBarSize.MEDIUM,
                        animated = progress > 0,
                        modifier = Modifier.weight(1f)
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(2.dp),
                        modifier = Modifier.padding(end = 10.dp)
                    ) {
                        Text(
                            text = "${progress.toInt()}%",
                            fontSize = 12.sp,
                            color = Color(0xFF6B7280)
                        )
                        
                        BadgeComponent(
                            count = watchCount,
                            style = BadgeStyle.ORIGINAL
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
            }

            // 视频列表
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically(animationSpec = tween(300)),
                exit = shrinkVertically(animationSpec = tween(300))
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 8.dp, vertical = 8.dp)
                        .heightIn(max = 300.dp)
                ) {
                    videos.forEach { video ->
                        VideoItem(
                            title = video.title,
                            category = video.category,
                            progress = video.progress,
                            watchCount = video.watchCount,
                            cacheStatus = video.cacheStatus,
                            isPurchased = video.isPurchased,
                            isLastWatched = video.isLastWatched,
                            onClick = { onVideoClick(video) }
                        )
                        
                        if (video != videos.last()) {
                            Divider(
                                color = Color(0xFFE2E8F0),
                                thickness = 1.dp,
                                modifier = Modifier.padding(start = 28.dp)
                            )
                        }
                    }
                }
            }
        }
    }
} 